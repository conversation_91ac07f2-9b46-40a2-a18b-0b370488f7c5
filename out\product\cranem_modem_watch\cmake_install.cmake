# Install script for directory: D:/Project_area/SVN/7_kxs52_spain/product/cranem_modem

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/crane_modem")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "RelWithDebInfo")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/zlib/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/libpng/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/libjpeg/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/libqrencode/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/code128/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/lvgl/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/lv_drivers/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/xf_drv/cmake_install.cmake")
  include("D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/ril/cmake_install.cmake")

endif()

if(CMAKE_INSTALL_COMPONENT)
  set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
file(WRITE "D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
