The target system is: Generic -  - arm
The host system is: Windows - 10.0.22631 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armcc [4d3604]
For Educational purposes only
Software supplied by: ARM Limited

Usage:         armcc [options] file1 file2 ... filen
Main options:
        
--arm          Generate ARM code
--thumb        Generate Thumb code
--c90          Switch to C mode (default for .c files)
--cpp          Switch to C++ mode (default for .cpp files)
-O0            Minimum optimization
-O1            Restricted optimization for debugging
-O2            High optimization
-O3            Maximum optimization
-Ospace        Optimize for codesize
-Otime         Optimize for maximum performance
--cpu <cpu>    Select CPU to generate code for
--cpu list     Output a list of all the selectable CPUs
-o <file>      Name the final output file of the compilation
-c             Compile only, do not link
--asm          Output assembly code as well as object code
-S             Output assembly code instead of object code
--interleave   Interleave source with disassembly (use with --asm or -S)
-E             Preprocess the C source code only
-D<symbol>     Define <symbol> on entry to the compiler
-g             Generate tables for high-level debugging
-I<directory>  Include <directory> on the #include search path
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_25232 
[1/2] Building C object CMakeFiles\cmTC_25232.dir\testCCompiler.o

[2/2] Linking C static library libcmTC_25232.a



Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_61591 
[1/2] Building C object CMakeFiles\cmTC_61591.dir\CMakeCCompilerABI.o

[2/2] Linking C static library libcmTC_61591.a



Determining if the CXX compiler works passed with the following output:
Change Dir: D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_c24ef 
[1/2] Building CXX object CMakeFiles\cmTC_c24ef.dir\testCXXCompiler.o

[2/2] Linking CXX static library libcmTC_c24ef.a



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_a6231 
[1/2] Building CXX object CMakeFiles\cmTC_a6231.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX static library libcmTC_a6231.a



