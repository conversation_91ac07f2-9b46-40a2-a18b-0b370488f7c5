#include "lv_watch_conf.h"
#include "lv_watch.h"
#include "../modem/mmi_modem_adaptor_main.h"
#include "mmi_modem_adaptor_ws.h"
#include "ws_cJSON.h"
#include "../ke/ke_location_flow.h"
#if USW_LV_WATCH_NA_NEW_PROTO == 0
void CWatchService_CheckbeatTimeOutCB(uint32_t pUser);
void CWatchService_HeartbeatTimerCB(uint32_t pUser);
void CWatchService_CommonTimerCB(uint32_t pUser);

//lyj add for merge noscreen and screen
extern int gui_is_lcd_valid(void);

static int s_connect_success_fag = 0;

#if USE_LV_WATCH_DATI
char g_imei[WS_SN_MAX_LEN]={0};
ws_dati_info g_answer_fun;
cur_subject_t cur_answer_result;
#endif

#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
nv_watch_groupList_t ke_group_list;
uint32_t ke_upPosTime;
#endif

#if USE_LV_WATCH_STUDENT_INFO!=0
nv_watch_nh_stuinfo_t nh_userInfo;
int big_icon_record_png_len = 0;
int down_over_flag = 0;
#endif

#if USE_LV_WATCH_YIXIN !=0
	#define WS_URL  "dev.ecplive.cn"
	#define WS_PORT  (10512) //(12000)
	static char sUrl[] = "dev.ecplive.cn";
#elif USE_LV_WATCH_SPAIN != 0
	#define WS_URL  "gw.api-pro-seniordomo.com"
	static char sUrl[80] = "gw.api-pro-seniordomo.com";  
	#define WS_PORT  (8888) 
	char apn_address[100]={0};
#else
	#define WS_URL  "card.kaerplatform.com"
	static char sUrl[80] = "card.kaerplatform.com";  
	#define WS_PORT  (13000) 
#endif

#define APN_CONFIG_FILE "C:/apn_spain.bin"

typedef lv_fs_file_t ui_file_t;
typedef lv_fs_dir_t  ui_dir_t;
#define UI_FILE_READ_ONLY     LV_FS_MODE_RD
#define UI_FILE_WRITE_ONLY    LV_FS_MODE_WR
#define UI_FILE_RD_APPEND     (LV_FS_MODE_RD | LV_FS_MODE_APPEND)
#define UI_FILE_READ_WRITE    (LV_FS_MODE_RD | LV_FS_MODE_WR)
#define UI_FILE_OPEN(p, m)   \
    ({                       \
        ui_file_t *f;        \
        f = lv_mem_alloc(sizeof(ui_file_t)); \
        memset(f,0,sizeof(ui_file_t)); \
        lv_fs_open(f, p, m); \
        (f);                 \
    }                        \
    )

#define UI_FILE_CLOSE(f)      \
    ({                        \
        lv_fs_close(f);       \
        lv_mem_free(f);       \
    }                         \
    )
#define UI_FILE_SEEK          lv_fs_seek
#define UI_FILE_READ(f, b, l)          \
    ({                                 \
        uint32_t Ret_Len;                \
        lv_fs_read(f, b, l, &Ret_Len); \
        (Ret_Len);                     \
    }                                  \
    )

#define UI_FILE_WRITE(f, b, l)          \
    ({                                  \
        uint32_t Ret_Len;                 \
        lv_fs_write(f, b, l, &Ret_Len); \
        (Ret_Len);                      \
    }                                   \
    )

#define UI_FILE_SIZE     lv_fs_size

static int s_up_lbs_flag = 0;

int g_need_update_bak_flag = 0;

#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
#define WS_REGISTER_SERVER "www.kaerplatform.cn"//"card.kaerplatform.com"
#define WS_REGISGER_PORT (12008)//(13002)
#define WS_REGISTER_SERVER_HEAD (0x4050)
#endif

#if USE_LV_WATCH_PULL_CONFIG != 0

#if USE_LV_WATCH_LANGUAGE_SUPPORT_E != 0
#define PULL_CONFIG_URL "https://www.banxiaoxing.com/esidcard/esc/getDevParam"
#elif defined(__XF_PRO_WEILIAO__) && USE_LV_WATCH_KEY_KW19 != 0
#define PULL_CONFIG_URL "https://www.kaerplatform.com/esidcard/esc/getDevParam"
#elif USE_LV_WATCH_KEY_KW19 != 0
#define PULL_CONFIG_URL "https://www.banxiaoxing.com/esidcard/esc/getDevParam"
#else
#define PULL_CONFIG_URL "https://www.kaerplatform.com/esidcard/esc/getDevParam"
#endif
enum {
    KAER_PULL_CONFIG_FAMILYNUM_SOS = 1,
    KAER_PULL_CONFIG_WHITENUM,
    KAER_PULL_CONFIG_MUTE,
    KAER_PULL_CONFIG_PERIOD,
    KAER_PULL_CONFIG_FAMILYNUM,
    KAER_PULL_CONFIG_SOS,
};
uint8_t g_pull_config_type = 0;
static void *g_pull_configTimer=NULL; 
uint8_t g_pull_config=KAER_PULL_CONFIG_WHITENUM;

void Http_pull_config_timer_Create();
#endif

typedef enum
{
	QT_SMS_NONE_INTERCEPT = 1,				
	QT_SMS_WHITELIST_NONE_INTERCEPT = 2,
	QT_SMS_ALL_INTERCEPT = 3,
}QT_SMS_INTERCEPT_TYPE;

#define KAER_PB_TYPE_SOS	2
#define KAER_PB_TYPE_FAMILY	1
#define KAER_PB_TYPE_WHITE	3

#define KAER_ALARM_TYPE_POWER_ON	1
#define KAER_ALARM_TYPE_POWER_OFF	2
#define KAER_ALARM_TYPE_SOS			3
#define KAER_ALARM_TYPE_FENCE		4

#define KAER_LOCATION_TYPE_PERIOD		0   //鍛ㄦ湡瀹氫綅
#define KAER_LOCATION_TYPE_IMMEDIATE	1   //绔嬪嵆瀹氫綅
#define KAER_LOCATION_TYPE_SOS 			KAER_ALARM_TYPE_SOS	//sos瀹氫綅	


#define KAER_MSG_MAX_LEN		200 * 3
#define MAX_SIO_BUFFER_LEN 		1024*6

#define WS_DORM_SECONDS        (3*TICKES_IN_SECOND)

#define KAER_PING_MIN_TIMER		(30)//(4*60) 
#if USE_LV_WATCH_SPAIN == 1
	#define heart_rate_UP_TIMER (10*60)//10
	#define SPO2_UP_TIMER       (60*60)//3600
	#define KAER_PING_MAX_TIMER		(3*60)

	#define  MAX_TEST_TIME (70)
	#define  BLOOD_TESTING_TIME  (45)
	TASK_HANDLE *blood_measure_task_handle = NULL;
	TASK_HANDLE *heart_measure_task_handle = NULL;
	static int blood_measure_flag = 0;
	static int heart_measure_flag = 0;

#else
	#define heart_rate_UP_TIMER (1*60)
	#define SPO2_UP_TIMER       (2*60)
	#define KAER_PING_MAX_TIMER		(5*60)
#endif
 
#define KAER_LOC_UP_TIMER		(10*60) 

#define KAER_MODULES    "L0"
#if USE_LV_WATCH_KEY_KW19 != 0
#define KAER_MODEL_NAME "KXS52"
#else
#define KAER_MODEL_NAME "KW7"
#endif
#define KAER_IDENT        CWatchService_GetDevSeq(pme)
#define KAER_SW_VERION    201

#define WS_FIELD_PK_TYPE    2
#define WS_FIELD_PK_LEN    4
#define WS_FIELD_BODY_LENGTH 2

#define WS_FRIST_READ_LENGTH (WS_FIELD_PK_LEN+WS_FIELD_BODY_LENGTH)  

#define WS_BATT_IND_RANGE (10)

extern int ke_update_flag;

static ws_srv_t    server = {
		   .host = sUrl,          //WS_URL,
		   .port = WS_PORT,
		   .fd = -1,
		   .dormancy = WS_DORM_SECONDS,
	   };
	   
typedef struct _ws_code_map
{
    uint16_t      msgType;
    ws_evt_id_e   cmd;

}ws_code_map;

enum {
    KAER_CMD_INVALID=0,
    KAER_CMD_LOGIN=0x00,
    KAER_CMD_HEART_S=0x03d1,
    KAER_CMD_HEART_T=0x0313,
    KAER_CMD_DT_SYNC=5,    
    KAER_CMD_IND_POS = 0x03e1,
    KAER_CMD_POS = 7,
    KAER_CMD_POS_REQ = 0x03dd,
    KAER_CMD_SMS_UP = 9,
    KAER_CMD_TK_IND = 10,
    KAER_CMD_TK_REQ = 11,
    KAER_CMD_PHB = 0x03d0,
    KAER_CMD_WEATHER = 17,
    KAER_CMD_SOS_LIST = 0x03d0,
    KAER_CMD_BAT_INF = 0x03dc,
    KAER_CMD_VER_INF = 0x03a7,
    KAER_CMD_WHITE_LIST_FLAG = 24,
    KAER_CMD_VOLUME = 26,
    KAER_CMD_STUDY_TIME = 0x03d7,
    KAER_CMD_ALARM_LIST = 30,
    KAER_CMD_WILL_PWROFF = 31,
    KAER_CMD_PWROFF = 0x03d3,
    KAER_CMD_MONITER = 34,
    KAER_CMD_CAPTURE = 36,
    KAER_CMD_TXT_MSG = 38,
    KAER_CMD_POSUP_TIME = 42,
    KAER_CMD_RESET_FACTORY = 0x03d9,
    KAER_CMD_RESET_FACTORY_T = 45,
    KAER_CMD_TOKEN_IND = 46,
    //video call cmd
    KAER_CMD_VCALL_REJECT   = 47, 
    KAER_CMD_VCALL_IN_SHW   = 48, 
    KAER_CMD_VCALL_DURATION = 49, 
    KAER_CMD_VCALL_HANGUP   = 50, 
    KAER_CMD_VCALL_OUT_SHW  = 51, 
    KAER_CMD_VCALL_OUT_FAILED= 52, 
    KAER_CMD_VCALL_CONTACT  = 53, 
    KAER_CMD_FLOWERS = 54,
    KAER_CMD_QUERY_TRAFFIC = 56,
    KAER_CMD_QUERY_TRAFFIC_CONTENT = 57,
    KAER_CMD_VCALL_IN_JPHONE = 58, 
    KAER_CMD_VCALL_OUT_JPHONE= 59, 
    KAER_CMD_MAKE_FRIEND = 71,
    KAER_CMD_FRIENDS_SYNC = 72,
    KAER_CMD_FRIENDS_SYNC_REQ = 73,
    KAER_CMD_DEL_FRIEND = 75,
    KAER_CMD_QUERY_FRIEND_MSG = 77,
    KAER_CMD_FRIEND_MSG_IND = 80,
    KAER_CMD_FIND_WATCH = 86,

    KAER_CMD_ERROR = 100,
    KAER_CMD_VOICE_TEST = 1235,
    KAER_CMD_IND_ALARM = 0x03db,
    KAER_CMD_COURSE=0x03d8,
    KAER_CMD_POWER_ONOFF = 0x03d2,
    KAER_CMD_SET_ONOFF = 0x03d6,
    KAER_CMD_SET_FENCE = 0x03d4,
	KAER_CMD_INIT_PWD = 0x03d5,  
    KAER_CMD_SET_ADDR  = 0x032e,
    KAER_CMD_NEW_TEXT_MSG = 0x03e2,
    KAER_CMD_REBOOT  = 0x0315,
	#if USE_LV_WATCH_SPAIN != 0
	KAER_CMD_POWEROFF = 0x0315,
	KAER_CMD_PLATFORM_ALARM = 0x106A,
	KAER_CMD_TIMEZONE_FORMAT = 0x5000,
	KAER_CMD_FALL_DETECTION = 0x5001,
	KAER_CMD_SHOW_MESSAGE = 0x5002,
	KAER_CMD_START_MEASURE = 0x5003,
	KAER_CMD_SET_LOCATION_CONFIG = 0x5004,
	KAER_CMD_SET_HEALTH_FREQ = 0x5005,
	KAER_CMD_ECO_OPEN=0x5006,//ECO mode
	#endif
    KAER_CMD_UPDATA  = 0x030c,
    KAER_CMD_DOWNLOAD_DATI  = 0x03bb,
    KAER_CMD_UP_DATI  = 0x03bc,
    KAER_CMD_SMS_INTERCEPT = 0x1015,
	KAER_CMD_REPORT_SMS = 0x1016,
	#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
	KAER_CMD_SET_GROUP_PERIOD = 0x1019,
	#endif

	KAER_CMD_SET_GOHOME_WIFI = 0x03EA,
	KAER_CMD_UP_GOHOME_WIFI = 0x03EC,
	KAER_CMD_UP_CALLLOG = 0x0312,
	KAER_CMD_UP_FUNCTION_LIST = 0x1040,
	KAER_CMD_SET_FUNCTION_LIST = 0x1041,
	KAER_CMD_GET_HEBAO_INFO = 0x1042,
	KAER_CMD_SET_HEBAO_START_APDU = 0x1043,
	KAER_CMD_HEBAO_UP_APDU_RSP = 0x1044,
	KAER_CMD_SET_SPORT_PLAN = 0x1045,
	KAER_CMD_UP_JUMP_DATA = 0x1046,
	KAER_CMD_GET_HEBAO_ORDER_LIST = 0x1048,
	KAER_CMD_PUSH_HEBAO_ORDER_LIST = 0x104D,
	KAER_CMD_SET_SLEEP_TIME = 0x104E,
	KAER_CMD_SET_STUDENT_INFO = 0X1050,
	KAER_CMD_SET_MIGU_URL =0x105C,
	KAER_CMD_SET_MIGU_PLAN = 0x1065,
	KAER_CMD_UP_HR_DATA = 0X105E,
	KAER_CMD_UP_RUN_STATUS = 0X105f,
	KAER_CMD_UP_RUN_DATA = 0X1060,
	KAER_CMD_SET_CONFIG = 0x0382,
	KAER_CMD_UP_HEALTH_DATA = 0X1063,
	KAER_CMD_SET_BLACK_MAC = 0x106E,
	KAER_CMD_SET_BLE_MAC = 0X1070,
    KAER_CMD_UNDEF,
	
	#if USE_LV_WATCH_KR_WEILIAO != 0
	KAER_CMD_SET_SPORT_MONITER = 0X105D,
	KAER_CMD_SET_MEDICINE_PLAN = 0x1077,
	KAER_CMD_UP_MEDICINE_STATE = 0x1078,
	KAER_CMD_UP_WEILIAO = 0x1081,
	KAER_CMD_DOWN_WEILIAO = 0x1082,
	KAER_CMD_UP_APPDISPLAY = 0x1072,
	#endif
};

#define STRING_TYPE               "type"
#define STRING_IDENT              "ident"
#define STRING_IMEI               "imei"
#define STRING_IMSI               "imsi"
#define STRING_VERSION            "version"
#define STRING_MODULES            "modules"
#define STRING_HEARTBEAT          "heartbeat"
#define STRING_TOKEN              "token"
#define STRING_HOST               "host"
#define STRING_PORT               "port"
#define STRING_DURATION           "duration"
#define STRING_URL                "url"

#define STRING_TERMINAL           "terminal"
#define STRING_COMMAND            "command"
#define STRING_RSP                "res"
#define STRING_REQ                "req"
#define STRING_RESULT             "result"
#if USE_LV_WATCH_SPAIN != 0
#define TIMEZONE_SCALE           (60)
#define MEASURE_TYPE_HEART    1  
#define MEASURE_TYPE_SPO2     2  
enum {
	ALARM_SUN = 0x01,//0000 0001 
    ALARM_MON = 0x02,//0000 0010  
    ALARM_TUE = 0x04,//0000 0100
    ALARM_WED = 0x08,//0000 1000
    ALARM_THU = 0x10,//0001 0000
    ALARM_FRI = 0x20,//0010 0000
    ALARM_SAT = 0x40,//0100 0000
    ALARM_ONCE = 0x00,//0000 0000
    ALARM_REPEAT = 0x80,//1000 0000
};
enum {
	SET_REPEAT_ONCE = 0,
    SET_WEEK_MON,
    SET_WEEK_TUE,
    SET_WEEK_WED,
    SET_WEEK_THU,
    SET_WEEK_FRI,
    SET_WEEK_SAT,
 	SET_WEEK_SUN,
};
struct {
    uint8_t enabled;
    uint8_t sensitivity;
} g_fall_detection = {0};
uint8_t g_date_format = 1; 
uint8_t g_time_format = 1;
#endif

static ws_contact TempWhiteContact[WS_PHB_MAX] = {0};
static char TempWhiteCnt = 0;
static char sWhiteCntCurrPac = 0;

static void CWatchService_SendJsonData(ws_client *pme,  ws_cJSON* root);
static void CWatchService_SendBatteryInfo(ws_client * pme, ws_batInfo *battery);
static void CWatchService_StartGps(ws_kaer_t *userData);
static void CWatchService_HeartbeatStart(ws_client *pme);
static void CWathcService_SetRedayMode(ws_client * pme, void *json,ws_evt_msg_t  * pEvtMsg);
static int CWatchService_SMS_SetUrl(char *Data);

#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
nv_watch_gohome_wifimac_t g_gohome_mac;
nv_watch_goscene_wifimac_t g_scene_mac;
uint8_t g_serching_mac;
static void CWatchService_SendGohomeLocation(ws_client *pme, LOCATION_UP_TYPE UpType);
extern void ke_common_string_HighToLow(char *high,char *low);
#endif
#if USE_LV_WATCH_VOICE_MSG_YNYD != 0

#define STR_PERSON_GET_TYPE_LIST  "get_type_list"
#define STR_PERSON_GET_QRCODE  "get_person_qrcode"
#define STR_PERSON_PUSH_SCAN_RET  "push_scan_result"
#define STR_PERSON_PSW_REST  "reset_password"
 
 
#define STR_GET_USER_LOGININFO  "get_user_loginInfo"
#define STR_SET_USER_LOGININFO  "set_user_loginInfo"
#define STR_GET_CONTACT_LIST  "get_contact_list"
#define STR_SET_CONTACT_LIST  "set_contact_list"
 
#define STR_GET_SESSION_LIST  "get_session_list"
#define STR_SET_SESSION_LIST  "set_session_list"
 
#define STR_GET_FRIEND_NEARBY  "get_friend_nearby"
#define STR_DEL_FRIEND_REQ  "request_delete_friend"
#define STR_ADD_FRIEND_REQ  "request_add_friend"
 
#define STR_GET_FRIEND_FIND  "get_friend_find"
 
 
#define STR_GET_SESSION_CONTENT  "get_session_content"
#define STR_SET_SESSION_CONTENT  "set_session_content"
 
#define STR_REPORT_SESSION_ACTION  "report_session_action"
#define STR_SET_SESSION_ACTION  "set_session_action"
 
#define STR_ACTION_OPEN  "open"
#define STR_ACTION_CLOSE  "close"
#define STR_ACTION_ADDCONTENT  "addContent"
#define STR_ACTION_DELETE  "delete"
#define STR_ACTION_MESSAGE_READ  "message_read"
#define STR_ACTION_CMD_CLICK  "clickCmd"


#define  NEW_LINE  "\r\n"
#define  BOUNDARY  "82955dc54a56"
#define  BOUNDARY_LINE	"--"BOUNDARY NEW_LINE
#define  BOUNDARY_END	NEW_LINE"--"BOUNDARY"--"NEW_LINE
#define  POST_HEADER_BOUNDARY "Content-Type: multipart/form-data; boundary="BOUNDARY NEW_LINE

#endif

#if USE_LV_WATCH_KR_WEILIAO != 0
#define UP_VOICE_FILE_URL  "http://show.kaerplatform.com/kefile/fileManage/wechat/fileUpload" //"https://show.kaerplatform.com/esidcard/wechat/fileUpload"
#define CANTACT_ONE_READ_COUNT  10
#define SESSION_LIST_ONE_READ_COUNT  10
#define SESSION_CONTENT_ONE_READ_COUNT  10
ws_friends_list g_friends_list;
uint8_t g_friend_index = 0;
uint8_t g_need_Save_friend_nv= 1;
extern voice_msg_contact_t gYnContacts[APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM];
extern voice_msg_contact_t *gYnSessions;
uint16_t g_Contact_page_num= 1;
uint16_t g_Contact_one_num = 0;
uint16_t g_Contact_total= 0;
uint16_t g_Contact_page_total= 0;

uint16_t g_Session_page_num= 1;
uint16_t g_Session_one_num = 0;
uint16_t g_Session_total= 0;
uint16_t g_Session_page_total= 0;

uint16_t g_Session_content_page_num= 1;
uint16_t g_Session_content_one_num = 0;
uint16_t g_Session_content_total= 0;
uint16_t g_Session_content_page_total= 0;

uint8_t g_need_update_seesionlist = 0;
uint8_t g_clear_cur_seesion_unread = 0;

static void *link_get_sessioncont_timer=NULL;

typedef struct
{
    uint8_t *file;
	uint8_t *Bigfile;
    uint32_t      emoji_id;
}voice_msg_emoji_list;
//  "\uD83D\uDE00" -->unicodeת���ģ�ת��ͼƬ������ͼƬ-->ulr����
static const voice_msg_emoji_list  VoiceMsgEmoLIST[] = 
{
    {"%f0%9f%98%80", "%F0%9F%98%80", WECHAT_MSG_EMOJI_ID_IDX101},
    {"%f0%9f%98%82", "%F0%9F%98%82", WECHAT_MSG_EMOJI_ID_IDX102},
    {"%f0%9f%98%89", "%F0%9F%98%89", WECHAT_MSG_EMOJI_ID_IDX103},
    {"%f0%9f%98%ad", "%F0%9F%98%AD", WECHAT_MSG_EMOJI_ID_IDX104},
    {"%f0%9f%98%a0", "%F0%9F%98%A0", WECHAT_MSG_EMOJI_ID_IDX105},
};

typedef struct
{
    uint8_t *file;
    uint8_t      img_id;
}voice_msg_image_list;

voice_msg_image_list VoicemsgImageList[]=
{
	{"E788B8E788B8",WATCH_PORTRAIT_ID_FATHER},  //father
	{"E5A688E5A688",WATCH_PORTRAIT_ID_MOTHER}, //mother
	{"E788B7E788B7",WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER},
	{"E5A5B6E5A5B6",WATCH_PORTRAIT_ID_PATERNAL_GRADMOTHER},
	{"E794B7",		WATCH_PORTRAIT_ID_OWNER_BOY},
	{"E5A5B3",		WATCH_PORTRAIT_ID_OWNER_GIRL},
	{"E5A7A5E788B7",WATCH_PORTRAIT_ID_MATERNAL_GRADFATHER},
	{"E5A7A5E5A7A5",WATCH_PORTRAIT_ID_MATERNAL_GRADMOTHER},
};
#endif
static uint8_t hearttick_expire_cnt = 0;

#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
static uint16_t g_pos_uptime_count = 0;
static uint8_t g_pos_data[1000]={0};
static void *g_common_pos_timer = NULL;
#endif
#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
static void *g_1min_step_timer = NULL;
static uint8_t g_1min_step_count = 0;
static int g_cesq_crsp = 0;
static int g_cesq_ralv = 0;
nv_watch_high_freq_flag_t g_high_freq_s;

static uint8_t g_r_pos_num = 0;
#define LOCATION_MAX_LEN (1000)
#define SAVE_LOC_DATA_MAX_GROUP (5)
static uint8_t g_r_pos_data[SAVE_LOC_DATA_MAX_GROUP][LOCATION_MAX_LEN];
#endif

#if USE_LV_WATCH_ONLINE_CAR_HAILING !=0
extern int location_status;
extern char taxi_posData[800];
#endif
 #if USE_LV_WATCH_SPAIN!=0
TASK_HANDLE *SPO2_report_timer_thread = NULL;
TASK_HANDLE *HR_report_timer_thread = NULL;
// void send_to_plat(void);
// void SPO2_value_timer_thread_stop();
// TASK_HANDLE * SPO2_value_task_HANDLE = NULL;
// TASK_HANDLE * SPO2_value_timer_HANDLE = NULL;
// void SPO2_value_task_thread(void *data);
// void SPO2_value_timer_thread(void *data);
// void SPO2_value_test(void);
// int bo_time =70;
int SPO2_value_task_thread_sta=0;
static void blood_oxygen_measure_thread(void *param);
static void heart_measure_thread(void *param);


void CWatchService_ECO_mode(int data);
static void *ECO_mode_strat_Timer=NULL;
TASK_HANDLE * ECO_mode_strat_Timer_thread_HANDLE = NULL;
void ECO_mode_strat_Timer_thread(void *data);
extern bool get_eco_enable();
extern int get_eco_period();
int get_ECO_connect();
void set_ECO_connect(int data);
int ECO_connect=0;
typedef struct
{
    uint32_t cur_time;
	int oxygen_value ;
	int dial_bo_val;
}blood_oxygen_params_t;
blood_oxygen_params_t blood_oxygen_data;
#endif
#if USE_LV_WATCH_HEBAO_CHUXING!=0
#include "../lv_apps/hebao/hebao.h"
#include "../ke/ke_apdu.h"
#include "../modem/mmi_modem_adaptor_sim.h"


UINT8 g_reqSessionId[48]={0};
char g_thirdCommand[33]={0};
char g_cmdType[20]={0};
char g_cmdStatus[3]={0};
extern int g_currentStep;
char g_notifyUrl[200]={0};
extern TASK_HANDLE * ke_mult_apdu_thread;
extern int ke_mult_apdu_state;
char *g_cmdinfo=NULL;
extern CardApp_Info_t g_CardApp_Info;
static TYPE_HEBAO_STATUS g_hebao_status=HEBAO_IDLE; //0-idle 1-hebao open 

static void CWatchService_VersionReqDataJson(ws_cJSON  *root);

static void CWathcService_Parse_HebaoApduInfo(char *data)
{
    if(data == NULL)
		return;

	ws_printf("CWathcService_Parse_HebaoApduInfo %s",data);

	ws_cJSON *root = NULL,*list_array_s=NULL, *list_s=NULL;
	ws_cJSON *index_s=NULL, *checker_s=NULL, *command_s=NULL;
    int i=0,len=0,cnt=0,index=0;
	
	len=strlen(data);
	g_cmdinfo = (char *)Hal_Mem_Alloc(len+1);
	if(g_cmdinfo==NULL)
	{
	   ws_printf("g_cmdinfo Hal_Mem_Alloc ERROR");
	}
	else
	{
	   memset(g_cmdinfo,0,len+1);
	   strcpy(g_cmdinfo,data);
	}

	//list_s=ws_cJSON_Parse("[{\"index\":\"1001\",\"checker\":\".*9000$\",\"command\":\"apdu\"},{\"command\":\"00A4040010D1560001010001600000000100000000\",\"index\":1,\"checker\":\".*9000$\"},{\"command\":\"00A4040C10D15600010100017100000004B0017201\",\"index\":2,\"checker\":\".*9000$\"}]");
	list_s=ws_cJSON_Parse(data);
	if(list_s==NULL)
		return;

	cnt = ws_cJSON_GetArraySize(list_s);
	//WS_PRINTF("CWathcService_Parse_HebaoApduInfo cnt=%d",cnt);

	memset(&g_CardApp_Info,0,sizeof(CardApp_Info_t));
	
	for(i=1; i<cnt; i++)
	{
		list_array_s = ws_cJSON_GetArrayItem(list_s, i);
		if(list_array_s)
		{
			index_s = ws_cJSON_GetObjectItem(list_array_s, "index");
			if(index_s!=NULL && index_s->valuestring!=NULL)
			{
				index = atoi(index_s->valuestring);
				//WS_PRINTF("CWathcService_Parse_HebaoApduInfo index=%d",index);
				g_CardApp_Info.cmdinfo[i-1].index = index;
				//WS_PRINTF("CWathcService_Parse_HebaoApduInfo [%d]index=%d",i-1,index);
			}

			checker_s = ws_cJSON_GetObjectItem(list_array_s, "checker");
			if(checker_s!=NULL && checker_s->valuestring!=NULL)
			{
				//WS_PRINTF("checker=%s",checker_s->valuestring);
				strcpy(g_CardApp_Info.cmdinfo[i-1].checker,checker_s->valuestring);
			}
			command_s = ws_cJSON_GetObjectItem(list_array_s, "command");
			if(command_s!=NULL && command_s->valuestring!=NULL)
			{
				//WS_PRINTF("command=%s",command_s->valuestring);
				strcpy(g_CardApp_Info.cmdinfo[i-1].command,command_s->valuestring);

				CMD_DATA_T cmd={0,0,NULL,1};

				if(cnt>1)
				{
				    cmd.type = HEBAO_MULTIPLE;
				}
				else if(cnt==1)
				{
				    cmd.type = HEBAO_SINGLE;
				}

				len = strlen(command_s->valuestring);
				if(len > 0)
				{	
					cmd.len = len;
					cmd.str_value = (char *)Hal_Mem_Alloc(len + 1);
					if(cmd.str_value == NULL)
					{
						WS_PRINTF(" modlue_addin_queue malloc failded\n");
						break;
					}
					memset(cmd.str_value,0,len+1);
					memcpy(cmd.str_value,command_s->valuestring,len);
					cmd.str_value[len] = 0;

					//WS_PRINTF(" modlue_addin_queue cmd.str_value=%s\n",cmd.str_value);
				}

				cmd.id = index;

				if(modlue_addin_queue(modlue_addin_queue,1,&cmd) == 0)
				{
				    if(cmd.str_value != NULL)
			    	{
						Hal_Mem_Free(cmd.str_value);
			    	}
					WS_PRINTF(" modlue_addin_queue failded\n");
				    break;
				}
			}	
		}
	}
}

//1--success 0--fail
int CWathcService_Hebao_StartApdu(void *json)
{
    ws_cJSON *root_parse=(ws_cJSON *)json;
    uint16_t i, cnt=0, Status=0; 
	ws_cJSON *req_s=NULL,*cmdInfo_s = NULL,*Status_s = NULL,*cmdType_s = NULL, *notifyUrl_s=NULL, *reqSessionId_s=NULL,*thirdCommand_s=NULL;

	ws_printf("CWathcService_Hebao_StartApdu in");

	req_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(req_s)
    {
		Status_s = ws_cJSON_GetObjectItem(req_s, "cmdStatus");
		if(Status_s!=NULL && Status_s->valuestring!=NULL)
		{
			memset(g_cmdStatus,0,sizeof(g_cmdStatus));
			strcpy(g_cmdStatus, Status_s->valuestring);
			Status = atoi(Status_s->valuestring);
		}
		
	    cmdType_s = ws_cJSON_GetObjectItem(req_s, "cmdType");
		if(cmdType_s!=NULL && cmdType_s->valuestring!=NULL)
		{
		    memset(g_cmdType,0,sizeof(g_cmdType));
			strcpy(g_cmdType, cmdType_s->valuestring);

			WS_PRINTF("g_cmdType=%s",g_cmdType);
	
			if(Status==0)//start hebao
			{   
				if(g_hebao_status != HEBAO_IDLE)
			    {
			        WS_PRINTF("CWathcService_Hebao_StartApdu:device busy %d...\n",g_hebao_status);
					return 0;
			    }
				
				g_hebao_status = HEBAO_OPEN;
			    g_currentStep=0;
			   
				if (ke_mult_apdu_thread != NULL) 
				{
				    WS_PRINTF("ke_mult_apdu_thread set null....");
					uos_suspend_task(ke_mult_apdu_thread);
		            uos_delete_task(ke_mult_apdu_thread);
		            ke_mult_apdu_thread = NULL;
		        }
				
				ke_mult_apdu_state = 1;
		        ke_mult_apdu_thread = uos_create_task(ke_multiple_apdu_handle, NULL, 64, 2 * 1000, 200, "MULT_APDU thread");

				ke_Close_ApduChannel("1");

				uos_sleep(20); //100ms

				ke_Open_ApduChannel("1");

				uos_sleep(20); //100ms
				
			}
		}
		

		notifyUrl_s = ws_cJSON_GetObjectItem(req_s, "notifyUrl");
		if(notifyUrl_s!=NULL && notifyUrl_s->valuestring!=NULL)
		{
		    memset(g_notifyUrl,0,sizeof(g_notifyUrl));
			strcpy(g_notifyUrl, notifyUrl_s->valuestring);
		}

		reqSessionId_s = ws_cJSON_GetObjectItem(req_s, "reqSessionId");
		if(reqSessionId_s!=NULL && reqSessionId_s->valuestring!=NULL)
		{
		    memset(g_reqSessionId,0,sizeof(g_reqSessionId));
			strcpy(g_reqSessionId, reqSessionId_s->valuestring);
		}

        memset(g_thirdCommand,0,sizeof(g_thirdCommand));
		thirdCommand_s = ws_cJSON_GetObjectItem(req_s, "thirdCommandSeq");
		if(thirdCommand_s!=NULL && thirdCommand_s->valuestring!=NULL)
		{
			strcpy(g_thirdCommand, thirdCommand_s->valuestring);
		}

		cmdInfo_s = ws_cJSON_GetObjectItem(req_s, "cmdInfo");
		if(cmdInfo_s!=NULL && cmdInfo_s->valuestring!=NULL)
		{
			CWathcService_Parse_HebaoApduInfo(cmdInfo_s->valuestring);	
		}	
	}

	ws_printf("CWathcService_Hebao_StartApdu end");
	return 1;
}

static void CWathcService_Hebao_StartApduRsp(ws_client * pme, int ret)
{
    ws_cJSON  *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_cJSON *response_s = NULL;
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_SET_HEBAO_START_APDU;

    ws_cJSON_AddStringToObject(root,STRING_TYPE, STRING_TERMINAL);
	ws_cJSON_AddStringToObject(root,STRING_COMMAND, "startCmd");
	
	response_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,response_s);

	ws_cJSON_AddNumberToObject(response_s, "result", ret);

    CWatchService_SendJsonData(pme, root);
}

void CWatchService_HebaoUp_ApduRsp()
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_cJSON *response_s = NULL;
	ws_cJSON  *root = NULL; 
	char tmp[4]={0};
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_HEBAO_UP_APDU_RSP;

    ws_cJSON_AddStringToObject(root,STRING_TYPE, STRING_TERMINAL);
	ws_cJSON_AddStringToObject(root,STRING_COMMAND, "HebaoUpApduRsp");
	
	response_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,response_s);

	ws_cJSON_AddStringToObject(response_s, "cmdInfo", g_cmdinfo); 

	ws_cJSON *commandResults = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(response_s,"commandResults",commandResults);
	ws_cJSON_AddStringToObject(commandResults, "succeed", "true");

	ws_cJSON *list_results = ws_cJSON_CreateArray();
	ws_cJSON_AddItemToObject(commandResults,"results",list_results);

	for(int i=0; i<g_CardApp_Info.ptr; i++)
	{
	   ws_cJSON *arrayitem = ws_cJSON_CreateObject();

	   ws_cJSON_AddNumberToObject(arrayitem,"index",g_CardApp_Info.cmdinfo[i].index);
	   ws_cJSON_AddStringToObject(arrayitem,"command",g_CardApp_Info.cmdinfo[i].command);
	   ws_cJSON_AddStringToObject(arrayitem,"checker",g_CardApp_Info.cmdinfo[i].checker);
	   ws_cJSON_AddStringToObject(arrayitem,"result",g_CardApp_Info.cmdinfo[i].apdu_rsp);
	
	   ws_cJSON_AddItemToArray(list_results,arrayitem);
	}

	WS_PRINTF("----g_cmdType=%s,g_currentStep=%d,g_cmdStatus=%s",g_cmdType,g_currentStep,g_cmdStatus);

	sprintf(tmp,"%d",g_currentStep);
	ws_cJSON_AddStringToObject(response_s, "currentStep", tmp);	
	ws_cJSON_AddStringToObject(response_s, "cmdStatus", g_cmdStatus);
	ws_cJSON_AddStringToObject(response_s, "cmdType", g_cmdType);
	ws_cJSON_AddStringToObject(response_s, "reqSessionId", g_reqSessionId);
	ws_cJSON_AddStringToObject(response_s, "notifyUrl", g_notifyUrl);
	ws_cJSON_AddStringToObject(response_s, "thirdCommandSeq", g_thirdCommand);

    CWatchService_SendJsonData(pme, root);

	if(g_cmdinfo!=NULL)
	{
		Hal_Mem_Free(g_cmdinfo);
		g_cmdinfo = NULL;
	}
}

void CWathcService_Hebao_SetApdu(void *json)
{
    ws_cJSON *root_parse=(ws_cJSON *)json;
    uint16_t i, cnt=0, Status=0; 
	ws_cJSON *req_s=NULL,*cmdInfo_s = NULL,*Status_s = NULL,*cmdType_s = NULL, *notifyUrl_s=NULL, *reqSessionId_s=NULL, *thirdCommand_s=NULL;

	req_s = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
	if(req_s)
	{
		Status_s = ws_cJSON_GetObjectItem(req_s, "cmdStatus");
		if(Status_s!=NULL && Status_s->valuestring!=NULL)
		{
			Status = atoi(Status_s->valuestring);
			memset(g_cmdStatus,0,sizeof(g_cmdStatus));
			strcpy(g_cmdStatus, Status_s->valuestring);
		}
		
		if(Status==1)//continue
		{
		    WS_PRINTF("CWathcService_Hebao_SetApdu continue");
		    notifyUrl_s = ws_cJSON_GetObjectItem(req_s, "notifyUrl");
			if(notifyUrl_s!=NULL && notifyUrl_s->valuestring!=NULL)
			{
			    memset(g_notifyUrl,0,sizeof(g_notifyUrl));
				strcpy(g_notifyUrl, notifyUrl_s->valuestring);
			}

			reqSessionId_s = ws_cJSON_GetObjectItem(req_s, "reqSessionId");
			if(reqSessionId_s!=NULL && reqSessionId_s->valuestring!=NULL)
			{
			    memset(g_reqSessionId,0,sizeof(g_reqSessionId));
				strcpy(g_reqSessionId, reqSessionId_s->valuestring);
			}

			memset(g_thirdCommand,0,sizeof(g_thirdCommand));
			thirdCommand_s = ws_cJSON_GetObjectItem(req_s, "thirdCommandSeq");
			if(thirdCommand_s!=NULL && thirdCommand_s->valuestring!=NULL)
			{
				strcpy(g_thirdCommand, thirdCommand_s->valuestring);
			}

			cmdInfo_s = ws_cJSON_GetObjectItem(req_s, "cmdInfo");
			if(cmdInfo_s!=NULL && cmdInfo_s->valuestring!=NULL)
			{
				CWathcService_Parse_HebaoApduInfo(cmdInfo_s->valuestring);	
			}
		}
		else if(Status==2)//end
		{
		    g_hebao_status = HEBAO_IDLE;
		    WS_PRINTF("CWathcService_Hebao_SetApdu close");
		    if (ke_mult_apdu_thread != NULL) 
			{
			    WS_PRINTF("ke_mult_apdu_thread set null....");
				uos_suspend_task(ke_mult_apdu_thread);
	            uos_delete_task(ke_mult_apdu_thread);
	            ke_mult_apdu_thread = NULL;
	        }

			ke_Close_ApduChannel("1");
		}	
	}

	ws_printf("CWathcService_Hebao_SetApdu end");
} 

static void CWatchService_SendHebaoInfo(ws_client * pme)
{
    ws_cJSON *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    userData->type = KAER_CMD_GET_HEBAO_INFO;

    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }  

    ws_cJSON_AddStringToObject(root,STRING_TYPE,STRING_TERMINAL);
    ws_cJSON_AddStringToObject(root,STRING_COMMAND,"getTerminalInfo");
	
	ws_cJSON *req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,req);

	ws_cJSON_AddNumberToObject(req,"result",1);

	char imsi[20]={0};
	MMI_Modem_Query_Imsi_Req(MMI_MODEM_SIM_1, imsi);
	ws_cJSON_AddStringToObject(req,"imsi",imsi);	

    char number[20]={0};
	MMI_Modem_Query_SimPhone_Req(MMI_MODEM_SIM_1, number);
	if(!strcmp("Unknown",number))
	{
	    memset(number, 0, sizeof(number));
	    ke_GetPhoneNum(number);
		ws_cJSON_AddStringToObject(req,"simPhone",number);
	}
	else
	    ws_cJSON_AddStringToObject(req,"simPhone",number);
	
	ws_cJSON_AddStringToObject(req,"firmVersion", app_adaptor_get_version());

	uint16_t percent=Hal_Battery_Get_Status();
    char power[4]={0};
	sprintf(power,"%d",percent);
	ws_cJSON_AddStringToObject(req,"powerNum", power);

	ws_cJSON_AddStringToObject(req,"is_online", "1");

    char data[32]={0};
	memset(data, 0, sizeof(data));
    ke_GetCardNo(data);
	ws_cJSON_AddStringToObject(req,"cardNo",data);

	memset(data, 0, sizeof(data));
    ke_GetYuErStr(data);
	ws_cJSON_AddStringToObject(req,"cardBalance",data);

    memset(data, 0, sizeof(data));
	ke_GetSeid(data);
	ws_cJSON_AddStringToObject(req,"seid",data);

    CWatchService_SendJsonData(pme, root);
}

void CWatchService_SendHebaoOrderInfo(uint32_t type)
{
    ws_cJSON *root = NULL;    
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    userData->type = type;
	char termSeq[5]={0},txnAmt[9]={0},transType[3]={0},termId[13]={0},txnDate[9]={0},txnTime[7]={0};
	char *data=NULL;
	int amount=0;
	char tmp[6]={0};

	ke_Close_ApduChannel("1");

	if (ke_mult_apdu_thread != NULL) 
	{
		WS_PRINTF("ke_mult_apdu_thread set null....");
		uos_suspend_task(ke_mult_apdu_thread);
		uos_delete_task(ke_mult_apdu_thread);
		ke_mult_apdu_thread = NULL;
	}
	
    root = ws_cJSON_CreateObject();
    if(NULL == root) return;
  
    ws_cJSON_AddStringToObject(root,STRING_TYPE,STRING_TERMINAL);
	if(type==KAER_CMD_GET_HEBAO_ORDER_LIST)
        ws_cJSON_AddStringToObject(root,STRING_COMMAND,"getOrderList");
	else 
	    ws_cJSON_AddStringToObject(root,STRING_COMMAND,"pushOrderList");
	
	ws_cJSON *req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,req);

	ws_cJSON_AddNumberToObject(req,"result",1);

	char num[20]={0};
	memset(num, 0, sizeof(num));
    ke_GetCardNo(num);
	ws_cJSON_AddStringToObject(req,"cardNo", num);
	
	ws_cJSON *list_order = ws_cJSON_CreateArray();
	ws_cJSON_AddItemToObject(req,"OrderList",list_order);

	for(int i=0; i<g_CardApp_Info.ptr; i++)
	{
	   memset(termSeq,0,sizeof(termSeq));
	   memset(txnAmt,0,sizeof(txnAmt));
	   memset(transType,0,sizeof(transType));
	   memset(termId,0,sizeof(termId));
	   memset(txnDate,0,sizeof(txnDate));
	   memset(txnTime,0,sizeof(txnTime));

	   //00520000000000000A09413101881521202302211335539000
	   data = g_CardApp_Info.cmdinfo[i].apdu_rsp;
	
	   strncpy(termSeq,data,4);
	   strncpy(txnAmt,data+10,8);
	   strncpy(transType,data+18,2);
	   strncpy(termId,data+20,12);
	   strncpy(txnDate,data+32,8);
	   strncpy(txnTime,data+40,6);

	   amount=strtoint(txnAmt);
	   memset(tmp,0,sizeof(tmp));
	   sprintf(tmp,"%d",amount);

	   if(!strcmp(transType,"09"))
	   {
		   ws_cJSON *arrayitem = ws_cJSON_CreateObject();
		   ws_cJSON_AddStringToObject(arrayitem,"termSeq",termSeq); //liushuihao
		   ws_cJSON_AddStringToObject(arrayitem,"txnAmt",tmp); //jin e
		   ws_cJSON_AddStringToObject(arrayitem,"transType",transType); 
		   ws_cJSON_AddStringToObject(arrayitem,"termId",termId); //zhongduan bianhao
		   ws_cJSON_AddStringToObject(arrayitem,"txnDate",txnDate); 
		   ws_cJSON_AddStringToObject(arrayitem,"txnTime",txnTime);
		   
		   ws_cJSON_AddItemToArray(list_order,arrayitem);
	   }
	}
	
    CWatchService_SendJsonData(pme, root);
}

#endif

#if USE_LV_WATCH_SMS_INTERCEPT != 0

void CWathcService_SetSMSIntercept_Old(ws_client * pme, void *json)
{
    ws_cJSON     *root_parse = (ws_cJSON *)json;
    ws_cJSON        *pkId_s = NULL;
    uint16_t          i,cnt = 0;
     ws_cJSON *request_s = NULL;
     char sbuf[2] = {0};
	 uint8_t SmsIntercept;

    request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(!request_s)
    {
    	return;
    }

	pkId_s = ws_cJSON_GetObjectItem(request_s, "interceptorMode");
    if(!pkId_s)
    {
    	return;
    }
	strcpy(sbuf,pkId_s->valuestring);
	WS_PRINTF("CWathcService_SetSMSIntercept_Old=%d",atoi(sbuf));

	SmsIntercept = atoi(sbuf);
	UI_NV_Write_Req(NV_SECTION_UI_SMS_MSG, NV_OFFSETOF(nv_watch_sms_msg_t, sms_intercept), sizeof(uint8_t), &SmsIntercept) ;	

}

void CWatchService_SendSMS(ws_client *pme,char* number, char* sms,uint16_t len)
{
	int i=0,j=0;
	char time_buf[32]={0};
	hal_rtc_t cur_time;

	if(pme == NULL)
	{
		return;
	}
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_cJSON  *root = NULL;    
	ws_cJSON  *req = NULL; 
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_REPORT_SMS;
	req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req);

	ws_cJSON *order_array = ws_cJSON_CreateArray();
	ws_cJSON_AddItemToObject(req, "list", order_array);

	ws_cJSON *arrayitem1 = ws_cJSON_CreateObject();

	ws_cJSON_AddStringToObject(arrayitem1,"sendNum",number);
	

	WS_PRINTF("node->name: %s", number);
	
	char *smsdata = (char *)malloc(len+2);

    memcpy(smsdata, sms, len);
    smsdata[len] = 0;
    smsdata[len+1] = 0;
	
	ws_cJSON_AddStringToObject(arrayitem1,"sendContent",smsdata);
	
	Hal_Rtc_Gettime(&cur_time);
	sprintf(time_buf, "%d%02d%02d%02d%02d%02d", cur_time.tm_year, cur_time.tm_mon, cur_time.tm_mday, cur_time.tm_hour, cur_time.tm_min, cur_time.tm_sec);
	WS_PRINTF("timebuf: %s", time_buf);
	ws_cJSON_AddStringToObject(arrayitem1,"sendTime",time_buf);
	ws_cJSON_AddItemToArray(order_array,arrayitem1);
	free(smsdata);
	CWatchService_SendJsonData(pme, root);
}
#endif

int is_ECO_mode_valid(char *input, int *first_value, int *second_value) {
    // 分割数据
    char *token;

    // 使用 strtok 分割字符串
    token = strtok(input, ","); // 获取第一个值
    if (token == NULL || sscanf(token, "%d", first_value) != 1) {
        return -1; // 解析失败，返回 -1
    }

    token = strtok(NULL, ","); // 获取第二个值
    if (token == NULL || sscanf(token, "%d", second_value) != 1) {
        return -1; // 解析失败，返回 -1
    }

    // 检查第一个值是否为 0 或 1
    if (*first_value < 0 || *first_value > 1) {
        return -1; // 第一个值不符合要求，返回 -1
    }

    // 检查第二个值是否是非负数
    if (*second_value < 0) {
        return -1; // 第二个值不符合要求，返回 -1
    }

    return 0; // 成功
}

static int CWatchService_SMS_SetUrl(char *Data)
{
	if(Data == NULL)
	{
		return 0;
	}
	char *Dat = Data;
	char UrlPort[100] = {0};
	strcpy(UrlPort, Dat);
		
	char *Index = strstr(UrlPort, "@");
	if(Index != NULL)
	{
		*Index = 0;
		Index++;
		int Port = atoi(Index);
		if(Port > 0 && Port < 0xffff)
		{
			if(!strcmp(sUrl, UrlPort)&&server.port == Port)
				return 0;
			strcpy(sUrl, UrlPort);
		
			server.port = Port;
			
			myNVMgr_SetWsUrlPort(Data);
		}
		WS_PRINTF("%s server.host IS %s, server.port is %d",__FUNCTION__, server.host, server.port);

		return 1;
	}		
}

void CWatchService_New_SMS_Handle(ws_client * pme, char* number, char* sms, uint16_t len)
{
	uint8_t SmsIntercept = 0;
    uint32_t length = sizeof(SmsIntercept);
	WS_PRINTF("CWatchService_New_SMS_Handle()len=%d, number=%s,sms=%s\n ", len, number, sms);

    if(WS_SMS_MAX_LEN < len || len <= 0)
    {
        return;
    }

 	if (!strncmp(number, "+86", 3))
    {
        number += 3;
 	}

	if (strncmp(sms, "KAER_UPDATE#", 12)==0)
	{
		WS_PRINTF("KAER_UPDATE_START");
		CWatchService_StartUpData();
		return;
	}
	#if USE_LV_WATCH_SETTING_PASSWORD != 0
	else if (strncmp(sms, "RESET_ZF#", 9)==0)
	{
		if(sos_check_phone_number(number))
		{
			WS_PRINTF("KAER_RESET_ZF");
			setting_password_reset();	
		}
	}
	#endif
	#if USE_LV_WATCH_SPAIN != 0
	else if (strncmp(sms, "#reboot#", 8)==0)    //重启短信指令
	{
		WS_PRINTF("[USE_LV_WATCH_SPAIN] WatchUtility_PassiveReboot\n");
		onkey_wakeup();
		//短信回复重启指令
		char send_buf[40] = {0};
		int sms_len = CWatchService_parse_sms_content("Reboot ok",strlen("Reboot ok"),0,send_buf);
		CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
		uos_sleep_ms(1000);

		WatchUtility_PassiveReboot(NULL);
	}
	else if (strncmp(sms, "#off#", 5)==0)	//关机短信指令
	{
		WS_PRINTF("[USE_LV_WATCH_SPAIN] shutdown_confirm_btn_action\n");
		onkey_wakeup();

		//短信回复关机指令
		char send_buf[40] = {0};
		int sms_len = CWatchService_parse_sms_content("Off ok",strlen("Off ok"),0,send_buf);
		CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
		uos_sleep_ms(1000);

		shutdown_confirm_btn_action(NULL, LV_EVENT_CLICKED);
	}
	else if (strncmp(sms, "#eco#=", 6)==0)	//ECO短信指令
	{
		char *data = sms + 6;
		int sms_eco_enable = 0, sms_eco_period = 0;
        ws_printf("#eco#=%s\n", data);
		if (is_ECO_mode_valid(data, &sms_eco_enable, &sms_eco_period) == 0) 
		{
			ws_printf("sms_eco_enable=%d,sms_eco_period=%d\n", sms_eco_enable, sms_eco_period);
			nv_watch_settings_t settings;
			UI_NV_Read_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (UINT8 *)&settings);
			// 短信开启ECO模式
			if (sms_eco_enable == 1)
			{	
				if (settings.eco_enable == sms_eco_enable)  // 当前已经开启ECO模式
				{  	
					if (settings.eco_period == sms_eco_period)
					{
						// 相同的ECO模式周期，无需操作
						return;
					}
					else
					{	
						// ECO下发的周期不相同，重新启动ECO
						settings.eco_period = sms_eco_period;
						set_eco_period(sms_eco_period);
						CWatchService_ECO_mode(1);
					}
				}     
				else
				{	
					// 当前未开启ECO模式，开启ECO
					settings.eco_enable = sms_eco_enable;
					settings.eco_period = sms_eco_period;
					set_eco_period(sms_eco_period);
					CWatchService_ECO_mode(1);
				}
				ws_printf("CWatchService_New_SMS_Handle settings.eco_enable %d,settings.eco_period=%d",settings.eco_enable,settings.eco_period);
				UI_NV_Write_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (UINT8 *)&settings);
			}
			// 短信关闭ECO模式
			else if (sms_eco_enable == 0)
			{    
				if (settings.eco_enable == 0)  // 当前已经关闭ECO模式
				{  	
					return;
				}
				else
				{
					// 当前开启ECO模式，关闭ECO
					settings.eco_enable = sms_eco_enable;
					CWatchService_ECO_mode(0);
				}
				UI_NV_Write_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (UINT8 *)&settings);
			}

			//短信回复ECO指令
			char send_buf[40] = {0};
			int sms_len;
			if(sms_eco_enable == 1)
				sms_len = CWatchService_parse_sms_content("Eco mode on",strlen("Eco mode on"),0,send_buf);
			else
				sms_len = CWatchService_parse_sms_content("Eco mode off",strlen("Eco mode off"),0,send_buf);
			CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
			uos_sleep_ms(1000);

		}
		else{
			printf("Invalid eco mode.\n");
		}
	}
	else if (strncmp(sms, "#apn#=", 6)==0)	//APN短信指令
	{
		char *data = sms + 6;
        ws_printf("#apn#=%s\n", data);
		char mcc[5], mnc[5], apn_address[50],  apn_name[50];
		char username[50], password[50], protocol[10];
		ui_file_t * apn_file = NULL;
		if (sscanf(data, "%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^#]",
				mcc, mnc, apn_address, apn_name, username, password, protocol) != 7) 
		{
			ws_printf("Invalid input format.\n");

			//短信回复配置APN指令
			char send_buf[40] = {0};
			int sms_len = CWatchService_parse_sms_content("APN error",strlen("APN error"),0,send_buf);
			CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
			uos_sleep_ms(1000);
		} 
		else 
		{
			if((strcmp(protocol,"PAP")!=0)||(strcmp(protocol,"CHAP")!=0))
			{
				memset(protocol,0,strlen(protocol));
				memcpy(protocol,"NONE",strlen("NONE"));
				memset(username,0,strlen(username));
				memcpy(username,"any",strlen("any"));
				memset(password,0,strlen(password));
				memcpy(password,"any",strlen("any"));
			}
 
			char apn_command[100]; // 存储构造的APN配置
			snprintf(apn_command, sizeof(apn_command), "{%s,%s,\"%s\",\"%s\",\"0\",\"%s\",\"%s\",\"%s\",\"IPV4V6\"}"
			,mcc
			,mnc
		  	,apn_name
			,apn_address
			,protocol
			,username
			,password);

			ws_printf("apn_command 66666666666666666666666: %s\n", apn_command);

			char *read_buf = NULL;
			uint32_t ReadLen = 0;
			uint32_t apn_config_num = 0;
			read_buf = (char *)lv_mem_alloc(0x60*20);
			memset(read_buf, 0, 0x60*20);

			apn_file = UI_FILE_OPEN(APN_CONFIG_FILE, UI_FILE_READ_ONLY);
			if (NULL != apn_file->file_d)
			{
				UI_FILE_SEEK(apn_file, 0); // 定位到指定的偏移量
				ReadLen = UI_FILE_READ(apn_file, read_buf, 0x60*20);
				ws_printf("11111111111111read_buf: %s,ReadLen= %d\n", read_buf, ReadLen);
				for(int i = 0; i < ReadLen; i++) {
					if(read_buf[i] == '}') {
						apn_config_num++;
						ws_printf("apn_config_num++++++++++++++++++++++");
					}
				}
			}
			UI_FILE_CLOSE(apn_file);
			apn_file = NULL;
 
			if(apn_config_num >= 20)
			{				
				char *first_apn_brace = NULL;
				first_apn_brace = strchr(read_buf,	'}');
				ws_printf("**********2222 del_first_apn: %s,len = %d\n", first_apn_brace+1, strlen(first_apn_brace+1));
				memcpy(&read_buf[0], first_apn_brace+1, strlen(read_buf));

				ws_printf("------------11------------read_buf: %s,len = %d ,dddd_len = %d\n", read_buf, strlen(read_buf),ReadLen-strlen(first_apn_brace+1));
				
			}
			strncat(read_buf, apn_command,strlen(apn_command));

			apn_file = UI_FILE_OPEN(APN_CONFIG_FILE, UI_FILE_RD_APPEND);
			if (NULL == apn_file->file_d)
			{
				ws_printf("333333333333333333333333333open apn file failed\n");
				apn_file = UI_FILE_OPEN(APN_CONFIG_FILE, UI_FILE_WRITE_ONLY);
				UI_FILE_SEEK(apn_file, 0);
				UI_FILE_WRITE(apn_file, read_buf, strlen(read_buf));
				UI_FILE_CLOSE(apn_file);
				apn_file = NULL;
			}
			else
			{
				UI_FILE_WRITE(apn_file, read_buf, strlen(read_buf));
				UI_FILE_CLOSE(apn_file);
				apn_file = NULL;
			}
			ws_printf("**********2222read_buf: %s  len = %d\n", read_buf, strlen(read_buf));
			lv_mem_free(read_buf);

			//短信回复配置APN指令
			char send_buf[40] = {0};
			int sms_len = CWatchService_parse_sms_content("APN ok",strlen("APN ok"),0,send_buf);
			CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
			uos_sleep_ms(1000);

		}
	}
	else if (strncmp(sms, "#host#=", 7)==0)		//设置平台地址
	{
		char *data = sms + 7;
		ws_printf("#host#=%s\n", data);
		char hostname[50];
		char port[10];
		char ip_port[60];

		if (sscanf(data, "%[^,],%[^#]", hostname, port) != 2) 
		{
			ws_printf("Invalid input format.\n");

			//短信回复配置host指令
			char send_buf[40] = {0};
			int sms_len = CWatchService_parse_sms_content("Host error",strlen("Host error"),0,send_buf);
			CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
			uos_sleep_ms(1000);
		} 
		else 
		{

			//短信回复配置host指令
			char send_buf[40] = {0};
			int sms_len = CWatchService_parse_sms_content("Host ok",strlen("Host ok"),0,send_buf);
			CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
			uos_sleep_ms(1000);

			snprintf(ip_port, sizeof(ip_port), "%s@%s", hostname, port);
            ws_printf("ip_port: %s\n", ip_port);
			int ret = CWatchService_SMS_SetUrl(ip_port);
			if(ret == 1)
			{
				MMI_ModemAdp_WS_Stop_Ext();
		   		int time = 0;
				while(time < 10)
				{
					if(!MMI_ModemAdp_WS_Is_Online())
					{
						uos_sleep(200);
						//start link 
						MMI_ModemAdp_WS_Start(pme);
						return;
					}
					time++;
					uos_sleep(200);
				}
			}		
		}
	}
	else if (strncmp(sms, "#status#", 8)==0)	//获取状态短信指令
	{
		WS_PRINTF("[USE_LV_WATCH_SPAIN] get status\n");

		//短信回复获取状态
		char sms_buf[200] = {0};
		char send_buf[250] = {0};
		char imei[15] = {0};
		char apn[100] = {0};
		char eco_mode_on_off[10] = {0};
		char firmware[50] = {0};

		char at_command[100]; // 存储构造的 AT 指令
		snprintf(at_command, sizeof(at_command), "AT+CGDCONT?");
		MMI_ModemAdp_Send_AT_Handler(at_command, NULL);
		uos_sleep_ms(100);

		MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, imei);

		char * apn_address = Get_apn_address();
		strcpy(apn,apn_address);

		if(get_eco_enable() == true)
			sprintf(eco_mode_on_off,"on");
		else
			sprintf(eco_mode_on_off,"off");
		sprintf(firmware, "%s",app_adaptor_get_external_version(0));

		sprintf(sms_buf, "Imei:%s;APN:%s;Host:%s;Firmware:%s;EcoMode:%s",imei,apn_address,server.host,firmware,eco_mode_on_off);
		int sms_len = CWatchService_parse_sms_content(sms_buf,strlen(sms_buf),0,send_buf);
		CWatchService_SendSmsReq(number,strlen(number),send_buf,sms_len);
		uos_sleep_ms(100);

	}
	#endif

	else if(strncmp(sms, "KAER_REBOOT#", 12)==0)
	{
		WatchUtility_PassiveReboot(NULL);
	}
    else
    {     
    	#if USE_LV_WATCH_LOCK_PHONE !=0
		if(check_iccid_is_same()==0)
		{
			WS_PRINTF("CWatchService_New_SMS_Handle >> check_iccid_is_same=0++++++++++++++++");
			return;
		}
		#endif
    #if USE_LV_WATCH_SMS_INTERCEPT != 0
    	if(length != UI_NV_Read_Req(NV_SECTION_UI_SMS_MSG, NV_OFFSETOF(nv_watch_sms_msg_t, sms_intercept), sizeof(uint8_t), &SmsIntercept))
		{
			WS_PRINTF("Read sms_intercept failed, length = %d",length);
			SmsIntercept = QT_SMS_NONE_INTERCEPT;	// 璇诲彇璁剧疆澶辫触锛岃缃负榛樿
		}

		if(QT_SMS_NONE_INTERCEPT == SmsIntercept) 
		{
			WS_PRINTF("Enter QT_SMS_NONE_INTERCEPT!");
			sms_msg_create_node(number, sms, len);
			CWatchService_SendSMS(pme,number,sms,len);
		}
		else if(QT_SMS_WHITELIST_NONE_INTERCEPT == SmsIntercept)
		{	
			WS_PRINTF("Enter QT_SMS_WHITELIST_NONE_INTERCEPT!");
			char * pb_number = phonebook_get_phone_number(0);
			phonebook_contact_t * contact_info = NULL;
			char *check_nu = (char *)lv_mem_alloc(strlen(number)+1);

			if(check_nu == NULL) return;
			if(number == NULL) return;

			strcpy(check_nu, number);
			
			if(pb_number) {
				Hal_Mem_Free(pb_number);
				pb_number = NULL;
				contact_info = phonebook_get_contact_info(check_nu);
			}
			else
			{
				lv_mem_free(check_nu);
			}
			if(contact_info != NULL || sos_check_phone_number(number)||ke_common_check_operator(number))
			{
				sms_msg_create_node(number, sms, len);
				CWatchService_SendSMS(pme,number,sms,len);
			}
		}
		else
		{
			WS_PRINTF("Enter QT_SMS_ALL_INTERCEPT!");
		}
	#elif USE_LV_WATCH_SMS_MSG != 0
	char * pb_number = phonebook_get_phone_number(0);
	phonebook_contact_t * contact_info = NULL;
	char *check_nu = (char *)lv_mem_alloc(strlen(number)+1);

	if(check_nu == NULL) return;
	if(number == NULL) return;

	strcpy(check_nu, number);

	if(pb_number) {
		lv_mem_free(pb_number);
		pb_number = NULL;
		contact_info = phonebook_get_contact_info(check_nu);
	}
	else
	{
		lv_mem_free(check_nu);
	}

	if(contact_info != NULL || sos_check_phone_number(number))
	{
		lv_mem_free(contact_info);
		sms_msg_create_node(number, sms, len);
	}
	#endif /* USE_LV_WATCH_VOICE_MSG */
    }
}

char* CWathcService_WsGetRecievedFileName(char* name, char* url)
{
    int i= strlen(url)-1;

    while(i>0)
    {
        if(url[i]=='/')
        {
            if(name)
            {
                strcpy(name, url+i+1);
            }
            return (url+i+1);
        }
        i--;
    }

    return NULL;
}

void Set_apn_address(char * apn)
{
	if(apn != NULL)
		strcpy(apn_address, apn);
	else
		strcpy(apn_address, "Unknown");
	printf("aaaaaaaaaaaaaaaaaaaaaaaaapn_address: %s   len=%d\n", apn_address, strlen(apn_address));
}

char* Get_apn_address()
{
	return apn_address;
}

char*CWatchService_TokenCode(ws_client * pme)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    return (char*)userData->m_Token;
}


static uint32_t CWatchService_GetDevSeq(ws_client * pme)
{   
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	uint32_t seed;
	uint32_t r;
	WS_PRINTF("%s() dev_seq=%d\n", __func__, userData->dev_seq);
	userData->dev_seq ++;
	return userData->dev_seq;
}

static ws_evt_id_e CWatchService_GetCmdid(uint32_t *pMsgCmd)
{
    uint16_t cmd_count = 0;
	WS_PRINTF("CWatchService_GetCmdid() pMsgCmd=%04x\n", pMsgCmd);
    if(pMsgCmd == KAER_CMD_LOGIN)
    {
    	return CMD_S2C_DEV_LOGIN_RSP;
    }   
	else if(pMsgCmd == KAER_CMD_BAT_INF)
    {
    	return CMD_C2S_PONG_RSP;
    }   
	else if(pMsgCmd == KAER_CMD_HEART_S)
    {
    	return CMD_S2C_SET_PERIOD;
    }   
	else if(pMsgCmd == KAER_CMD_SOS_LIST)
    {
    	return CMD_S2C_SETPARAM_CONTACT_LIST;
    }
	else if(pMsgCmd == KAER_CMD_POS_REQ)
    {
    	return CMD_S2C_EXEC_LOCATION;
    } 
	#if USE_LV_WATCH_SPAIN == 0  
	else if(pMsgCmd == KAER_CMD_PWROFF)  //spain dosen't need
    {
    	return CMD_S2C_EXEC_SHUTDOWM;
    }   
	else if(pMsgCmd == KAER_CMD_RESET_FACTORY)     //spain dosen't need 
    {
    	return CMD_S2C_SETPARAM_RESET;
    } 
	else if(pMsgCmd == KAER_CMD_STUDY_TIME)   //spain dosen't need
    {
    	return CMD_S2C_SETPARAM_SLIENT_PERIOD;
    } 
	else if(pMsgCmd == KAER_CMD_SMS_INTERCEPT)    //spain dosen't need
	{
		return CMD_S2C_SETPARAM_SMS_INTERCEPT;
	}
	#endif
	else if (pMsgCmd == KAER_CMD_UPDATA)
    {
		return CMD_DEV_IND_KAER_UPDATA;
    }
	else if (pMsgCmd == KAER_CMD_ECO_OPEN)
    {
		return CMD_KAER_CMD_ECO_OPEN;
    }
	else if (pMsgCmd == KAER_CMD_PLATFORM_ALARM)
	{
		return CMD_REQ_SET_PLATFORM_ALARM;
	}
	else if (pMsgCmd == KAER_CMD_TIMEZONE_FORMAT)
	{
		return CMD_S2C_SET_TIMEZONE_FORMAT;
	}
	else if (pMsgCmd == KAER_CMD_FALL_DETECTION)
	{
		return CMD_S2C_SET_FALL_DETECTION;
	}
	else if (pMsgCmd == KAER_CMD_SHOW_MESSAGE)
	{
		return CMD_S2C_SHOW_MESSAGE;
	}
	else if (pMsgCmd == KAER_CMD_SET_LOCATION_CONFIG)
	{
		return CMD_SET_LOCATION_CONFIG;
	}
	else if (pMsgCmd == KAER_CMD_START_MEASURE)
	{
		return CMD_S2C_START_MEASURE;
	}
	else if (pMsgCmd == KAER_CMD_POWEROFF)
	{
		return CMD_S2C_EXEC_REBOOT;
	}
	else if (pMsgCmd == KAER_CMD_SET_HEALTH_FREQ)
	{
		return CMD_KAER_SET_HEALTH_FREQ;
	}
    return WS_SERVICE_EVENT_MAX;
}

static void CWatchService_ResultRspDataJson(ws_kaer_t *userData, ws_cJSON  *root, uint8_t nResult, uint16_t type)
{
	if(userData == NULL || root == NULL)
	{
		return;
	}
	ws_cJSON *rsp = NULL;
	char valueStr[30]={0};
	
	rsp = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,rsp);
	if(type == KAER_CMD_REBOOT)
	{
		#if USE_LV_WATCH_SPAIN == 0
		ws_cJSON_AddStringToObject(rsp,"type","4");	
		ws_cJSON_AddStringToObject(rsp,"devId",userData->mDeviceId);	
		#endif
	}
	else if(type == KAER_CMD_UPDATA)
	{
		ws_cJSON_AddStringToObject(rsp,"DeviceID",userData->mDeviceId);
	}
	
	valueStr[0]= 0;
	sprintf(valueStr, "%d", nResult);
	ws_cJSON_AddStringToObject(rsp,STRING_RESULT,valueStr);	
}


static void CWatchService_SendSimpleCmd(ws_client * pme, uint16_t type, double seq)
{
    ws_cJSON  *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = type;

    CWatchService_ResultRspDataJson(userData,root,1, type);

    CWatchService_SendJsonData(pme, root);
}


static void CWatchService_SendCourseRspCmd(ws_client * pme, uint16_t type, double seq)
{
    ws_cJSON  *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};	
	ws_cJSON *rsp = NULL;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = type;

    ws_cJSON_AddStringToObject(root,STRING_TYPE, "app");
	
	ws_cJSON_AddStringToObject(root,STRING_COMMAND, "course");
		
	rsp = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,rsp);

	valueStr[0]= 0;
	sprintf(valueStr, "%d", 1);
	ws_cJSON_AddStringToObject(rsp,STRING_RESULT,valueStr);
	ws_cJSON_AddStringToObject(rsp,"Message","OK");

    CWatchService_SendJsonData(pme, root);
}

static void CWatchService_AlarmIndCmd(ws_client * pme, uint16_t type, double seq)
{
	#if USE_LV_WATCH_LOCK_PHONE !=0
	if(check_iccid_is_same()==0)
	{
		WS_PRINTF("CWatchService_AlarmIndCmd >> check_iccid_is_same=0++++++++++++++++");
		return;
	}
	#endif
	ws_cJSON  *root = NULL;    
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};	
	ws_cJSON *req_s = NULL;
	hal_rtc_t rtc_curr;   
	float lonf=0.00;
	float latf=0.00;
	
	Hal_Rtc_Gettime(&rtc_curr);
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_IND_ALARM;
	
	req_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req_s);
	#if USE_LV_WATCH_SPAIN != 0
	if(type == LOCATION_TYPE_SOS)
		sprintf(valueStr, "%d", 3);  //sos报警
	else if(type == LOCATION_TYPE_FALL)
		sprintf(valueStr, "%d", 4);  //跌倒报警
	else if(type == LOCATION_TYPE_CHARGER_CONNECTED)
		sprintf(valueStr, "%d", 5);  //充电器连接
	else if(type == LOCATION_TYPE_CHARGER_DISCONNECTED)
		sprintf(valueStr, "%d", 6);  //充电器断开
	else if(type == LOCATION_TYPE_BATTERY_LOW)
		sprintf(valueStr, "%d", 7);  //低电报警
	else if(type == LOCATION_TYPE_CHARGER_COMPLETE)
		sprintf(valueStr, "%d", 8);  //低电报警
	#else
	sprintf(valueStr, "%d", type);
	#endif
	ws_cJSON_AddStringToObject(req_s,STRING_TYPE,valueStr);

	sprintf(valueStr, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,
			rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);
	ws_cJSON_AddStringToObject(req_s,"time",valueStr);
#if USE_LV_WATCH_SPAIN == 0
#if USE_CRANE_WATCH_GPS != 0
	GPS_getPostionInfo(&lonf, &latf);	
#endif
    ws_cJSON_AddNumberToObject(req_s,"lat",latf);		
    ws_cJSON_AddNumberToObject(req_s,"lon", lonf);
	
	ws_cJSON_AddStringToObject(req_s,"mode","5");
#endif  //USE_LV_WATCH_SPAIN

    CWatchService_SendJsonData(pme, root);
}


static void CWatchService_SendPbRspCmd(ws_client * pme, uint16_t pkId, uint16_t pkCount)
{
    ws_cJSON  *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};	
	ws_cJSON *response_s = NULL;
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_PHB;
	
	response_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,response_s);

	ws_cJSON_AddStringToObject(response_s,STRING_RESULT, "1");	
	ws_cJSON_AddNumberToObject(response_s,"pkId", pkId);
	ws_cJSON_AddNumberToObject(response_s,"pkCount", pkCount);

    CWatchService_SendJsonData(pme, root);
}
#if USE_LV_WATCH_SPAIN != 0
void PositionToJson(ws_kaer_t *userData, ws_cJSON *LocationJs, ws_location* location, LOCATION_UP_TYPE UpType)
{
    char Imei[15] = {0};
    uint8_t sflag = 0;
    MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, Imei);
    
    float lonf = 0.00;
    float latf = 0.00;
    float accuracy = 0.00;
    #if USE_LV_WATCH_GOHOME_WIFIMAC != 0
    uint8_t is_gohomemac = 0;
    #endif
    
    // 添加基本设备信息
    ws_cJSON_AddStringToObject(LocationJs, "imei", Imei);
    
    // 创建位置类型数组
    ws_cJSON *location_types = ws_cJSON_CreateArray();
    ws_cJSON_AddItemToObject(LocationJs, "location_types", location_types);
    
    // GPS定位信息处理
    ws_cJSON *gps_info = NULL;
    if(UpType == LOCATION_UP_TYPE_GPS || UpType == LOCATION_UP_TYPE_WIFI)
    {
		if(GetGpsOnceFixed() == 2 && userData->mGpsON == 1) {
			userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_GPS;
			// if(GetGpsOnceFixed() == 2) {
				GPS_getPostionInfo(&lonf, &latf, &accuracy);
				userData->mHisLocationInfo.mLastUpLon = lonf;
				userData->mHisLocationInfo.mLastUpLat = latf;
				WS_PRINTF("PositionToJson()GPS location lonf is %f latf is %f accuracy is %f", lonf, latf, accuracy);
			// }
			userData->mHisLocationInfo.mLastUpLon = lonf;
			userData->mHisLocationInfo.mLastUpLat = latf;
			WS_PRINTF("PositionToJson()GPS location lonf is %f latf is %f", lonf, latf);

		    if((lonf != 0) || (latf != 0))
			{
				ws_cJSON_AddItemToArray(location_types, ws_cJSON_CreateString("gps"));
				gps_info = ws_cJSON_CreateObject();
				ws_cJSON_AddItemToObject(LocationJs, "gps", gps_info);
				ws_cJSON_AddNumberToObject(gps_info, "lat", latf);        
				ws_cJSON_AddNumberToObject(gps_info, "lon", lonf);
				ws_cJSON_AddNumberToObject(gps_info, "accuracy", accuracy);
				
				WS_PRINTF("PositionToJson() send GPS location");
			}
		}
    }
    else if((UpType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_GPS))
    {
        lonf = userData->mHisLocationInfo.mLastUpLon;
        latf = userData->mHisLocationInfo.mLastUpLat;
        WS_PRINTF("PositionToJson() send the same GPS location");
    }
    
    // if((lonf != 0) || (latf != 0))
    // {
    //     ws_cJSON_AddItemToArray(location_types, ws_cJSON_CreateString("gps"));
    //     gps_info = ws_cJSON_CreateObject();
    //     ws_cJSON_AddItemToObject(LocationJs, "gps", gps_info);
    //     ws_cJSON_AddNumberToObject(gps_info, "lat", latf);        
    //     ws_cJSON_AddNumberToObject(gps_info, "lon", lonf);
    //     ws_cJSON_AddNumberToObject(gps_info, "accuracy", accuracy);
        
    //     WS_PRINTF("PositionToJson() send GPS location");
    // }
    
    // WiFi定位信息处理
    ws_cJSON *wifi_info = NULL;
    char Macs[650] = {0};
    int has_wifi_data = 0;
    
    if((UpType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_WIFI))
    {
        if(userData->mHisLocationInfo.mLastUpWifi.wifi_count > 0) {
            has_wifi_data = 1;
            
            for(int i=0; i<userData->mHisLocationInfo.mLastUpWifi.wifi_count; i++)
            {
                char Macsi[50] = {0};
                if(i<(userData->mHisLocationInfo.mLastUpWifi.wifi_count - 1))
                    sprintf(Macsi,"%s,%s,ssid_%d|",userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi,i);
                else
                    sprintf(Macsi,"%s,%s,ssid_%d",userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi,i);
                
                strcat(Macs, Macsi);
            }
            
            #if USE_LV_WATCH_GOHOME_WIFIMAC != 0
            is_gohomemac = CWathcService_IsGohome_WifiMac(Macs);
            #endif
            
            WS_PRINTF("PositionToJson() send the same WIFI location");
        }
    }
    else if((UpType == LOCATION_UP_TYPE_WIFI) && (location->wifi) && (location->wifi->wifi_count > 2))
    {
        has_wifi_data = 1;
        userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_WIFI;
        
        for(int i=0; i<location->wifi->wifi_count; i++)
        {
            char Macsi[50] = {0};
            if(i<(location->wifi->wifi_count - 1))
                sprintf(Macsi,"%s,%s,ssid_%d|",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);
            else
                sprintf(Macsi,"%s,%s,ssid_%d",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);
            
            strcat(Macs, Macsi);
            strcpy(userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac, location->wifi->wifs[i].mac);
            strcpy(userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi, location->wifi->wifs[i].rssi);
        }
        userData->mHisLocationInfo.mLastUpWifi.wifi_count = location->wifi->wifi_count;
        
        #if USE_LV_WATCH_GOHOME_WIFIMAC != 0
        is_gohomemac = CWathcService_IsGohome_WifiMac(Macs);
        #endif
        
        WS_PRINTF("PositionToJson() WIFI location");
    }
    
    if(has_wifi_data) {
        ws_cJSON_AddItemToArray(location_types, ws_cJSON_CreateString("wifi"));
        wifi_info = ws_cJSON_CreateObject();
        ws_cJSON_AddItemToObject(LocationJs, "wifi", wifi_info);
        ws_cJSON_AddStringToObject(wifi_info, "macs", Macs);
    }
    
    // 处理基站信息(LBS)
    ws_cJSON *lbs_info = NULL;
    char Bts[650] = {0};
    int pos = 0;
    int has_lbs_data = 0;
    
    if(location->cellType == WS_CELL_TYPE_GSM || location->cellType == WS_CELL_TYPE_LTE) {
        has_lbs_data = 1;
        // 如果没有其他定位数据，将LBS标记为最后使用的定位类型
        if(!gps_info && !wifi_info) {
            userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_LBS;
        }
        
        lbs_info = ws_cJSON_CreateObject();
        ws_cJSON_AddItemToObject(LocationJs, "lbs", lbs_info);
        ws_cJSON_AddItemToArray(location_types, ws_cJSON_CreateString("lbs"));
        
        if(location->cellType == WS_CELL_TYPE_GSM) {
            sprintf(Bts, "%d,%d,%d,%d,%d", location->gsm.cells[0].mcc, location->gsm.cells[0].mnc, 
                    location->gsm.cells[0].lac, location->gsm.cells[0].cell_id, location->gsm.cells[0].rxlev);
            ws_cJSON_AddStringToObject(lbs_info, "type", "gsm");
            ws_cJSON_AddStringToObject(lbs_info, "bts", Bts);
            
            char NearBts[650] = {0};
            int near_pos = 0;
            
            for(int i=1; i<location->gsm.num; i++) {
                if((0 == location->gsm.cells[i].cell_id) && 
                   (location->gsm.cells[i].mnc == location->gsm.cells[i].cell_id) && 
                   (location->gsm.cells[i].mcc == location->gsm.cells[i].mnc) && 
                   (location->gsm.cells[i].lac == location->gsm.cells[i].cell_id)) {
                    continue; // 删除全0数据
                }
                
                if(i < (location->gsm.num - 1)) {
                    sflag = 1;
                    near_pos += sprintf(NearBts + near_pos, "%d,%d,%d,%d,%d|", 
                                      location->gsm.cells[i].mcc, location->gsm.cells[i].mnc,
                                      location->gsm.cells[i].lac, location->gsm.cells[i].cell_id, 
                                      location->gsm.cells[i].rxlev);
                } else {
                    sflag = 1;
                    near_pos += sprintf(NearBts + near_pos, "%d,%d,%d,%d,%d",
                                      location->gsm.cells[i].mcc, location->gsm.cells[i].mnc,
                                      location->gsm.cells[i].lac, location->gsm.cells[i].cell_id, 
                                      location->gsm.cells[i].rxlev);
                }
            }
            
            if(sflag == 1) {
                ws_cJSON_AddStringToObject(lbs_info, "nearbts", NearBts);
            }
            
        } else if(location->cellType == WS_CELL_TYPE_LTE) {
            sprintf(Bts, "%d,%d,%d,%d,%d", location->lte.scell.mcc, location->lte.scell.mnc,
                    location->lte.scell.tac, location->lte.scell.cellid, location->lte.scell.rxlev);
            ws_cJSON_AddStringToObject(lbs_info, "type", "lte");
            ws_cJSON_AddStringToObject(lbs_info, "bts", Bts);
            
            if(location->lte.num > 0) {
                char NearBts[650] = {0};
                int near_pos = 0;
                
                for(int i=0; i<location->lte.num; i++) {
                    if((0 == location->lte.cells[i].cellid) && 
                       (location->lte.cells[i].mnc == location->lte.cells[i].cellid) && 
                       (location->lte.cells[i].mcc == location->lte.cells[i].mnc) && 
                       (location->lte.cells[i].tac == location->lte.cells[i].cellid)) {
                        continue; // 删除全0数据
                    }
                    
                    if(i < (location->lte.num - 1)) {
                        sflag = 1;
                        near_pos += sprintf(NearBts + near_pos, "%d,%d,%d,%d,%d|",
                                          location->lte.cells[i].mcc, location->lte.cells[i].mnc,
                                          location->lte.cells[i].tac, location->lte.cells[i].cellid, 
                                          location->lte.cells[i].rssi);
                    } else {
                        sflag = 1;
                        near_pos += sprintf(NearBts + near_pos, "%d,%d,%d,%d,%d",
                                          location->lte.cells[i].mcc, location->lte.cells[i].mnc,
                                          location->lte.cells[i].tac, location->lte.cells[i].cellid, 
                                          location->lte.cells[i].rssi);
                    }
                }
                
                if(sflag == 1) {
                    ws_cJSON_AddStringToObject(lbs_info, "nearbts", NearBts);
                }
            }
        }
    }
}
#else
void PositionToJson(ws_kaer_t *userData,ws_cJSON *LocationJs, ws_location* location, LOCATION_UP_TYPE UpType)
{
 	char Imei[15] = {0};
	uint8_t sflag = 0;
	MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, Imei);
	//ws_location* location = &userData->location;
#if USE_CRANE_WATCH_GPS != 0
	WS_PRINTF("PositionToJson()GetGpsOnceFixed is %d UpType is %d",GetGpsOnceFixed(),UpType); 
#endif
	float lonf=0.00;
    float latf=0.00;
	#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
	uint8_t is_gohomemac = 0;
	#endif

	if(UpType == LOCATION_UP_TYPE_GPS)
	{
		userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_GPS;
#if USE_CRANE_WATCH_GPS != 0
        GPS_getPostionInfo(&lonf, &latf);
#endif
        userData->mHisLocationInfo.mLastUpLon = lonf;
        userData->mHisLocationInfo.mLastUpLat = latf;
        WS_PRINTF("PositionToJson()GPS location lonf is %d latf is %d",lonf,latf);
		
	}
	else if((UpType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_GPS))
	{
		lonf = userData->mHisLocationInfo.mLastUpLon;
        latf = userData->mHisLocationInfo.mLastUpLat;
        WS_PRINTF("PositionToJson() send the same GPS location");
	}
	if((lonf != 0) || (latf != 0))
	{
		ws_cJSON_AddStringToObject(LocationJs,"mode","4");
		ws_cJSON_AddStringToObject(LocationJs,"imei",Imei);
		ws_cJSON_AddNumberToObject(LocationJs,"lat",latf);		
		ws_cJSON_AddNumberToObject(LocationJs,"lon", lonf);
		WS_PRINTF("PositionToJson() send  GPS location");
		
	}
	
#if 1
	else if((UpType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_WIFI))	
	{
    
        char Macs[650] = {0};
       
 
        ws_cJSON_AddStringToObject(LocationJs,"mode","3");
        ws_cJSON_AddStringToObject(LocationJs,"imei",Imei);
    	
        for(int i=0; i<userData->mHisLocationInfo.mLastUpWifi.wifi_count; i++)
        {
            char Macsi[50] = {0};
            if(i<(userData->mHisLocationInfo.mLastUpWifi.wifi_count - 1))
                sprintf(Macsi,"%s,%s,ssid_%d|",userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi,i);
            else
                sprintf(Macsi,"%s,%s,ssid_%d",userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi,i);

            strcat(Macs,Macsi);
        
        }
        ws_cJSON_AddStringToObject(LocationJs,"macs",Macs);
		#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
		is_gohomemac=CWathcService_IsGohome_WifiMac(Macs);
		#endif

        WS_PRINTF("PositionToJson() send the same WIFI location");
        
    }
    else if((UpType == LOCATION_UP_TYPE_WIFI) && 
   			 (location->wifi)&&
   			 (location->wifi->wifi_count>2))
    {
    
        char Macs[650] = {0};
        userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_WIFI;
 
        ws_cJSON_AddStringToObject(LocationJs,"mode","3");
        ws_cJSON_AddStringToObject(LocationJs,"imei",Imei);
    
        for(int i=0; i<location->wifi->wifi_count; i++)
        {
            char Macsi[50] = {0};
            if(i<(location->wifi->wifi_count - 1))
                sprintf(Macsi,"%s,%s,ssid_%d|",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);
            else
                sprintf(Macsi,"%s,%s,ssid_%d",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);

            strcat(Macs,Macsi);
            strcpy(userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac, location->wifi->wifs[i].mac);  //澶囦唤wifi
			strcpy(userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi, location->wifi->wifs[i].rssi);
        }
        userData->mHisLocationInfo.mLastUpWifi.wifi_count = location->wifi->wifi_count;
        ws_cJSON_AddStringToObject(LocationJs,"macs",Macs);

        WS_PRINTF("PositionToJson()WIFI location");
		#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
		is_gohomemac=CWathcService_IsGohome_WifiMac(Macs);
		#endif
    }
 	else
#endif

#if USE_LV_WATCH_LOCATION_BLEMAC != 0
	if(UpType == LOCATION_UP_TYPE_BLE)
	{
		ws_cJSON_AddStringToObject(LocationJs,"macs",userData->BLE_Addr);
		ws_cJSON_AddStringToObject(LocationJs,"gpsAddType","3");
		return;
	}
	else
#endif
 	{
 		userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_LBS;
		ws_cJSON_AddStringToObject(LocationJs,"mode","2");
		ws_cJSON_AddStringToObject(LocationJs,"imei",Imei);
		
		WS_PRINTF("PositionToJson()station location");
 	}

/*GSP WIFI 閮藉甫涓奓BS淇℃伅*/
	char Bts[650] = {0};
	int pos = 0;
	if(location->cellType ==  WS_CELL_TYPE_GSM)
	{
		sprintf(Bts, "%d,%d,%d,%d,%d",location->gsm.cells[0].mcc, location->gsm.cells[0].mnc, 
		location->gsm.cells[0].lac, location->gsm.cells[0].cell_id, location->gsm.cells[0].rxlev);
		ws_cJSON_AddStringToObject(LocationJs,"bts",Bts);

		for(int i=1; i<location->gsm.num; i++)
        {
        	if((0 == location->gsm.cells[i].cell_id) && (location->gsm.cells[i].mnc == location->gsm.cells[i].cell_id) && (location->gsm.cells[i].mcc == location->gsm.cells[i].mnc) && (location->gsm.cells[i].lac == location->gsm.cells[i].cell_id) )
        	{//delete 0,0,0,0 
				continue;
			}
        	if(i < (location->gsm.num - 1))
        	{
        		sflag = 1;
	        	pos += sprintf(Bts+pos,"%d,%d,%d,%d,%d|",location->gsm.cells[i].mcc, location->gsm.cells[i].mnc, 
				location->gsm.cells[i].lac, location->gsm.cells[i].cell_id, location->gsm.cells[i].rxlev); // 鍖哄煙鐮? 鍩虹珯缂栧彿, 淇″彿寮哄害
        	}
			else
			{
				sflag = 1;
				pos += sprintf(Bts+pos,"%d,%d,%d,%d,%d",location->gsm.cells[i].mcc, location->gsm.cells[i].mnc, 
				location->gsm.cells[i].lac, location->gsm.cells[i].cell_id, location->gsm.cells[i].rxlev);
			}
			if(sflag == 1)
				ws_cJSON_AddStringToObject(LocationJs,"nearbts",Bts);
			sflag = 0;
        }
   #if EVERY_TIME_UP_LBS != 0
		WS_PRINTF("Send gsm Lbs mcc is %d mnc is %d lac is %d,cell_id is %d rxlev is %d",location->gsm.cells[0].mcc,location->gsm.cells[0].mnc,
		location->gsm.cells[0].lac,location->gsm.cells[0].cell_id, location->gsm.cells[0].rxlev);
	#endif
	}
	else if(location->cellType ==  WS_CELL_TYPE_LTE)
	{
		sprintf(Bts, "%d,%d,%d,%d,%d",location->lte.scell.mcc, location->lte.scell.mnc, 
		location->lte.scell.tac, location->lte.scell.cellid, location->lte.scell.rxlev);
		ws_cJSON_AddStringToObject(LocationJs,"bts",Bts);
		if(location->lte.num > 0)
		{	
			for(int i=0; i<location->lte.num; i++)
	        {
	        	if((0 == location->lte.cells[i].cellid) && (location->lte.cells[i].mnc == location->lte.cells[i].cellid) && (location->lte.cells[i].mcc == location->lte.cells[i].mnc) && (location->lte.cells[i].tac == location->lte.cells[i].cellid) )
	        	{//delete 0,0,0,0 
					continue;
				}
	        	if(i < (location->lte.num - 1))
	        	{
	        		sflag = 1;
		        	pos += sprintf(&Bts[pos],"%d,%d,%d,%d,%d|",location->lte.cells[i].mcc, location->lte.cells[i].mnc,
					location->lte.cells[i].tac, location->lte.cells[i].cellid, location->lte.cells[i].rssi); // 鍖哄煙鐮? 鍩虹珯缂栧彿, 淇″彿寮哄害
	        	}
				else
				{
					sflag = 1;
					pos += sprintf(&Bts[pos],"%d,%d,%d,%d,%d",location->lte.cells[i].mcc, location->lte.cells[i].mnc,
					location->lte.cells[i].tac, location->lte.cells[i].cellid, location->lte.cells[i].rssi);
				}
	        }
			if(sflag == 1)
				ws_cJSON_AddStringToObject(LocationJs,"nearbts",Bts);
			sflag = 0;
		}
	#if EVERY_TIME_UP_LBS != 0
		WS_PRINTF("Send lte Lbs mcc is %d mnc is %d lac is %d,cell_id is %d rxlev is %d",location->lte.scell.mcc,location->lte.scell.mnc,
		location->lte.scell.tac,location->lte.scell.cellid,location->lte.scell.rxlev);    
	#endif
	}

	#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
	if(is_gohomemac == 1)
	{
		WS_PRINTF("+++++++++++PositionToJson gohomeeeee");
		is_gohomemac = 0;
		ws_cJSON_AddStringToObject(LocationJs,"gpsAddType","2");

		#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
		WS_PRINTF("!!!!!!!!!!!!!PositionToJson opne=%d",g_high_freq_s.high_freq_open);
		//not cur week,clear
		if(g_high_freq_s.high_freq_open == 1)
		{
			g_high_freq_s.high_freq_open = 0;
			UI_NV_Write_Req(NV_SECTION_HIGH_FREQ_LOC_INFO, 0, sizeof(nv_watch_high_freq_flag_t), (uint8_t *)&g_high_freq_s);
		}
		#endif

	}
	#endif
	#if USE_LV_WATCH_LANGUAGE_SUPPORT_E != 0
	if((0 == lv_lang_act()))
	{
		//englins ,use google 
		ws_cJSON_AddStringToObject(LocationJs,"api","google");
	}
	#endif

}
#endif
static void CWatchService_AlarmIndCmdCb(void *pUser, uint32_t par)
{
	ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;
	WS_PRINTF("CWatchService_AlarmIndCmdCb--%x\n", par);
	
    ws_cJSON  *root = NULL;    
	//ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};	
	ws_cJSON *req_s = NULL;
	hal_rtc_t rtc_curr;   
	float lonf=0.00;
	float latf=0.00;
	
	Hal_Rtc_Gettime(&rtc_curr);
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_IND_ALARM;
	
	req_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req_s);
	
	sprintf(valueStr, "%d", par);  
	ws_cJSON_AddStringToObject(req_s,STRING_TYPE,valueStr);

	sprintf(valueStr, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,
			rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);
	ws_cJSON_AddStringToObject(req_s,"time",valueStr);

	PositionToJson(userData, req_s, loc_data, 0);

    CWatchService_SendJsonData(pme, root);
   
    userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;

}

static void CWatchService_CommonTimerCB(uint32_t pUser)
{
   ws_client * pme = (ws_client*)pUser;    
   ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));  /*??????*/
   if(cmd_info)
   {
	   memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	   cmd_info->client = pme;
	   cmd_info->evt = CMD_LOCAL_COMMON_TIMER_EXPIRE;
	   CWatchService_NofityClient(pme, cmd_info);
	   free(cmd_info);
   	}		
}

static void CWatchService_CheckbeatTimeOutCB(uint32_t pUser)
{
    ws_client * pme = (ws_client*)pUser;    
	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));	/*命令数据结构*/
	if(cmd_info)
	{
	    memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	    cmd_info->client = pme;
	    cmd_info->evt = CMD_LOCAL_START_BEAT_CHECKOUT_TIMER;
	    CWatchService_NofityClient(pme, cmd_info);
	    free(cmd_info);
	}
}

static void CWatchService_CheckbeatTimeOutExpire(ws_client * pme)
{
		
	WS_PRINTF("CWatchService--CWatchService_CheckbeatTimeOutExpire >> send beat timeout!! \n", 0, 0, 0);		
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

    WS_PRINTF("CWatchService_CheckbeatTimeOutExpire()heartTime=%d", userData->m_base.ws_reg.heartTime);
	hearttick_expire_cnt++;
   if(hearttick_expire_cnt >=2)
   {
   		hearttick_expire_cnt = 0;
		MMI_ModemAdp_WS_Stop_Ext();

	    uint8_t time = 0;
	    while(time < 10)
		{
			if(!MMI_ModemAdp_WS_Is_Online())
			{
				uos_sleep(200);
				MMI_ModemAdp_WS_Start(pme);
				return;
			}
			time++;
			uos_sleep(200);
		}

   }
   else
   	{
		if(userData->m_base.is_connected == TRUE)
	    {
	        CWatchService_CmdProcess((ws_client*)pme, CMD_C2S_PONG, NULL, 0);
	    }
		CWatchService_HeartbeatStart(pme);
   }
}


static void CWatchService_HeartbeatTimerCB(uint32_t pUser)
{
    ws_client * pme = (ws_client*)pUser;    
	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));	/*命令数据结构*/
	if(cmd_info)
	{
	    memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	    cmd_info->client = pme;
	    cmd_info->evt = CMD_LOCAL_START_BEAT_LISTENER_TIMER;
	    CWatchService_NofityClient(pme, cmd_info);
	    free(cmd_info);
	}    
}

static void CWatchService_HeartbeatTimerExpire(ws_client * pme)
{

    WS_PRINTF("CWatchService--CWatchService_HeartbeatTimerExpire >>  beating ",  0, 0);
    uos_timer_stop(pme->adapter->timer_beat);
    uos_timer_start(pme->adapter->timer_beat, 30*TICKES_IN_SECOND, 0, CWatchService_CheckbeatTimeOutCB, (uint32_t)pme);

    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    
    if(userData->m_base.is_connected == TRUE)
    {
        CWatchService_CmdProcess((ws_client*)pme, CMD_C2S_PONG, NULL, 0);
    }
}

void CWatchService_HeartbeatStop(ws_client *pme)
{
    WS_PRINTF("CWatchService--CWatchService_HeartbeatStop >> stop beat timer\n", 0, 0, 0);
    
    uos_timer_stop(pme->adapter->timer_beat);

    return ;
}

static void CWatchService_HeartbeatStart(ws_client *pme)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

    WS_PRINTF("$$$$$$$$$$$$$$$$$$$$$$CWatchService--CWatchService_HeartbeatStart() heartTime=%d \n", userData->m_base.ws_reg.heartTime);
    
	uos_timer_stop(pme->adapter->timer_beat);
	uos_timer_start(pme->adapter->timer_beat, userData->m_base.ws_reg.heartTime*TICKES_IN_SECOND, 0, CWatchService_HeartbeatTimerCB, (uint32_t)pme);
    return ;
}

static void CWatchService_UpdateBatteryInfor(ws_client * pme)
{
	#if USE_LV_WATCH_LOCK_PHONE !=0
	if(check_iccid_is_same()==0)
	{
		WS_PRINTF("CWatchService_UpdateBatteryInfor >> check_iccid_is_same=0++++++++++++++++");
		return;
	}
	#endif
   	static int8_t percent_bak = 100;
     ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
     
     WS_PRINTF("CWatchService--CWatchService_UpdateBatteryInfor()\n");
     
    if((userData->m_base.is_connected == TRUE) &&
		(WatchUtility_GetWsSwitch()->batteryLow == 1))
    {
        int8_t percent = Hal_Battery_Get_Status();       
		if(percent == 10 || percent == 5)
       	{
       		if(percent_bak > percent)  //鎶ヨ涓婃姤
       		{
       			CWatchService_SendBatteryInfo(pme, NULL); 
       		}			
       	}
	   percent_bak = percent;
    }
    
}

#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
static void CWatchService_LinkReqDataJson_register(ws_cJSON  *root,ws_regInfo *reg)
{
	ws_cJSON *req = NULL;
	char valueStr[30]={0};
	
	req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,"req",req);
	ws_cJSON_AddStringToObject(req,"devType","1060");
	ws_cJSON_AddStringToObject(req,"modelName",KAER_MODEL_NAME);
	sprintf(valueStr, "%s", reg->sn);
	ws_cJSON_AddStringToObject(req,"identity",valueStr);
	ws_cJSON_AddNumberToObject(req,"keepAlive",1);
	#if USE_LV_WATCH_LANGUAGE_SUPPORT_E != 0
	ws_cJSON_AddStringToObject(req,"plat","bxxing");//"218cs");
	#elif USE_LV_WATCH_WEILIAO_52 != 0
	ws_cJSON_AddStringToObject(req,"plat","baxing");//"218cs");
	#elif USE_LV_WATCH_KEY_KW19 != 0
	ws_cJSON_AddStringToObject(req,"plat","baxing");//"218cs");
	#else
	ws_cJSON_AddStringToObject(req,"plat","baxing");//"218cs");
	#endif

}

uint8_t CWatchService_GetProtoServerInfo(void)
{
	char UrlPort[100] = {0};
	uint8_t ret = 0;
	ret = myNVMgr_GetWsUrlPort(UrlPort);
		
	char *Index = strstr(UrlPort, "@");
	if(Index == NULL)
		return 0;
	else
		return 1;
}

void CWatchService_ClearProtoServerInfo()
{
	if(CWatchService_GetProtoServerInfo() == 1)
	{
		memset(sUrl,0,sizeof(sUrl));
		server.port =0;
		myNVMgr_SetWsUrlPort(sUrl);
	}
}
#endif

static void CWatchService_LinkReqDataJson(ws_cJSON  *root,ws_regInfo *reg)
{
	ws_cJSON *req = NULL;
	char valueStr[30]={0};
	
	req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req);
	ws_cJSON_AddStringToObject(req,"modelName",KAER_MODEL_NAME);
	sprintf(valueStr, "%s", reg->sn);
	ws_cJSON_AddStringToObject(req,"identity",valueStr);

	ws_cJSON_AddNumberToObject(req,"keepAlive",1);
	ws_cJSON_AddStringToObject(req,"devType","1032");
	ws_cJSON_AddStringToObject(req,"softver", app_adaptor_get_version());
	ws_cJSON_AddStringToObject(req,"iccid",app_adaptor_get_iccid_info_req());
}

static void CWatchService_DevLogin(ws_client * pme ,ws_regInfo *reg)
{
    ws_cJSON *root = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    userData->type = KAER_CMD_LOGIN;
    root = ws_cJSON_CreateObject();
    if(NULL == root || NULL == reg)
    {
        return;
    }

	char *Iccid = NULL;
	MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, userData->m_base.ws_reg.sn);
	userData->m_base.ws_reg.sn[15] = 0;
	MMI_Modem_Query_Imsi_Req(MMI_MODEM_SIM_1, userData->m_base.ws_reg.imsi);
	Iccid = app_adaptor_get_iccid_info_req();
	if(Iccid != NULL)
	{
		strcpy(userData->mIccid, Iccid);
		WS_PRINTF("Iccid is %s", userData->mIccid);
		lv_mem_free(Iccid);
	}
#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
	if(CWatchService_GetProtoServerInfo() == 0)
	{
	//first register
		CWatchService_LinkReqDataJson_register(root, reg);
		CWatchService_SendJsonData(pme, root);
		return;
	}
#endif

	CWatchService_LinkReqDataJson(root, reg);
    CWatchService_SendJsonData(pme, root);
}  

static void CWatchService_SendCmdPong(ws_client * pme, ws_pong_extra *sensor_info, double seq)
{
    ws_cJSON *root = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_BAT_INF;
    WS_PRINTF("CWatchService_SendCmdPong()");

    CWatchService_SendJsonData(pme, root);
}

static void CWatchService_BatteryReqDataJson(ws_cJSON  *root)
{
	ws_cJSON *req = NULL;
	uint16_t percent=0;
	#if USE_LV_WATCH_SPAIN == 1
	uint16_t Charging_sta=0;
	uint16_t signalType=0;
	uint16_t satellites=0;
	MMI_MODEM_PLMN_RAT rat;
	watch_modem_get_operator_req(&rat);
	if(MMI_MODEM_PLMN_RAT_LTE == rat) {
		printf("4G");
		signalType=3;
	} else if(MMI_MODEM_PLMN_RAT_GSM == rat) {
		printf("2G");
		signalType=1;
	} else if(MMI_MODEM_PLMN_RAT_UMTS == rat){
		printf("3");
		signalType=2;
	}
	int signal_bar=watch_modem_get_signal_bar_req();
	signal_bar=signal_bar*20;
	
	Charging_sta=Hal_Get_Charging_Complete_State();
	uint8_t Data[5] = {0};
	GetGpsData(Data);
	satellites=Data[0]+Data[1]+Data[2]+Data[3]+Data[4];
	#endif
	percent = Hal_Battery_Get_Status();
	gsensor_get_steps_and_check_save(); //主动更新检查一下步数数据
	req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req);
	printf("$$$$$$===CWatchService_BatteryReqDataJson,%d\n",percent);
	ws_cJSON_AddNumberToObject(req,"battery",percent);
	#if USE_LV_WATCH_SPAIN == 1
	// ws_cJSON_AddNumberToObject(req,"status",Charging_sta);
	ws_cJSON_AddNumberToObject(req,"signalLevel",signal_bar);
	ws_cJSON_AddNumberToObject(req,"signalType",signalType);	
	ws_cJSON_AddNumberToObject(req,"satellites",satellites);
	ws_cJSON_AddNumberToObject(req,"steps",mmi_get_steps_count());
	#endif
}

static void CWatchService_SendBatteryInfo(ws_client * pme, ws_batInfo *battery)
{
    ws_cJSON *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    userData->type = KAER_CMD_BAT_INF;
 
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }   

	CWatchService_BatteryReqDataJson(root); 

    CWatchService_SendJsonData(pme, root);
}

static void CWatchService_VersionReqDataJson(ws_cJSON  *root)
{
	ws_cJSON *req = NULL;
	char *ptr=app_adaptor_get_iccid_info_req();
	char  iccid_ptr[35]={0};
	if(ptr == NULL)
	{
		ws_printf("get iccid err");
		strcpy(iccid_ptr,"");
	}
	else
	{
		strcpy(iccid_ptr,ptr);
		lv_mem_free(ptr);
	}
	req = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req);
	ws_cJSON_AddStringToObject(req,"softver", app_adaptor_get_version());		
	ws_cJSON_AddStringToObject(req,"iccid",iccid_ptr);
    #if DUER_CUST_KAER != 0	
	char cuid[70]={0};
	duer_generate_device_id(cuid);
	ws_cJSON_AddStringToObject(req,"cuid",cuid);	
    #endif
	#if USE_LV_WATCH_KEY_KW19 != 0
	ws_cJSON_AddStringToObject(req,"hardver","KW19_V1.0");
	#endif

}

static void CWatchService_SendVersionInfo(ws_client * pme, ws_batInfo *battery)
{
    ws_cJSON *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    userData->type = KAER_CMD_VER_INF;
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }  

	CWatchService_VersionReqDataJson(root);

    CWatchService_SendJsonData(pme, root);
}

#if 1
static void CWatchService_LocationDataJson(ws_kaer_t *userData,ws_cJSON  *root, ws_location *location, LOCATION_UP_TYPE UpType)
{
	ws_cJSON *LocationJs = ws_cJSON_CreateObject();

	ws_cJSON_AddItemToObject(root,STRING_REQ,LocationJs);
	
	PositionToJson(userData, LocationJs, location, UpType);

	uint16_t percent=0;
	static uint16_t BakPercent = 100;
	percent = Hal_Battery_Get_Status();
	gsensor_get_steps_and_check_save(); //主动更新检查一下步数数据 
	ws_cJSON_AddNumberToObject(LocationJs,"step",mmi_get_steps_count());	
	
	ws_cJSON_AddNumberToObject(LocationJs,"battery",percent);	
	char BatteryStatus = 3;
	if(Hal_Charger_Get_Status() == HAL_CHG_CONNECTED)
	{
		BatteryStatus = 2;
		if(percent < BakPercent)
		{
			percent = BakPercent;
		}
		
	}
	else if(Hal_Charger_Get_Status() == HAL_CHG_DISCONNECTED)
	{
		BatteryStatus = 3;
		
		if(percent > BakPercent)
		{
			percent = BakPercent;
		}				
	}
	BakPercent = percent;
	ws_cJSON_AddNumberToObject(LocationJs,"batteryStatus",BatteryStatus);	
	
}
#else
static void CWatchService_LocationDataJson(ws_cJSON  *root, ws_location *location)
{	
    hal_rtc_t rtc_curr;   
    Hal_Rtc_Gettime(&rtc_curr);
    static int SendFlg = 0;
    char Imei[15] = {0};
    MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, Imei);
    // if((location->gps) && (SendFlg == 0))
    if(SendFlg == 1)
    {
        ws_cJSON *gps = NULL;
        char valueStr[30]={0};
        float lonf=0.00;
        float latf=0.00;
        
        GPS_getPostionInfo(&lonf, &latf);
        gps = ws_cJSON_CreateObject();
        ws_cJSON_AddItemToObject(root,STRING_REQ,gps);
        ws_cJSON_AddStringToObject(gps,"mode","4");
        ws_cJSON_AddStringToObject(gps,"imei",Imei);
        ws_cJSON_AddNumberToObject(gps,"lat",latf);		
        ws_cJSON_AddNumberToObject(gps,"lon", lonf);
        SendFlg = 2;
        return;
		//sprintf(valueStr, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,
		//	rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);
	//	ws_cJSON_AddStringToObject(gps,"time",valueStr);		
    }

    if((location->wifi) && (SendFlg == 0))
    {
        SendFlg = 1;
        if(location->wifi->wifi_count>0)
        {
            char Macs[500] = {0};
            ws_cJSON *Wifis = ws_cJSON_CreateObject();
            
            ws_cJSON_AddItemToObject(root,STRING_REQ,Wifis);
            ws_cJSON_AddStringToObject(Wifis,"mode","3");
            ws_cJSON_AddStringToObject(Wifis,"imei",Imei);
        
            for(int i=0; i<location->wifi->wifi_count; i++)
            {
                //ws_cJSON *wf = NULL;
                char Macsi[50] = {0};
				if(i<(location->wifi->wifi_count - 1))
					sprintf(Macsi,"%s,%s,ssid_%d|",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);
				else
					sprintf(Macsi,"%s,%s,ssid_%d",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);

				strcat(Macs,Macsi);

              /*  wf = ws_cJSON_CreateObject();
                ws_cJSON_AddStringToObject(wf, "mac", location->wifi->wifs[i].mac);
                ws_cJSON_AddStringToObject(wf, "signal", location->wifi->wifs[i].rssi);
                ws_cJSON_AddStringToObject(wf, "ssid", location->wifi->wifs[i].ap_name);
                ws_cJSON_AddItemToArray(wifis, wf);
                */
            }

			ws_cJSON_AddStringToObject(Wifis,"macs",Macs);
           // ws_cJSON_AddItemToObject(root, "wifis",wifis);
        }
		return;
    }
	if(SendFlg != 2)
	{
		return;
	}
	SendFlg = 0;
	/*
    if(location->cellType ==  WS_CELL_TYPE_CDMA)
    {
        ws_cJSON *telecomcell = NULL;
        telecomcell = ws_cJSON_CreateObject();
        ws_cJSON_AddItemToObject(root,"telecomcell",telecomcell);
        ws_cJSON_AddNumberToObject(telecomcell,"sid",location->cdma.sid);
        ws_cJSON_AddNumberToObject(telecomcell,"nid",location->cdma.nid);
        ws_cJSON_AddNumberToObject(telecomcell,"bid",location->cdma.baseid);
    }
    else 
    */
    if(location->cellType ==  WS_CELL_TYPE_GSM)
    {
        char Bts[50] = {0};
        ws_cJSON *cells = ws_cJSON_CreateObject();
        
        ws_cJSON_AddItemToObject(root,STRING_REQ,cells);
        ws_cJSON_AddStringToObject(cells,"mode","2");
        ws_cJSON_AddStringToObject(cells,"imei",Imei);

        sprintf(Bts, "%d,%d,%d,%d,%d",location->gsm.cells[0].mcc, location->gsm.cells[0].mnc, 
			location->gsm.cells[0].lac, location->gsm.cells[0].cell_id, location->gsm.cells[0].rxlev);
        ws_cJSON_AddStringToObject(cells,"bts",Bts);
       // for(int i=0; i<location->gsm.num; i++)
       /*
        {
            ws_cJSON *cell = NULL;
            
            cell = ws_cJSON_CreateObject();
            ws_cJSON_AddNumberToObject(cell, "mcc", location->gsm.cells[i].mcc);
            ws_cJSON_AddNumberToObject(cell, "mnc", location->gsm.cells[i].mnc);
            ws_cJSON_AddNumberToObject(cell, "lac", location->gsm.cells[i].lac);
            ws_cJSON_AddNumberToObject(cell, "ci", location->gsm.cells[i].cell_id);
            ws_cJSON_AddNumberToObject(cell, "rxlev", location->gsm.cells[i].rxlev);
            ws_cJSON_AddItemToArray(cells, cell);
        }
        ws_cJSON_AddItemToObject(root, "cells",cells);
        */
    }
    else if(location->cellType ==  WS_CELL_TYPE_LTE)
    {
        char Bts[50] = {0};
        ws_cJSON *cells = ws_cJSON_CreateObject();
        
        ws_cJSON_AddItemToObject(root,STRING_REQ,cells);
        ws_cJSON_AddStringToObject(cells,"mode","2");
        ws_cJSON_AddStringToObject(cells,"imei",Imei);

        sprintf(Bts, "%d,%d,%d,%d,%d",location->lte.scell.mcc, location->lte.scell.mnc, 
			location->lte.scell.tac, location->lte.scell.cellid, location->lte.scell.rxlev);
        ws_cJSON_AddStringToObject(cells,"bts",Bts);
        /*	
        ws_cJSON *cells = NULL;
        ws_cJSON *cell = NULL;
        cells = ws_cJSON_CreateArray();
        cell = ws_cJSON_CreateObject();
        ws_cJSON_AddNumberToObject(cell, "mcc", location->lte.scell.mcc);
        ws_cJSON_AddNumberToObject(cell, "mnc", location->lte.scell.mnc);
        ws_cJSON_AddNumberToObject(cell, "lac", location->lte.scell.tac);
        ws_cJSON_AddNumberToObject(cell, "ci", location->lte.scell.cellid);
        ws_cJSON_AddNumberToObject(cell, "rxlev", location->lte.scell.rxlev);
        ws_cJSON_AddItemToArray(cells, cell);
        ws_cJSON_AddItemToObject(root, "cells",cells);
        */
    }

}
#endif

static int CWatchService_BuildPosData(ws_client * pme, char* data, LOCATION_UP_TYPE LocationType)
{
	if(pme == NULL)
	{
		return;
	}
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_location *location = &(userData->m_base.location); 
    hal_rtc_t rtc_curr;
    int pos = 0;
	char Imei[15] = {0};
	float lonf=0.00;
    float latf=0.00;
    MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, Imei);
    WS_PRINTF("CWatchService_BuildPosData() LocationType is %d last Tpye is %d ", LocationType,userData->mHisLocationInfo.mLastUpType);
    if(data==NULL)
	{
		return 0;
	}
	
	if(LocationType == LOCATION_UP_TYPE_GPS)
	{
		userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_GPS;
        
        //GPS_getTempPostionInfo(&lonf, &latf);
		GPS_getPostionInfo(&lonf, &latf);
        userData->mHisLocationInfo.mLastUpLon = lonf;
        userData->mHisLocationInfo.mLastUpLat = latf;
	}
	else if((LocationType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_GPS))
	{
		lonf = userData->mHisLocationInfo.mLastUpLon;
        latf = userData->mHisLocationInfo.mLastUpLat;
	}
	if((lonf == 0) && (latf == 0)) 
	{
		pos += sprintf(data+pos,"1");								
		pos += sprintf(data+pos,"E000.000000");		
		pos += sprintf(data+pos,"N00.000000"); 		
	}
	else
	{
		pos += sprintf(data+pos,"0");
		if(lonf >= 0)
		{
			pos += sprintf(data+pos,"E");
		}
		else
		{
			pos += sprintf(data+pos,"W");
		}
		pos += sprintf(data+pos,"%.6f", lonf);

		if(latf >= 0)
		{
			pos += sprintf(data+pos,"N");
		}
		else
		{
			pos += sprintf(data+pos,"S");
		}
		pos += sprintf(data+pos,"%.6f", latf);
		
	
		Hal_Rtc_Gettime(&rtc_curr);
		
	//pos += sprintf(data+pos,"T"); 			

		pos += sprintf(data+pos,"T%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year, rtc_curr.tm_mon, rtc_curr.tm_mday,
		rtc_curr.tm_hour, rtc_curr.tm_min, rtc_curr.tm_sec);	// UTC时间
	}
	
	if(userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_LBS    //上报上一次 上一次是基站 不更新基站数据
							&& LocationType == LOCATION_UP_TYPE_LAST)
	{
		location = &userData->mHisLocationInfo.mLastLocationIn;
	}

	//LBS
	if(location->cellType ==  WS_CELL_TYPE_GSM)
	{
		pos += sprintf(data+pos,"@");								//?????
		pos += sprintf(data+pos,"%d!%d!%d!%d!%d", location->gsm.cells[0].mcc, location->gsm.cells[0].mnc, 
			location->gsm.cells[0].lac, location->gsm.cells[0].cell_id, location->gsm.cells[0].rxlev);
		for(int i = 1; i < location->gsm.num; i++)
		{
			pos += sprintf(data+pos,"#");								//?????
			pos += sprintf(data+pos,"%d!%d!%d!%d!%d", location->gsm.cells[i].mcc,location->gsm.cells[i].mnc,
					location->gsm.cells[i].lac,location->gsm.cells[i].cell_id,location->gsm.cells[i].rxlev);
		}
	}
	else if(location->cellType ==  WS_CELL_TYPE_LTE)
	{
		pos += sprintf(data+pos,"@");								//?????
		pos += sprintf(data+pos,"%d!%d!%d!%d!%d", location->lte.scell.mcc,location->lte.scell.mnc,
					location->lte.scell.tac,location->lte.scell.cellid,location->lte.scell.rxlev);
		for(int i = 0; i < location->lte.num; i++)
		{
			pos += sprintf(data+pos,"#");								//?????
			pos += sprintf(data+pos,"%d!%d!%d!%d!%d", location->lte.cells[i].mcc,location->lte.cells[i].mnc,
					location->lte.cells[i].tac,location->lte.cells[i].cellid,location->lte.cells[i].rssi);
		}
		if(LocationType == LOCATION_UP_TYPE_LBS)	// 记录上次是基站上报
		{
			userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_LBS;
		}
	}

	
	
#if LOCATION_ONLY_GPS_ON == 0
	//WIFI
	if((LocationType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_WIFI))
	{
			
		for(int i=0; i<userData->mHisLocationInfo.mLastUpWifi.wifi_count; i++)
		{
		   
		    
			if(i > 0)
		    {
				pos += sprintf(data+pos,"#wifi%d!%s!%s",i,userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi);								//?????
		    }
			else
			{
				pos += sprintf(data+pos,"@wifi%d!%s!%s",i,userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi);								//?????
			}	
		}
	}
    else if( (LocationType == LOCATION_UP_TYPE_WIFI)     
    && (location->wifi)&& (location->wifi->wifi_count>=3))
    {
		int wifi_cnt = (location->wifi->wifi_count>10) ? 10 : location->wifi->wifi_count;	
    	userData->mHisLocationInfo.mLastUpWifi.wifi_count = wifi_cnt;
    	userData->mHisLocationInfo.mLastUpType = LOCATION_UP_TYPE_WIFI;							
        for(int i=0; i<wifi_cnt; i++)
        {
           
            
			if(i > 0)
            {
				pos += sprintf(data+pos,"#wifi%d!%s!%s",i,location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi);								//?????
				
				
            }
			else
			{
				pos += sprintf(data+pos,"@wifi%d!%s!%s",i,location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi);								//?????
			}	
			strcpy(userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac, location->wifi->wifs[i].mac);
			strcpy(userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi, location->wifi->wifs[i].rssi);
        }
    }
    else
    {
    	pos += sprintf(data+pos,"@0");
    }
 #else 
 pos += sprintf(data+pos,"@0");
 #endif 
 	SetGpsOnceFixed(0);
	return strlen(data);
	
}


static void CWatchService_SendSmsInfo(ws_client * pme, ws_sms_info *sms_info)
{
    ws_cJSON  *root = NULL;

    root = ws_cJSON_CreateObject();
    if(NULL == root || NULL == sms_info)
    {
        return;
    }
    
    ws_cJSON_AddNumberToObject(root,"type",KAER_CMD_SMS_UP);
    ws_cJSON_AddNumberToObject(root,"ident",KAER_IDENT);
    ws_cJSON_AddStringToObject(root,"phone",sms_info->phone);
    ws_cJSON_AddStringToObject(root,"sms",sms_info->text);

    CWatchService_SendJsonData(pme, root);
}

#if USE_LV_WATCH_CONFIG_MAINMENU != 0
//id 1=zhifubao 2=rili,3=miaobiao 4-xiaodu zidian 5-xiaodufanyi 6-xiaodu hongse jingdian 7-beidanci 
void CWatchService_Send_function_list(ws_client *pme)
{
	uint8_t i=0,j=0;
	uint8_t sname[200]={0};
	watchLangTxtId_t stextid;
	char tmp[4]={0};

	ws_cJSON *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    userData->type = KAER_CMD_UP_FUNCTION_LIST;
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }  

	ws_cJSON *req=ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req);

	ws_cJSON *list_array = ws_cJSON_CreateArray();
	ws_cJSON_AddItemToObject(req, "list", list_array);
	
	uint32_t cfg_parm = ke_GetMenuConfig();
	
	if(gui_is_lcd_valid() == true)
	{
		uint8_t totalmenu =primary_get_mainmenu_total_number(); 
		for(i=0,j=0;i<totalmenu;i++)
		{
			stextid = primary_get_menuID_text(i);
			#if USE_LV_WATCH_KEY_KW19 != 0
			if(stextid == WATCH_TEXT_ID_UPAY || stextid == WATCH_TEXT_ID_CALENDER || stextid == WATCH_TEXT_ID_STOPWATCH
				|| stextid == WATCH_TEXT_ID_DICTIONARY || stextid == WATCH_TEXT_ID_FOLLOW_READING 
				|| stextid == WATCH_TEXT_ID_INTERPRETER ||stextid == WATCH_TEXT_ID_HONGSEJINGDIAN
				)

			#else
			if(stextid == WATCH_TEXT_ID_UPAY || stextid == WATCH_TEXT_ID_CALENDER || stextid == WATCH_TEXT_ID_STOPWATCH
				|| stextid == WATCH_TEXT_ID_DICTIONARY || stextid == WATCH_TEXT_ID_FOLLOW_READING 
				|| stextid == WATCH_TEXT_ID_INTERPRETER ||stextid == WATCH_TEXT_ID_HONGSEJINGDIAN
				|| stextid == WATCH_TEXT_ID_WEATHER
				)
			#endif
				;
			else
				continue;

			ws_cJSON *arrayitem = ws_cJSON_CreateObject();

			j++;
		    memset(tmp,0,sizeof(tmp));
			sprintf(tmp,"%d",j);
		    ws_cJSON_AddStringToObject(arrayitem,"no", tmp);		
	
			memset(sname,0,sizeof(sname));
			primary_get_menuID_utf8_text(i,sname);
			ws_cJSON_AddStringToObject(arrayitem,"name",sname);
	
			//on off
			if(0 == (cfg_parm & (1<<i)))
			{
				ws_cJSON_AddStringToObject(arrayitem,"swit","0");
			}
			else
			{
				ws_cJSON_AddStringToObject(arrayitem,"swit","1");
			}

			ws_cJSON_AddItemToArray(list_array,arrayitem);
		}
	    
	   CWatchService_SendJsonData(pme, root);
	}
}

void CWathcService_ReceiveFunctionList(ws_client * pme, void *json)
{
	uint32_t svalue=0, son=0;
	int i=0, cnt=0;
	ws_cJSON *request_s=NULL,*allSwit_s=NULL;
	ws_cJSON *no_s=NULL,*swit_s=NULL;
	ws_cJSON* list_array_s=NULL;
	int set_type=0;

	ws_cJSON *root_parse = (ws_cJSON *)json;  
	request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(request_s==NULL)
    {
        WS_PRINTF("CWathcService_ReceiveFunctionList(): STRING_RSP error\n");
        return;
    }	

    allSwit_s = ws_cJSON_GetObjectItem(request_s, "allSwit");
    if(allSwit_s==NULL || allSwit_s->valuestring==NULL)
    {
        return;
    }
	
	set_type = atoi(allSwit_s->valuestring);

	uint32_t cfg_parm = ke_GetMenuConfig();

	if(set_type==0) //all off
	{
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_UPAY);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_CALENDER);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_STOPWATCH);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_DICTIONARY);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_FOLLOW_READING);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_INTERPRETER);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_HONGSEJINGDIAN);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		#if USE_LV_WATCH_KEY_KW19 != 1
		svalue = primary_get_menuID_offset(WATCH_TEXT_ID_WEATHER);
		cfg_parm &= ~((1 << svalue)) ;
		cfg_parm |= (son << svalue);
		#endif
		WS_PRINTF("all off..CWathcService_ReceiveFunctionListcfg_parm = %x",cfg_parm);
	}
	else if(set_type==1) //all on
	{
	    cfg_parm = 0xFFFFFFFF;
	}
	else if(set_type==2)
	{
		ws_cJSON* list_s = ws_cJSON_GetObjectItem(request_s, "list");
	    cnt = ws_cJSON_GetArraySize(list_s);
		for(i=0; i<cnt; i++)
	    {
	        list_array_s = ws_cJSON_GetArrayItem(list_s, i);

			if(list_array_s!=NULL)
			{
                no_s = ws_cJSON_GetObjectItem(list_array_s,"no");
				if(no_s!=NULL && no_s->valuestring!=NULL)
				{
				    svalue=atoi(no_s->valuestring);
				}

				swit_s = ws_cJSON_GetObjectItem(list_array_s,"swit");
				if(swit_s!=NULL && swit_s->valuestring!=NULL)
				{
				    son=atoi(swit_s->valuestring);
				}

				son &= 0x01;
				if(svalue == 1)
				{
					//zhifubao
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_UPAY);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				else if(svalue == 2)
				{
					//rili
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_CALENDER);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				else if(svalue == 3)
				{
					//miaobiao
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_STOPWATCH);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				else if(svalue == 4)
				{
					//xiaodu zidian
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_DICTIONARY);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				else if(svalue == 5)
				{
					//xiaodufanyi
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_INTERPRETER);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				else if(svalue == 6)
				{
					//hongsejingdian
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_HONGSEJINGDIAN);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				else if(svalue == 7)
				{
					//beidanci
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_FOLLOW_READING);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
				
	#if USE_LV_WATCH_KEY_KW19 != 1
				else if(svalue == 8)
				{
					//beidanci
					svalue = primary_get_menuID_offset(WATCH_TEXT_ID_WEATHER);
					cfg_parm &= ~((1 << svalue)) ;
					cfg_parm |= (son << svalue);
				}
	#endif
				WS_PRINTF("CWathcService_ReceiveFunctionList[%d] = %d,%d,%x",i,svalue,son,cfg_parm);
			}
		}
	}

	primary_menu_refresh_display(cfg_parm);
}
#endif	//end of USE_LV_WATCH_CONFIG_MAINMENU


static void CWathcService_GetServiceLoginRsp(ws_client * pme,ws_service_rsp *rsp, void *data)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_cJSON *root_parse = (ws_cJSON *)data;
	ws_cJSON *pRsp = NULL;
    ws_cJSON *pTime = NULL;
    ws_cJSON *devId = NULL;
    ws_cJSON *Result = NULL;
    ws_cJSON *pDeviceManager = NULL;	
    
    if(NULL == root_parse || NULL == rsp){
        return;
    }

    pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_GetServiceLoginRsp(): STRING_RSP error\n");
		return;
    }	
	 
	Result = ws_cJSON_GetObjectItem(pRsp, "Result");
	if(Result)
	{
		WS_PRINTF("CWathcService_GetServiceLoginRsp(): Result:%s\n", Result->valuestring);
	}

	devId = ws_cJSON_GetObjectItem(pRsp, "DeviceID");
	if(devId && devId->valuestring != NULL)
	{
		WS_PRINTF("CWathcService_GetServiceLoginRsp(): devId:%s\n", devId->valuestring);
		strncpy(userData->mDeviceId , devId->valuestring, WS_DEVICE_ID_MAX_LEN);
	//	userData->ws_reg.devId = atoi(devId->valuestring);
	}	

    pDeviceManager = ws_cJSON_GetObjectItem(pRsp, "DeviceManager");
    if(pDeviceManager)
    {
        WS_PRINTF("CWathcService_GetServiceLoginRsp(): DeviceManager:%s\n", pDeviceManager->valuestring);
    }

	pTime = ws_cJSON_GetObjectItem(pRsp, "Time");
    if(pTime)
    {
        WS_PRINTF("CWathcService_GetServiceLoginRsp(): time:%s\n", pTime->valuestring);
    }   
}

static void CWathcService_CmdPongRsp(ws_client * pme,ws_service_rsp *rsp, void *data)
{
    ws_cJSON *root_parse = (ws_cJSON *)data;
    ws_cJSON *pRsp = NULL;	
    ws_cJSON *pHeartbeat = NULL;
	
    if(NULL == root_parse || NULL == rsp){
        return;
    }
    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

	pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_CmdPongRsp(): STRING_RSP error\n");
		return;
    }	
	 
	pHeartbeat = ws_cJSON_GetObjectItem(pRsp, "heartbeatPeriod");
	if(pHeartbeat)
	{
		WS_PRINTF("CWathcService_CmdPongRsp(): heartbeatPeriod:%d\n", pHeartbeat->valueint);
		userData->m_base.ws_reg.heartTime = pHeartbeat->valueint;
	}

    WS_PRINTF("CWathcService_CmdPongRsp()heartTime=%d", userData->m_base.ws_reg.heartTime);
}

static void CWathcService_SetPeriod(ws_client * pme,ws_service_rsp *rsp, void *data)
{
    ws_cJSON *root_parse = (ws_cJSON *)data;    
    ws_cJSON *request_s = NULL;	
    ws_cJSON *heartbeatPeriod_s = NULL;	
    ws_cJSON *posPeriod_s = NULL;

    WS_PRINTF("CWathcService_SetPeriod\n");
    if(NULL == root_parse || NULL == rsp){
        return;
    }
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	
    request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(request_s==NULL)
    {
        WS_PRINTF("CWathcService_GetServiceLoginRsp(): STRING_RSP error\n");
        return;
    }	
#if 0		//platfrom delete heartbeat period settings
    heartbeatPeriod_s = ws_cJSON_GetObjectItem(request_s, "heartbeatPeriod");
    if(heartbeatPeriod_s)
    {
        userData->m_base.ws_reg.heartTime = heartbeatPeriod_s->valueint;
        myNVMgr_SetWsDevPingTime(userData->m_base.ws_reg.heartTime);
    }
#endif
    posPeriod_s = ws_cJSON_GetObjectItem(request_s, "posPeriod");
    if(posPeriod_s)
    {
		userData->m_base.ws_reg.upPosTime = 60*(posPeriod_s->valueint);
		#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
		ke_upPosTime = 60*(posPeriod_s->valueint);
		#endif
		myNVMgr_SetWsDevPosUpTime(userData->m_base.ws_reg.upPosTime);
    }
   	WS_PRINTF("CWathcService_CmdPongRsp()heartTime=%d, upPosTime=%d", userData->m_base.ws_reg.heartTime, userData->m_base.ws_reg.upPosTime);
    //app_adaptor_fast_dormancy_req();
}

#if USE_LV_WATCH_COURSE != 0
static bool CWathcService_GetCourse(ws_client * pme,ws_course_list *pCourse, void *data)
{
    ws_cJSON *root_parse = (ws_cJSON *)data;    
    ws_cJSON *request_s = NULL;	
	ws_cJSON *list_s = NULL;
	ws_cJSON *week_s = NULL;
	ws_cJSON *timeList_s = NULL;
	ws_cJSON *idx_s = NULL;
	ws_cJSON *name_s = NULL;	
	int nWeekCnt =0;
	int nTimeCnt =0;
	int i,j,nTotal;
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	
    if(NULL == root_parse || NULL == pCourse){
        return FALSE;
    }
	
	request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
	if(request_s==NULL)
    {
    	WS_PRINTF("CWathcService_GetCourse(): STRING_RSP error\n");
		return FALSE;
    }	
	
	list_s = ws_cJSON_GetObjectItem(request_s , "list");
    if(!list_s)
    {
    	return FALSE;
    }

	nWeekCnt = ws_cJSON_GetArraySize(list_s );
	for(i=0; i<nWeekCnt; i++)
	{
		week_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "week");
		if(!week_s)
	    {
	    	return FALSE;
	    }
		
		timeList_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "timeList");
		if(!timeList_s)
		{
			return FALSE;
		}

		nTimeCnt = ws_cJSON_GetArraySize(timeList_s );
		nTotal +=nTimeCnt;
	}
	WS_PRINTF("CWathcService_GetCourse(): nWeekCnt :%d, nTotal:%d", nWeekCnt, nTotal);

	if(nWeekCnt > 7 || nTotal > NV_WS_MAX_COURSE_CNT)
		return FALSE;
	
	pCourse->count = NV_WS_MAX_COURSE_CNT;
	pCourse->course =  (ws_nv_course*)lv_mem_alloc(pCourse->count * sizeof(ws_nv_course));
	if(pCourse->course)
	{
		for(i=0; i<nWeekCnt; i++)
		{
			week_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "week");			
			timeList_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "timeList");

			nTimeCnt = ws_cJSON_GetArraySize(timeList_s );
			
			for(j=0; j<nTimeCnt; j++)
			{
				idx_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(timeList_s, j) , "idx");
				pCourse->course[i*NV_WS_MAX_COURSE_IDX_CNT+j].idx = idx_s->valueint;
				name_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(timeList_s, j) , "name");
				strncpy(pCourse->course[i*NV_WS_MAX_COURSE_IDX_CNT+j].name, name_s->valuestring, NV_WS_COURSE_NAME_LEN);
				
				WS_PRINTF("CWathcService_GetCourse(): week :%d,%d, time:%d, idx:%d:%s", nWeekCnt,week_s->valueint, nTimeCnt, idx_s->valueint, name_s->valuestring);
			}
		}
	}
	return TRUE;
}

#endif

#if USE_LV_WATCH_DATI != 0

uint8_t CWatchService_Is_Dati_Status()
{
	if((g_answer_fun.dati_mode >= 0x01)&&(g_answer_fun.dati_mode <= 0x03))
	{
		return 1;
	}
	else
		return 0;
}

static void CWatchService_SendDownland_Dati_RspCmd(ws_client * pme, char *pkCount ,uint8_t result_suc)
{
    ws_cJSON  *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	uint8_t valueStr[20]={0};	
	uint8_t valueStr2[80]={0};
	ws_cJSON *response_s = NULL;
	uint8_t slen = 0,i=0;
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	cur_answer_result.bignum = cur_answer_result.subnum = 0;
	userData->type = KAER_CMD_DOWNLOAD_DATI;
	
	response_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,response_s);

	memset(valueStr,0,sizeof(valueStr));
	memset(valueStr2,0,sizeof(valueStr2));
	valueStr[0] = 0xAA;
	valueStr[1] = 0xA5;
	valueStr[2] = 0x02;
	valueStr[3] = 0x2C;

	//result
	if(result_suc == 0)
		valueStr[4] = 0x01;
	else
		valueStr[4] = 0x00;

	//check
	for(i=3;i<5;i++)
	{
		slen +=valueStr[i];
	}
	valueStr[5] = slen;
	//change to string
	ke_common_bin_to_hex(valueStr,6,valueStr2);
	
	ws_cJSON_AddStringToObject(response_s,"data", valueStr2);
	ws_cJSON_AddStringToObject(response_s,"imei", pkCount);	

    CWatchService_SendJsonData(pme, root);
}

/*
{"command":"downanswer","request":{"data":"AAA5170C0B0000000000000000000000FFFFFFFFFFFFFFFFFFFF01","imei":"865443040589042"},"type":"client"}
{"command":"downanswer","request":{"data":"AAA5170C01010100000000000000000041FFFFFFFFFFFFFFFFFF3B","imei":"865443040589042"},"type":"client"}
{"command":"downanswer","request":{"data":"AAA5170C02011600000000000000000062FFFFFFFFFFFFFFFFFF51","imei":"865443040589042"},"type":"client"}
{"command":"downanswer","request":{"data":"AAA5170C01010100000000000000000023FFFFFFFFFFFFFFFFFF4B","imei":"865443040589042"},"type":"client"}
{"command":"downanswer","request":{"data":"AAA5170C01010100000000000000000064FFFFFFFFFFFFFFFFFF4Ff","imei":"865443040589042"},"type":"client"}
{"command":"downanswer","request":{"data":"AAA5170C030302030400000000000000416223FFFFFFFFFFFFFFCE","imei":"865443040589042"},"type":"client"}
*/
static uint8_t CWathcService_DownDatiProcess(ws_client * pme, void *json)
{
	ws_cJSON     *root_parse = (ws_cJSON *)json;
	ws_cJSON *request_s = NULL;
	ws_cJSON        *pkId_s = NULL;
    ws_cJSON        *pkCount_s = NULL;	
	ws_cJSON        *ptype_s = NULL;
	char sbuf[200]={0};
	char strimei[WS_SN_MAX_LEN]={0};
	uint8_t i = 0,j=0,k=0,totallen = 0;;
	uint8_t svalue = 0;

    if(NULL == root_parse)
    {
        return 0;
    }
    request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(!request_s)
    {
    	return 0;
    }

	pkCount_s = ws_cJSON_GetObjectItem(request_s, "data");
    if(!pkCount_s)
    {
    	return 0;
    }
	//WS_PRINTF("\n CWathcService_DownDatiProcess start=%s,%s\n",pkId_s->string,pkCount_s->valuestring);

	svalue = 0;
	totallen = (ke_common_asc_to_bin(pkCount_s->valuestring[4]))&0x0f;
	totallen <<= 4;
	totallen |= (ke_common_asc_to_bin(pkCount_s->valuestring[5]))&0x0f;
	
	pkId_s = ws_cJSON_GetObjectItem(request_s, "imei");
    if(!pkId_s)
    {
    	return 0;
    }
	strcpy(strimei,pkId_s->valuestring);
	//cplog_printf("\n strimei[%s]=%s,%s\n",strimei,pkId_s->valuestring,g_imei);
	if((strlen(strimei) >0)&&(strlen(g_imei)>0)&&(memcmp(strimei,g_imei,strlen(strimei))==0))
		;
	else
	{
		//CWatchService_SendDownland_Dati_RspCmd(pme, pkId_s->valuestring, 0);
		//return;
	}
	if(totallen < 9)
	{
		CWatchService_SendDownland_Dati_RspCmd(pme, pkId_s->valuestring, 0);
		return 0;
	}

	//command
	svalue = (ke_common_asc_to_bin(pkCount_s->valuestring[6]))&0x0f;
	svalue <<= 4;
	svalue |= (ke_common_asc_to_bin(pkCount_s->valuestring[7]))&0x0f;
	
	//cplog_printf("\n valuestring[%x]=%s\n",svalue,&pkCount_s->valuestring[8]);
	if(svalue == 0x0c)
		;
	else
	{
		CWatchService_SendDownland_Dati_RspCmd(pme, pkId_s->valuestring, 0);
		return 0;
	}
	
	//str to hex  byte
	memset(sbuf,0,sizeof(sbuf));
	ke_common_hex_to_bin(&pkCount_s->valuestring[8],(totallen-1)*2,sbuf);
	if((sbuf[D_INDEX_STATUS] == TB_MODE_NONE) || (sbuf[D_INDEX_STATUS]== TB_MODE_END))
	{
		memset(&g_answer_fun,0,sizeof(ws_dati_info));
		CWatchService_SendDownland_Dati_RspCmd(pme, pkId_s->valuestring, 1);
		return 2;
	}
	
	memset(&g_answer_fun,0,sizeof(ws_dati_info));
	g_answer_fun.dati_mode = sbuf[D_INDEX_STATUS];
	g_answer_fun.totalnum = sbuf[D_INDEX_TOTAL_NUM];
	//cplog_printf("\n modes=[%d] totalnum=%d\n",g_answer_fun.dati_mode,g_answer_fun.totalnum );
	if((sbuf[D_INDEX_STATUS] == TB_MODE_SINGLE)  )
	{
		memset(g_answer_fun.subnum,0,sizeof(g_answer_fun.subnum));
	}
	else if(sbuf[D_INDEX_STATUS]== TB_MODE_MULTI)
	{
		memset(g_answer_fun.subnum,0,sizeof(g_answer_fun.subnum));
		g_answer_fun.subnum[0] = sbuf[D_INDEX_SUB_NUM];
		//cplog_printf("\n sunum duo[ ] =%d\n",g_answer_fun.subnum[0]);
	}
	else
	{
		for(i=0;i<g_answer_fun.totalnum;i++)
		{
			if(sbuf[D_INDEX_SUB_NUM+i] >= S_SUBJECT_NUM)
				g_answer_fun.subnum[i] = S_SUBJECT_NUM;//小题控制范围
			else
				g_answer_fun.subnum[i]= sbuf[D_INDEX_SUB_NUM+i];
			//cplog_printf("\n sunum[%d] =%d\n",i, g_answer_fun.subnum[i]);
		}
	}

	//start from sbuf[12]
	if((sbuf[D_INDEX_STATUS] == TB_MODE_SINGLE))
	{
		svalue = sbuf[D_INDEX_SUBJECTTYPE];
		if(svalue == 0)
			g_answer_fun.subjecttype[0][0].choicetype = TB_TYPE_MONO_SELECT;
		else
			g_answer_fun.subjecttype[0][0].choicetype = svalue &0x0f;
		g_answer_fun.subjecttype[0][0].choicecount = ((svalue &0xf0)>>4)&0x0f;
		//cplog_printf("\n sunumtype00[0][0] =%d,%d\n",g_answer_fun.subjecttype[0][0].choicetype,g_answer_fun.subjecttype[0][0].choicecount);
	}
	else if(sbuf[D_INDEX_STATUS]== TB_MODE_MULTI)
	{
		svalue = sbuf[D_INDEX_SUBJECTTYPE];
		for(i=0;i<g_answer_fun.subnum[0];i++)
		{
			g_answer_fun.subjecttype[0][i].choicetype =  svalue &0x0f;
			g_answer_fun.subjecttype[0][i].choicecount =  ((svalue &0xf0)>>4)&0x0f;
			//cplog_printf("\n sunumtype01[0][%d] =%d,%d\n",i,g_answer_fun.subjecttype[0][i].choicetype,g_answer_fun.subjecttype[0][i].choicecount);
		}
	}
	else if(sbuf[D_INDEX_STATUS]== TB_MODE_PAPER)
	{
		for(i=0;i<g_answer_fun.totalnum;i++)
		{
			if(i >= B_SUBJECT_NUM)
				break;
			k = g_answer_fun.subnum[i];
			
			if(k >= S_SUBJECT_NUM)
				k = S_SUBJECT_NUM;
			
			svalue = sbuf[D_INDEX_SUBJECTTYPE+i] ;
			for(j=0;j<k;j++)
			{
				if((svalue &0x0f) == TB_TYPE_MONO_SELECT)
				{
					g_answer_fun.subjecttype[i][j].choicetype = TB_TYPE_MONO_SELECT;
					g_answer_fun.subjecttype[i][j].choicecount =  ((svalue &0xf0)>>4)&0x0f;
					//cplog_printf("\n sunumtype1[%d][%d] =%d,%d\n",i,j, g_answer_fun.subjecttype[i][j].choicetype,g_answer_fun.subjecttype[i][j].choicecount);
				}
				else if((svalue &0x0f) == TB_TYPE_MULTI_SELECT)
				{
					g_answer_fun.subjecttype[i][j].choicetype = TB_TYPE_MULTI_SELECT;
					g_answer_fun.subjecttype[i][j].choicecount =  ((svalue &0xf0)>>4)&0x0f;
					//cplog_printf("\n sunumtype2[%d][%d] =%d,%d\n",i,j, g_answer_fun.subjecttype[i][j].choicetype,g_answer_fun.subjecttype[i][j].choicecount);
				}
				else if((svalue &0x0f) == TB_TYPE_JUDGE_SELECT)
				{
					g_answer_fun.subjecttype[i][j].choicetype = TB_TYPE_JUDGE_SELECT;
					g_answer_fun.subjecttype[i][j].choicecount = ((svalue &0xf0)>>4)&0x0f;
					//cplog_printf("\n sunumtype3[%d][%d] =%d,%d\n",i,j, g_answer_fun.subjecttype[i][j].choicetype,g_answer_fun.subjecttype[i][j].choicecount);
				}
			}
		}
	}
	
	CWatchService_SendDownland_Dati_RspCmd(pme, pkId_s->valuestring, 1);
	return 1;
}

static void CWathcService_UpDatiProcess(ws_client * pme, key_stu_t *key_info)
{
    ws_cJSON  *root = NULL;    
	ws_cJSON *response_s = NULL;
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	uint8_t valueStr[20]={0};	
	uint8_t valueStr2[80]={0};
	uint8_t slen = 0,i=0,kd=0,kc=0,kb=0,ka=0;
    root = ws_cJSON_CreateObject();
    if(NULL == root || NULL == key_info)
    {
        return;
    }
	//cplog_printf("\n ++++++++++CWathcService_UpDatiProcess key=%d,=%d,=%d,=%d\n",key_info->key_one_1,key_info->key_one_2,key_info->key_one_3,key_info->key_one_sos);

	valueStr[UP_INDEX_PROTO1] = 0xAA;
	valueStr[UP_INDEX_PROTO2] = 0xA5;
	valueStr[UP_INDEX_LEN] = (UP_INDEX_VERSION - UP_INDEX_LEN)&0x0f;
	valueStr[UP_INDEX_CMD] = 0x29;
	 
	valueStr[UP_INDEX_ONE_NUM] = 0x01
	valueStr[UP_INDEX_CARD_ID1]=valueStr[UP_INDEX_CARD_ID1+1]=valueStr[UP_INDEX_CARD_ID1+2]=valueStr[UP_INDEX_CARD_ID1+3] = 0X00;

	valueStr[UP_INDEX_CARD_IDVERTIY] = 0X00;
	valueStr[UP_INDEX_SUBJECT_TYPE] = g_answer_fun.subjecttype[cur_answer_result.bignum][cur_answer_result.subnum].choicetype ;
	
	if(g_answer_fun.dati_mode == TB_MODE_SINGLE)
	{
		valueStr[UP_INDEX_TOTAL_NUM]=valueStr[UP_INDEX_SUB_NUM] = 0x01;

		if(valueStr[UP_INDEX_SUBJECT_TYPE] == TB_TYPE_NUMBER)
		{
			valueStr[UP_INDEX_TOTAL_NUM]=valueStr[UP_INDEX_SUB_NUM]=0;
			valueStr[UP_INDEX_ANSWER]=0;
		}
	}
	else if(g_answer_fun.dati_mode == TB_MODE_MULTI)
	{
		valueStr[UP_INDEX_TOTAL_NUM]=g_answer_fun.subnum[0];
		valueStr[UP_INDEX_SUB_NUM]=cur_answer_result.subnum+1;
		
	}
	else if(g_answer_fun.dati_mode == TB_MODE_PAPER)
	{
		valueStr[UP_INDEX_TOTAL_NUM]=cur_answer_result.bignum+1;
		valueStr[UP_INDEX_SUB_NUM]=cur_answer_result.subnum+1;

	}

	if(valueStr[UP_INDEX_SUBJECT_TYPE] == TB_TYPE_MONO_SELECT)
	{
		if(key_info->key_one_1)
		{
			valueStr[UP_INDEX_ANSWER] = 0x01;//a
		}
		else if(key_info->key_one_2)
		{
			valueStr[UP_INDEX_ANSWER] = 0x02;//b
		}
		else if(key_info->key_one_3)
		{
			valueStr[UP_INDEX_ANSWER] = 0x04;//c
		}
		else if(key_info->key_one_sos)
		{
			valueStr[UP_INDEX_ANSWER] = 0x08;//d
		}
	}
	else if(valueStr[UP_INDEX_SUBJECT_TYPE] == TB_TYPE_MULTI_SELECT)
	{
		//key1=a,2=b,3=c,sos=d
		kd = key_info->key_one_sos &0x01;
		kd <<=3;
		kc = key_info->key_one_3&0x01;
		kc <<= 2;
		kb = key_info->key_one_2&0x01;
		kb <<= 1;
		ka = key_info->key_one_1&0x01;
		valueStr[UP_INDEX_ANSWER] = (kd|kc|kb|ka)&0x0f; 
		
	}
	else if(valueStr[UP_INDEX_SUBJECT_TYPE] == TB_TYPE_JUDGE_SELECT)
	{
		//key1or2or3==ok soskey = err//
		if((key_info->key_one_1)||(key_info->key_one_2)||(key_info->key_one_3))
		{
			valueStr[UP_INDEX_ANSWER] = 0x80;
		}
		if(key_info->key_one_sos)
		{
			valueStr[UP_INDEX_ANSWER] = 0x40;
		}
	}	

	//cplog_printf("\n ++++++++++CWathcService_UpDatiProcess[5]=%d,[6]=%d,[7]=%d,[8]=%x\n",valueStr[UP_INDEX_SUBJECT_TYPE],valueStr[UP_INDEX_TOTAL_NUM],valueStr[UP_INDEX_SUB_NUM],valueStr[UP_INDEX_ANSWER]);
	//check
	for(i=UP_INDEX_CMD;i<UP_INDEX_VERSION;i++)
	{
		slen +=valueStr[i];
	}
	valueStr[UP_INDEX_CHECKSUN] = slen;

	memset(valueStr2,0,sizeof(valueStr2));
	//change to string
	ke_common_bin_to_hex(valueStr,UP_INDEX_CHECKSUN+1,valueStr2);
    
	userData->type = KAER_CMD_UP_DATI;
	
	response_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,response_s);

	ws_cJSON_AddStringToObject(response_s,"data", valueStr2);	
    CWatchService_SendJsonData(pme, root);
}

static uint8_t CWathcService_UpDatiResponse(ws_client * pme, void *json)
{
	ws_cJSON     *root_parse = (ws_cJSON *)json;
    ws_cJSON        *request_s = NULL;
	ws_cJSON        *pkCount_s = NULL;
	uint8_t svalue = 0,i=0,j=0;
	if(NULL == root_parse)
		return 0;
	
	request_s = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(!request_s)
    {
    	return 0;
    }
	pkCount_s = ws_cJSON_GetObjectItem(request_s, "data");
    if(!pkCount_s)
    {
		return 0;
    }

	//command
	svalue = (ke_common_asc_to_bin(pkCount_s->valuestring[6]))&0x0f;
	svalue <<= 4;
	svalue |= (ke_common_asc_to_bin(pkCount_s->valuestring[7]))&0x0f;
	
	if(svalue == 0x09)
		;
	else
		return 0;

	//cplog_printf("\n CWathcService_UpDatiResponse valuestring[%x]=%s\n",svalue,&pkCount_s->valuestring[8]);
	svalue = (ke_common_asc_to_bin(pkCount_s->valuestring[8]))&0x0f;
	svalue <<= 4;
	svalue |= (ke_common_asc_to_bin(pkCount_s->valuestring[9]))&0x0f;

	if(svalue == 0)
		;
	else
	{
	//cplog_printf("\n valuestringfaillllllllllllll\n" );
	}

	if(g_answer_fun.dati_mode == TB_MODE_SINGLE)
	{
		cur_answer_result.bignum = cur_answer_result.subnum = 0;
		return 3;
	}
	else if(g_answer_fun.dati_mode == TB_MODE_MULTI)
	{
		cur_answer_result.bignum = 0;
		cur_answer_result.subnum++;
		//cplog_printf("\n valuestring[2]=s=%d,t=%d\n",cur_answer_result.subnum,g_answer_fun.totalnum);
		if(cur_answer_result.subnum >= g_answer_fun.subnum[0])
		{
			cur_answer_result.bignum = cur_answer_result.subnum = 0;
			return 3;
		}
	}
	else if(g_answer_fun.dati_mode == TB_MODE_PAPER)
	{
		cur_answer_result.subnum++;
		//cplog_printf("\n valuestring[3]=b=%d,s=%d,t=%d,ts=%d\n",cur_answer_result.bignum,cur_answer_result.subnum,g_answer_fun.totalnum,g_answer_fun.subnum[cur_answer_result.bignum]);
		if(cur_answer_result.subnum >= g_answer_fun.subnum[cur_answer_result.bignum])
		{
			cur_answer_result.subnum = 0;
			cur_answer_result.bignum++;
		}
		if(cur_answer_result.bignum >= g_answer_fun.totalnum)
		{
			cur_answer_result.bignum = cur_answer_result.subnum = 0;
			return 3;
		}
	}

	return 2;
}
#endif

// "{\"type\":\"terminal\",\"command\":\"setNumList\",\"request\":{\"pkId\":0,\"pkCount\":2,\"list\":[{\"type\":\"1\",\"typelist\":[{\"number\":\"15366166748\",\"disname\":\"鐢崇埍鏋\"},{\"number\":\"15628588865\",\"disname\":\"娴嬭\"}]}],\"taskId\":50372}}"
//{"type":"terminal","command":"setNumList","request":{"pkId":0,"pkCount":2,"list":[{"type":"1","typelist":[]}],"taskId":50477}} 
static void CWathcService_ContactsProcess(ws_client * pme, void *json,ws_evt_msg_t  * pEvtMsg )
{
    ws_cJSON     *root_parse = (ws_cJSON *)json;
    ws_cJSON        *type_s = NULL;
    ws_cJSON         *last_s = NULL;
    ws_cJSON    *phb_array_s = NULL;
    ws_cJSON          *phb_s = NULL;
    ws_cJSON         *name_s = NULL;
    ws_cJSON        *phone_s = NULL;
    ws_cJSON        *avatar_s = NULL;
    ws_cJSON        *pkId_s = NULL;
    ws_cJSON        *pkCount_s = NULL;	
    uint16_t          i,cnt = 0;
    ws_cJSON *list_s = NULL;
    ws_cJSON *typelist_s = NULL;
    ws_cJSON *request_s = NULL;
    uint16_t pkId; uint16_t type;
    ws_contact_list *pContact = &pEvtMsg->cmd.contatc_list;    
    ws_sos_list *sos_list = &pEvtMsg->cmd.sos_list;

    //WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
    if(NULL == root_parse || NULL == pEvtMsg)
    {
        return;
    }

    request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(!request_s)
    {
    	return;
    }

	pkId_s = ws_cJSON_GetObjectItem(request_s, "pkId");
    if(!pkId_s)
    {
    	return;
    }

	pkCount_s = ws_cJSON_GetObjectItem(request_s, "pkCount");
    if(!pkCount_s)
    {
		return;
    }
	
    list_s = ws_cJSON_GetObjectItem(request_s , "list");
    if(!list_s)
    {
    	return;
    }

	type_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, 0) , "type");
	if(!type_s)
	{
		return;
	}

	type = atoi(type_s->valuestring);
	typelist_s = ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, 0) , "typelist");
	cnt = ws_cJSON_GetArraySize(typelist_s);
	//WS_PRINTF("%s:%d, type:%d", __FUNCTION__, __LINE__, type);
	if(typelist_s)
	{
		if(type==KAER_PB_TYPE_FAMILY)
		{
		    pEvtMsg->evt = CMD_S2C_SETPARAM_CONTACT_LIST;
		    pContact->index_start = 0;//pkId_s->valueint;
		    pContact->index_end = cnt;//pkCount_s->valueint;    
		   
		    WS_PRINTF("KAERKernelC::ParsingPHBList(): pkId=%d, pkCount=%d, cnt:%d", pkId_s->valueint , pkCount_s->valueint, cnt);
		    
		    pContact->count = cnt;
		    pContact->max = WS_PHB_MAX;	   
		    pContact->contact = NULL;
		    if(cnt)
		    {
				uint8_t *FamilyNoSer =  WatchUtility_GetFamilyNoSer();
			    memset(FamilyNoSer, 0, 3);
		    	ws_cJSON    *familyNoSer_s = NULL;
		        pContact->contact = lv_mem_alloc(cnt*sizeof(ws_contact));
		        if(pContact->contact==NULL)
		        {
			        WS_PRINTF("contact alloc fail !!");
			        return;
		        }

                memset(pContact->contact, 0, cnt*sizeof(ws_contact));

		        for(i=0; i<cnt; i++)
		        {
			        phb_s = ws_cJSON_GetArrayItem(typelist_s, i);
			        name_s = ws_cJSON_GetObjectItem(phb_s, "disname");
			        phone_s = ws_cJSON_GetObjectItem(phb_s, "number");
			        familyNoSer_s = ws_cJSON_GetObjectItem(phb_s, "serialNo");

			        if((familyNoSer_s) && (i < 3) && (familyNoSer_s->valueint <= 3) && (familyNoSer_s->valueint > 0))
			        {
			        	WS_PRINTF("familyNoSer_s->valueint IS %d i is %d",familyNoSer_s->valueint, i);
						pContact->contact[i].speed_dial_pos = familyNoSer_s->valueint - 1;
						pContact->speed_dial_has_pos = 1;
						FamilyNoSer[familyNoSer_s->valueint - 1] = i + 1 ;
			        }else{
						pContact->contact[i].speed_dial_pos = 255;
			        }
			        
			        if(name_s)
			        {
			            strncpy(pContact->contact[i].name, name_s->valuestring, WS_CONTACT_NAME_MAX_LEN);
			        }
			        if(phone_s)
			        {
			            strncpy(pContact->contact[i].phone, phone_s->valuestring, WS_CONTACT_PHONE_MAX_LEN);						
			        }
			        pContact->contact[i].avatar_id =9;
		        }
				myNVMgr_SetFamilyNoSer(FamilyNoSer);
		    }    
		}
		else if(type==KAER_PB_TYPE_WHITE)
		{
		    pEvtMsg->evt = CMD_S2C_SETPARAM_WHITE_LIST;
		    pContact->index_start = pkId_s->valueint;
		    pContact->index_end = pkCount_s->valueint;    
		    if(pkId_s->valueint == 0)
		    {
		    	TempWhiteCnt = 0;
		    	sWhiteCntCurrPac = 0;
		    }
		   
		    WS_PRINTF("KAERKernelC::ParsingPHBList(): pkId=%d, pkCount=%d, cnt:%d", pkId_s->valueint , pkCount_s->valueint, cnt);
		    
		   // pContact->count = cnt + TempWhiteCnt;
		    pContact->max = WS_PHB_MAX;	   
		    pContact->contact = NULL;
		    if(cnt)
		    {
		        pContact->contact = lv_mem_alloc((cnt + TempWhiteCnt)*sizeof(ws_contact));
				if(pContact->contact==NULL)
		        {
			        WS_PRINTF("contact alloc fail !!");
			        return;
		        }

                memset(pContact->contact, 0, (cnt+TempWhiteCnt)*sizeof(ws_contact));

				for(i=0; i< TempWhiteCnt; i++)  //
				{
							
					strncpy(pContact->contact[i].name, TempWhiteContact[i].name, WS_CONTACT_NAME_MAX_LEN);
					strncpy(pContact->contact[i].phone, TempWhiteContact[i].phone, WS_CONTACT_PHONE_MAX_LEN);
					pContact->contact[i].avatar_id =10;
				}

				char Temp = TempWhiteCnt;
				WS_PRINTF("CWathcService_ContactsProcess()Temp IS %d (%d) %s %d (%d)",Temp, i, typelist_s,pkId_s->valueint,sWhiteCntCurrPac);
				while(i < (cnt  + Temp))
		    	{
		    		//WS_PRINTF("CWathcService_ContactsProcess()Temp IS %d i is %d",Temp, i);
			        phb_s = ws_cJSON_GetArrayItem(typelist_s, i - Temp);
			        name_s = ws_cJSON_GetObjectItem(phb_s, "disname");
			        phone_s = ws_cJSON_GetObjectItem(phb_s, "number");
			        WS_PRINTF("CWathcService_ContactsProcess()name_s->valuestring IS %s phone_s->valuestring (%s)",
			        name_s->valuestring, phone_s->valuestring);
			        if(name_s && phone_s)
			        {
			            strncpy(pContact->contact[i].name, name_s->valuestring, WS_CONTACT_NAME_MAX_LEN);
			            strncpy(pContact->contact[i].phone, phone_s->valuestring, WS_CONTACT_PHONE_MAX_LEN);
						pContact->contact[i].avatar_id =10;
						
						if((pkCount_s->valueint > (pkId_s->valueint +1))
						&& (pkId_s->valueint == sWhiteCntCurrPac)) //
				        {
				        	
				        	strncpy(TempWhiteContact[TempWhiteCnt].name, name_s->valuestring, WS_CONTACT_NAME_MAX_LEN);
							strncpy(TempWhiteContact[TempWhiteCnt++].phone, phone_s->valuestring, WS_CONTACT_PHONE_MAX_LEN);	
				        }
			        }	
	     
			        i++;
			         if(i >= (WS_PHB_MAX - 3))   //鍘绘帀3涓翰鎯呭彿 鏈€澶氾紙WS_PHB_MAX - 3锛?涓櫧鍚嶅崟
			        {
			        	break;
			        }
			    }
			    sWhiteCntCurrPac++;
		    }    
		    pContact->count = i;
		}

		else if(type==KAER_PB_TYPE_SOS)
		{			
			pEvtMsg->evt = CMD_S2C_SETPARAM_SOS_LIST;
			sos_list->count = cnt;
		    sos_list->single = 0xff;
			WS_PRINTF("KAERKernelC::sos pkId=%d, pkCount=%d, cnt:%d", pkId_s->valueint , pkCount_s->valueint, cnt);
		    for(i=0; i<cnt; i++)
		    {
		        phb_s = ws_cJSON_GetArrayItem(typelist_s, i);
				#if USE_LV_WATCH_SPAIN != 0
				name_s = ws_cJSON_GetObjectItem(phb_s, "disname");
				#endif
				phone_s = ws_cJSON_GetObjectItem(phb_s, "number");
				if(phone_s )
				{
					memset(sos_list->sos[i],0,WS_CONTACT_PHONE_MAX_LEN+1);
		        	strcpy(sos_list->sos[i], phone_s->valuestring);
					#if USE_LV_WATCH_SPAIN != 0
					memset(sos_list->name[i],0,WS_CONTACT_NAME_MAX_LEN+1);
					strcpy(sos_list->name[i], name_s->valuestring);
					#endif
					//cplog_printf("\n KAERKernelC::sos[%d]=%s\n ", i,sos_list->sos[i]);
		    	}
		    }
		}
	}
	
	CWatchService_SendPbRspCmd(pme, pkId_s->valueint, pkCount_s->valueint);	
}

static uint16_t CWathcService_FindMuteListPos(ws_mute_list *pMute, char *pBeg, char*pEnd)
{
	uint16_t nPos =0;
	for(nPos=0; nPos<pMute->count; nPos++)
	{
		if(pMute->mutes[nPos].repeat==0)
			break;
		if(memcmp(pMute->mutes[nPos].start, pBeg, 5)==0 
			&& memcmp(pMute->mutes[nPos].end, pEnd, 5)==0)
			break;
	}
	return nPos;
}

static uint16_t CWathcService_GetMuteListCnt(ws_mute_list *pMute)
{
	uint16_t nPos =0;
	for(nPos=0; nPos<pMute->count; nPos++)
	{
		if(pMute->mutes[nPos].repeat==0)
			break;
	}
	return nPos;
}

static void CWatchService_FromNvmGetUrl(void)
{
	char UrlPort[100] = {0};
	if(CWatchService_IsTempUrlValid())
	{
		CWatchService_SetTempUrl(UrlPort);
	}
	else
		myNVMgr_GetWsUrlPort(UrlPort);
	WS_PRINTF("CWatchService_FromNvmGetUrl() UrlPort is %s", UrlPort);
	char *Index = strstr(UrlPort, "@");
	if(Index != NULL)
	{
		*Index = 0;
		Index++;
		int Port = atoi(Index);
		
		if(Port > 0 && Port < 0xffff)
		{
			memset(sUrl,0,sizeof(sUrl));
			strcpy(sUrl, UrlPort);
			
			server.port = Port;
		}		
	}
	else
	{
	#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
		memset(sUrl,0,sizeof(sUrl));
		strncpy(sUrl, WS_REGISTER_SERVER, strlen(WS_REGISTER_SERVER));
		server.port = WS_REGISGER_PORT;
	#endif
	}
	WS_PRINTF("%s server.host IS %s, server.port is %d",__FUNCTION__, server.host, server.port);
}

/*
{
"type":"terminal",
"command":"sendOrder",
"request":{
"type":
"content":
"taskId":""
}
}
*/
static bool CWathcService_Reboot(ws_client * pme, void *json,ws_mute_list *pMute)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *type = NULL;
    ws_cJSON *msg = NULL;	
    ws_cJSON *week_s = NULL;	
    ws_cJSON *timeList_s = NULL;	
    ws_cJSON *endTime_s = NULL;	
    ws_cJSON *begTime_s = NULL;
    uint16_t          i,cnt = 0;
    uint16_t          j,nTimCnt = 0;	
	uint16_t		  nTotal = 0;	
	uint16_t		  nPos = 0;
	
    ws_cJSON *disturb_array_s = NULL;
	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pMute)
    {
        return false;
    }
	
	userData->type = KAER_CMD_REBOOT;

    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return false;
	}

	type = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "type");
	if(!type)
    {
    	return false;
    }

#if USE_LV_WATCH_SPAIN != 0
    switch(type->valueint) {
        case 3:  // 关机
			onkey_wakeup();
			shutdown_confirm_btn_action(NULL, LV_EVENT_CLICKED);
            return true;
        case 4:  // 重启
			onkey_wakeup();
			restart_yes_btn_action(NULL, LV_EVENT_CLICKED);
            return true;
        default:
            return false;
    }
#else
	if(type->valueint ==  4)
	{
		return true;
	}
	return false;
#endif
}



/*
{
"type":"terminal",
"command":"pushMsg",
"request":{
"disname":"",
"time":"2020-11-19 11:41:23",
"msg":"",
"taskId":
}
}
*/
static void CWathcService_PushMsg(ws_client * pme, void *json,ws_mute_list *pMute)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *disname = NULL;
    ws_cJSON *msg = NULL;	
    ws_cJSON *week_s = NULL;	
    ws_cJSON *timeList_s = NULL;	
    ws_cJSON *endTime_s = NULL;	
    ws_cJSON *begTime_s = NULL;
    uint16_t          i,cnt = 0;
    uint16_t          j,nTimCnt = 0;	
	uint16_t		  nTotal = 0;	
	uint16_t		  nPos = 0;
	
    ws_cJSON *disturb_array_s = NULL;
	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pMute)
    {
        return;
    }
	
	userData->type = KAER_CMD_NEW_TEXT_MSG;

    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	disname = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "disname");
	if(!disname)
    {
    	return;
    }
    
    #if USE_LV_WATCH_SMS_MSG != 0
	msg = ws_cJSON_GetObjectItem(req_s , "msg");
    if(!msg)
    {
    	return;
    }
	if(msg->valuestring[0]=='D')
	{
		char *pauses = strstr(msg->valuestring,"DEVICECOMMPAUSE=");
		if( pauses != NULL)
		{
			char  *pausewitch = pauses + 16;
			#if USE_LV_WATCH_SAME_TIME_OPEN_STOP !=0
			CWatchService_set_sametime_open(pausewitch);
			#endif
			return;
		}
	}
	#if USE_LV_WATCH_SMS_NO_SHOW != 1
	char UrlPort[50] = {0};
	uint16_t MsgLen = strlen(msg->valuestring);
	MsgLen = MsgLen > KAER_MSG_MAX_LEN ? KAER_MSG_MAX_LEN : MsgLen;
	sms_msg_create_node(disname->valuestring, msg->valuestring, MsgLen);
	#endif
    #endif
}

/*
{
"type":"terminal",
"command":"chgaddr",
"request":{
"platip":"**************",
"platport":"12000",
"fileport":"12001",
}
}
*/
static void CWathcService_SetUrl(ws_client * pme, void *json,ws_mute_list *pMute)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *platip = NULL;
    ws_cJSON *platport = NULL;	
    ws_cJSON *week_s = NULL;	
    ws_cJSON *timeList_s = NULL;	
    ws_cJSON *endTime_s = NULL;	
    ws_cJSON *begTime_s = NULL;
    uint16_t          i,cnt = 0;
    uint16_t          j,nTimCnt = 0;	
	uint16_t		  nTotal = 0;	
	uint16_t		  nPos = 0;
	uint8_t ret = 0;
	
    ws_cJSON *disturb_array_s = NULL;
	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pMute)
    {
        return;
    }
	
	userData->type = KAER_CMD_SET_ADDR;

    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	platip = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "platip");
	if(!platip)
    {
    	return;
    }
	platport = ws_cJSON_GetObjectItem(req_s , "platport");
    if(!platport)
    {
    	return;
    }
	char UrlPort[100] = {0};
	snprintf(UrlPort, 100, "%s@%s",platip->valuestring, platport->valuestring);
	WS_PRINTF("CWathcService_SetUrl() URLPORT IS %s",UrlPort);
	
	ret = CWatchService_SetUrl(UrlPort);
	if(ret == 0)
		return;
	uos_sleep(400);
	MMI_ModemAdp_WS_Stop_Ext();
    
    uint8_t time = 0;
    while(time < 10)
	{
		if(!MMI_ModemAdp_WS_Is_Online())
		{
			uos_sleep(200);
			MMI_ModemAdp_WS_Start(pme);
			return;
		}
		time++;
		uos_sleep(200);
	}

	//WS_PRINTF("Mute nPos=%d, nTotal=%d, count:%d", nPos , nTotal, pMute ->count);
}

//{"type":"terminal","command":"hiding","request":{"swit":"1","list":[{"timeList":[],"week":"0"},{"timeList":[],"week":"1"},{"timeList":[],"week":"2"},{"timeList":[{"endTime":"1800","begTime":"1626"},{"endTime":"2300 
//{"type":"terminal","command":"hiding","request":{"swit":1,  "list":[{"week":1,"timeList":[{"begTime":"0500","endTime":"2003"},{"begTime":"0801","endTime":"1500"}]},{"week":2,"timeList":[{"begTime":"1002","en 
static void CWathcService_GetMuteList(ws_client * pme, void *json,ws_mute_list *pMute)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *swit_s = NULL;
    ws_cJSON *list_s = NULL;	
    ws_cJSON *week_s = NULL;	
    ws_cJSON *timeList_s = NULL;	
    ws_cJSON *endTime_s = NULL;	
    ws_cJSON *begTime_s = NULL;
    uint16_t          i,cnt = 0;
    uint16_t          j,nTimCnt = 0;	
	uint16_t		  nTotal = 0;	
	uint16_t		  nPos = 0;
	
    ws_cJSON *disturb_array_s = NULL;
	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pMute)
    {
        return;
    }
	
	userData->type = KAER_CMD_STUDY_TIME;

    memset(pMute, 0, sizeof(ws_mute_list));
    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	swit_s = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "swit");
	if(!swit_s)
    {
    	return;
    }
	list_s = ws_cJSON_GetObjectItem(req_s , "list");
    if(!list_s)
    {
    	return;
    }

	cnt = ws_cJSON_GetArraySize(list_s);
	for(i=0; i<cnt; i++)
	{		
		timeList_s = ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "timeList");
		nTimCnt = ws_cJSON_GetArraySize(timeList_s);
		nTotal += nTimCnt; 
	}

	pMute ->count = nTotal;
	
	if(nTotal)
	{
		char WatchUtilityLimited = WatchUtility_GetCallinLimited();
		int Limited = 0; 
		Limited = 0;//sos call in
		Limited	&= 0x01;
		WatchUtilityLimited &= ~((1 << 7) | (1 << 6)) ;  
		WatchUtilityLimited |= (Limited << 7);
		if(atoi(swit_s->valuestring)==1)		
			Limited = 0; //sos禁止呼出
		else if(atoi(swit_s->valuestring)==3)		
			Limited = 1; //sos允许呼出
		else
			Limited = 1;
		WatchUtilityLimited |= (Limited << 6);     
				
		myNVMgr_SetCallinLimited(&WatchUtilityLimited);

		pMute->mutes = (ws_mute*)lv_mem_alloc(nTotal*sizeof(ws_mute));
		memset(pMute->mutes, 0 , nTotal*sizeof(ws_mute));
		for(i=0; i<cnt; i++)
		{		
			week_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "week");
			timeList_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "timeList");
			nTimCnt = ws_cJSON_GetArraySize(timeList_s);
			for(j=0; j<nTimCnt; j++)
			{			
				begTime_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(timeList_s, j) , "begTime");
				endTime_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(timeList_s, j) , "endTime");
				if(begTime_s && endTime_s)
				{
					const uint8_t wrp[7] = {0,1,2,3,4,5,6};
					uint8_t  w;
					char    sBegTime[10]={0};				
					char    sEndTime[10]={0};
					memcpy(sBegTime, begTime_s->valuestring, 2);
					sBegTime[2]=':';
					memcpy(sBegTime+3, begTime_s->valuestring+2, 2);			
					
					memcpy(sEndTime, endTime_s->valuestring, 2);
					sEndTime[2]=':';
					memcpy(sEndTime+3, endTime_s->valuestring+2, 2);

					nPos =CWathcService_FindMuteListPos(pMute, sBegTime, sEndTime);
					if(nPos >= WS_MUTE_MAX_COUNT)
						break;

					pMute->mutes[nPos].id = nPos;
					//pMute->mutes[nPos].is_on = swit_s->valueint;
					pMute->mutes[nPos].is_on = atoi(swit_s->valuestring);
					//w= week_s->valueint;
					w= atoi(week_s->valuestring);
					pMute->mutes[nPos].repeat += (1<<wrp[w]);
					pMute->mutes[nPos].repeat |= 0x80;
					strcpy(pMute->mutes[nPos].start, sBegTime);
					strcat(pMute->mutes[nPos].start, ":00");
					strcpy(pMute->mutes[nPos].end, sEndTime);
					strcat(pMute->mutes[nPos].end, ":00");
					WS_PRINTF("start %s, end :%s, nPos:%d, repeat:%x,w:%d,is_on:%d"
								, pMute->mutes[nPos].start, pMute->mutes[nPos].end, nPos, pMute->mutes[nPos].repeat, w, pMute->mutes[nPos].is_on);
				}
			}			
		}
	}
	pMute ->count = CWathcService_GetMuteListCnt(pMute);
	//WS_PRINTF("Mute nPos=%d, nTotal=%d, count:%d", nPos , nTotal, pMute ->count);
}

static void CWathcService_OpenClose(ws_client * pme, void *json,ws_nv_power_period *pPeriod)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *swit_s = NULL;
    ws_cJSON *endTime_s = NULL;	
    ws_cJSON *begTime_s = NULL;	

	char    sBegTime[10]={0};				
	char    sEndTime[10]={0};
#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0 && USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
	nv_watch_alarm_info_t tempvalue;
	char Wk = 0;
	uint8_t i = 0,k=0;
	nv_watch_alarm_t nv_alarm;
	app_adaptor_alarm_t *alarm_list = NULL;
	int len;
    Hal_Mem_Set(&nv_alarm, 0, sizeof(nv_watch_alarm_t));
	memset((void*)&tempvalue, 0x00, sizeof(nv_watch_alarm_info_t));

	memset(sBegTime,0,sizeof(sBegTime));
	memset(sEndTime,0,sizeof(sEndTime));
#endif
	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pPeriod)
    {
        return;
    }
	
	userData->type = KAER_CMD_POWER_ONOFF;

    memset(pPeriod, 0, sizeof(ws_nv_power_period));
    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	swit_s = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "swit");
	if(!swit_s)
    {
    	return;
    }

	WS_PRINTF("____________ CWathcService_OpenClose swit=%d",atoi(swit_s->valuestring));
#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0 && USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
	if(atoi(swit_s->valuestring) == 0)
	{
		//close---------------
		//power off
		WatchUtility_SavePwroff_info(tempvalue,0,1);
		myNVMgr_SetWsPwroffClock();
		//power on
		UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);
		len = NV_ALARM_MAX_ALARM_NUM*sizeof(app_adaptor_alarm_t);
		alarm_list = (app_adaptor_alarm_t *)lv_mem_alloc(len);
		Hal_Mem_Set(alarm_list, 0, len);

		for(i = 0;i<NV_ALARM_MAX_ALARM_NUM;i++)
		{
			if(i == (NV_ALARM_MAX_ALARM_NUM - 1))
			{
				//powr on alarm
	            alarm_list[i].on_off = 0;
	            alarm_list[i].hour =  0;
	            alarm_list[i].min = 0;
	            alarm_list[i].repeat_bitmap = 0; 
				alarm_list[i].valid =  0;

				#if USE_LV_WATCH_CYCLE_ALARM != 0 || USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
	            alarm_list[i].type = 0;
	            WS_PRINTF("CWathcService_OpenClose(i:%d) type:%d",i,alarm_list[i].type );
				#endif
				#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
	            alarm_list[i].ring_en = nv_alarm.alarm_info[i].ring_en;
	            alarm_list[i].vib_en = nv_alarm.alarm_info[i].vib_en;
				#endif
				#if USE_LV_WATCH_ALARM_S2C_NAME!=0
				strncpy(alarm_list[i].name,nv_alarm.alarm_info[i].name,40);
				#endif

			}
			else
			{
	            alarm_list[i].on_off = nv_alarm.alarm_info[i].on_off;
	            alarm_list[i].hour =  nv_alarm.alarm_info[i].hour;
	            alarm_list[i].min = nv_alarm.alarm_info[i].min;
	            alarm_list[i].repeat_bitmap = nv_alarm.alarm_info[i].repeat_bitmap; 
				alarm_list[i].valid =  nv_alarm.alarm_info[i].valid;

				#if USE_LV_WATCH_CYCLE_ALARM != 0 || USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
	            alarm_list[i].type = nv_alarm.alarm_info[i].type;
	            WS_PRINTF("CWathcService_OpenClose(i:%d) type:%d",i,alarm_list[i].type );
				#endif
				#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
	            alarm_list[i].ring_en = nv_alarm.alarm_info[i].ring_en;
	            alarm_list[i].vib_en = nv_alarm.alarm_info[i].vib_en;
				#endif
				#if USE_LV_WATCH_ALARM_S2C_NAME!=0
				strncpy(alarm_list[i].name,nv_alarm.alarm_info[i].name,40);
				#endif

			}
            WS_PRINTF("CWathcService_OpenClose() alarm_list[%d]%d:%d,onoff=%d, %02x", i, alarm_list[i].hour,alarm_list[i].min,alarm_list[i].on_off,alarm_list[i].repeat_bitmap);


		}
		alarm_set_alarm(alarm_list, NV_ALARM_MAX_ALARM_NUM);
		lv_mem_free(alarm_list);
		
		return;

	}
	begTime_s = ws_cJSON_GetObjectItem(req_s , "begTime");
    if(!begTime_s)
    {
    	return;
    }

	endTime_s = ws_cJSON_GetObjectItem(req_s , "endTime");
    if(!endTime_s)
    {
    	return;
    }
	//OPEN=================
	//power on
	UI_NV_Read_Req(NV_SECTION_UI_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&nv_alarm);
	len = NV_ALARM_MAX_ALARM_NUM*sizeof(app_adaptor_alarm_t);
	alarm_list = (app_adaptor_alarm_t *)lv_mem_alloc(len);
	Hal_Mem_Set(alarm_list, 0, len);
	for(i = 0;i<NV_ALARM_MAX_ALARM_NUM;i++)
	{
		if(i == (NV_ALARM_MAX_ALARM_NUM - 1))
		{
		//power on alarm
            alarm_list[i].on_off = 1;
			memcpy(sBegTime, begTime_s->valuestring, 2);
            alarm_list[i].hour =  atoi(sBegTime);
			memset(sBegTime,0,sizeof(sBegTime));
			memcpy(sBegTime, begTime_s->valuestring+2, 2);		
            alarm_list[i].min = atoi(sBegTime);
			alarm_list[i].valid =  1;
			Wk = 0;
			for(k=0;k < 7; k++)
			{
				Wk |= (1 << k);
			}
			alarm_list[i].repeat_bitmap = Wk; 
			alarm_list[i].repeat_bitmap |= 0x80;

			#if USE_LV_WATCH_CYCLE_ALARM != 0 || USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
            alarm_list[i].type = ALARM_TYPE_POWERON;
            WS_PRINTF("CWathcService_OpenClose222(i:%d) type:%d",i,alarm_list[i].type );
			#endif
			#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
            alarm_list[i].ring_en = nv_alarm.alarm_info[i].ring_en;
            alarm_list[i].vib_en = nv_alarm.alarm_info[i].vib_en;
			#endif
			#if USE_LV_WATCH_ALARM_S2C_NAME!=0
			strncpy(alarm_list[i].name,nv_alarm.alarm_info[i].name,40);
			#endif

		}
		else
		{
            alarm_list[i].on_off = nv_alarm.alarm_info[i].on_off;
            alarm_list[i].hour =  nv_alarm.alarm_info[i].hour;
            alarm_list[i].min = nv_alarm.alarm_info[i].min;
            alarm_list[i].repeat_bitmap = nv_alarm.alarm_info[i].repeat_bitmap; 
			alarm_list[i].valid =  nv_alarm.alarm_info[i].valid;

			#if USE_LV_WATCH_CYCLE_ALARM != 0 || USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
            alarm_list[i].type = nv_alarm.alarm_info[i].type;
            WS_PRINTF("CWathcService_OpenClose33(i:%d) type:%d",i,alarm_list[i].type );
			#endif
			#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
            alarm_list[i].ring_en = nv_alarm.alarm_info[i].ring_en;
            alarm_list[i].vib_en = nv_alarm.alarm_info[i].vib_en;
			#endif
			#if USE_LV_WATCH_ALARM_S2C_NAME!=0
			strncpy(alarm_list[i].name,nv_alarm.alarm_info[i].name,40);
			#endif

		}
        WS_PRINTF("CWathcService_OpenClose() 33alarm_list[%d]%d:%d,onoff=%d, %02x", i, alarm_list[i].hour,alarm_list[i].min,alarm_list[i].on_off,alarm_list[i].repeat_bitmap);


	}
	alarm_set_alarm(alarm_list, NV_ALARM_MAX_ALARM_NUM);
	lv_mem_free(alarm_list);


	
	//powoff
	memcpy(sEndTime, endTime_s->valuestring, 2);
	tempvalue.hour = atoi(sEndTime);
	memset(sEndTime,0,sizeof(sEndTime));
	memcpy(sEndTime, endTime_s->valuestring+2, 2);
	tempvalue.min = atoi(sEndTime);
	tempvalue.on_off = tempvalue.valid = 1;
	Wk = 0;
	for(k=0;k < 7; k++)
	{
		Wk |= (1 << k);
	}
	tempvalue.repeat_bitmap = Wk;
	
	WatchUtility_SavePwroff_info(tempvalue,0,0);
	myNVMgr_SetWsPwroffClock();
#endif	
}

//rcv:136,pklen:116,Cnt:1,Idx:0,type:3d6 :{"type":"terminal","command":"setOnoff","request":{"taskId":56036,"openCloseAlert":1,"allowClose":0,"batteryLow":1}} 
static void CWathcService_SetOnOff(ws_client * pme, void *json,ws_nv_switch *pSwitch)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *batteryLow_s = NULL;	
    ws_cJSON *allowClose_s = NULL;	
    ws_cJSON *openCloseAlert_s = NULL;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pSwitch)
    {
        return;
    }
	
	userData->type = KAER_CMD_SET_ONOFF;

    memset(pSwitch, 0, sizeof(ws_nv_switch));
    myNVMgr_GetWsSwitch(pSwitch);
    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}
	WS_PRINTF("req_s is %s",req_s->string);
	batteryLow_s = ws_cJSON_GetObjectItem(req_s , "batteryLow");
    if(batteryLow_s)
    {
    	if(batteryLow_s->type == ws_cJSON_String)
   		 	pSwitch->batteryLow = atoi(batteryLow_s->valuestring);
   		 else if(batteryLow_s->type == ws_cJSON_Number)
   		 	pSwitch->batteryLow = batteryLow_s->valueint;
    }

	allowClose_s = ws_cJSON_GetObjectItem(req_s , "allowClose");
	
    if(allowClose_s)
    {
    	WS_PRINTF("allowClose_s is %s %d type is %d",allowClose_s->valuestring, allowClose_s->valueint,allowClose_s->type);
    	if(allowClose_s->type == ws_cJSON_String)
   		 	pSwitch->allowClose = atoi(allowClose_s->valuestring);
   		 else if(allowClose_s->type == ws_cJSON_Number)
   		 	pSwitch->allowClose = allowClose_s->valueint;
    }

	openCloseAlert_s = ws_cJSON_GetObjectItem(req_s , "openCloseAlert");
    if(openCloseAlert_s)
    {
    	if(openCloseAlert_s->type == ws_cJSON_String)
   		 	pSwitch->openCloseAlert = atoi(openCloseAlert_s->valuestring);
   		 else if(openCloseAlert_s->type == ws_cJSON_Number)
   		 	pSwitch->openCloseAlert = openCloseAlert_s->valueint;
    }

	WS_PRINTF("batteryLow %d, allowClose :%d,openCloseAlert:%d"
				, pSwitch->batteryLow, pSwitch->allowClose, pSwitch->openCloseAlert);
}

static void CWathcService_GetAlarmInfo(ws_client * pme, void *json,ws_alarm_list *alarm_list)
{
    ws_cJSON             *root = (ws_cJSON *)json;
    ws_cJSON            *alarm_array_s = NULL;

    if(NULL == root || NULL == alarm_list)
    {
        return;
    }

    alarm_array_s = ws_cJSON_GetObjectItem(root, "alarm");
    if(alarm_array_s){
        ws_cJSON *alarm_s = NULL;
        ws_cJSON *start_s = NULL;
        ws_cJSON *week_s = NULL;
        ws_cJSON *text_s = NULL;
        ws_cJSON *status_s = NULL;
        int i, cnt = ws_cJSON_GetArraySize(alarm_array_s);

        alarm_list->count = cnt;
        alarm_list->single = 0xff;
        alarm_list->alarm = NULL;
        if(cnt>0)
        {
            alarm_list->alarm = (ws_alarm*)lv_mem_alloc(cnt*sizeof(ws_alarm));
			if(alarm_list->alarm == NULL) return;
			memset(alarm_list->alarm, 0, cnt*sizeof(ws_alarm));
        }

        for(i=0; i<cnt; i++)
        {
            alarm_s = ws_cJSON_GetArrayItem(alarm_array_s, i);
            start_s = ws_cJSON_GetObjectItem(alarm_s, "start");
            week_s = ws_cJSON_GetObjectItem(alarm_s, "week");
            text_s = ws_cJSON_GetObjectItem(alarm_s, "text");
            status_s = ws_cJSON_GetObjectItem(alarm_s, "status");
            if(start_s && start_s && week_s && status_s)
            {
                const uint8_t wrp[7] = {1,2,3,4,5,6,0};
                uint8_t w;
                
                alarm_list->alarm[i].id = i;
                alarm_list->alarm[i].ring = 0;
                alarm_list->alarm[i].is_on = status_s->valueint;
                strcpy(alarm_list->alarm[i].name, text_s->valuestring);
                strcpy(alarm_list->alarm[i].time, start_s->valuestring);
                strcat(alarm_list->alarm[i].time, ":00");
                
                alarm_list->alarm[i].repeat = 0;
                for(w=0; w<7; w++)
                {
                    if((week_s->valuestring[w])=='1')
                    {
                        alarm_list->alarm[i].repeat += (1<<wrp[w]);
                        alarm_list->alarm[i].repeat |= 0x80;
                    }
                }
                WS_PRINTF("CWathcService_GetAlarmInfo() >> alarm_list->alarm[%d].repeat:%02x", i, alarm_list->alarm[i].repeat);
              
            }
        }   
    }

    return;
}

static void CWathcService_GetLocInterval(ws_client * pme, void *json,uint32_t *interval)
{
    ws_kaer_t   *userData = (ws_kaer_t*)pme->adapter->data;
    ws_cJSON          *root = (ws_cJSON *)json;
    ws_cJSON          *json_interval = NULL;
    
    if(NULL == root || NULL == interval)
    {
        return;
    }

    json_interval = ws_cJSON_GetObjectItem(root, "second");
    if(json_interval){
        *interval = (uint32_t)json_interval->valueint;
        userData->m_base.ws_reg.upPosTime = json_interval->valueint;
    }
    WS_PRINTF("CWatchService--CWathcService_GetLocInterval >> interval:%d",*interval, 0, 0);
    return;

}

static void CWathcService_GetWitelistOn(ws_client * pme, void *json,bool *on)
{
    ws_cJSON             *root = (ws_cJSON *)json;
    ws_cJSON            *on_s = NULL;
    
    if(NULL == root || NULL == on)
    {
        return;
    }

    on_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, "on");
    
    *on = (on_s->valueint == 1) ? TRUE : FALSE;
}

static void CWathcService_GetVolume(ws_client * pme, void *json,int16_t *volume)
{
    ws_cJSON             *root = (ws_cJSON *)json;
    ws_cJSON          *json_volume = NULL;
    
    if(NULL == root || NULL == volume)
    {
        return;
    }

    json_volume = ws_cJSON_GetObjectItem(root, "level");
    if(json_volume){
        *volume = (int16_t)json_volume->valueint;
    }
    
    WS_PRINTF("CWatchService--CWathcService_GetVolume >> volume:%d",*volume, 0, 0);
    return;
}

static void CWathcService_GetMonitorNum(ws_client * pme, void *json,char *phone)
{
    ws_cJSON             *root = (ws_cJSON *)json;
    ws_cJSON          *json_phone = NULL;
    
    if(NULL == root || NULL == phone)
    {
        return;
    }

    json_phone = ws_cJSON_GetObjectItem(root, "phone");
    if(json_phone){
        if(WS_CONTACT_PHONE_MAX_LEN >= strlen(json_phone->valuestring)){
            strncpy(phone, json_phone->valuestring, strlen(json_phone->valuestring));
        }
    }
    WS_PRINTF("CWatchService--CWathcService_GetMonitorNum >> phone:%s",phone, 0, 0);
    return;
}

///鍙戦€佸懆鏈熷畾
static void CWatchService_SendPeriodLocation(ws_client *pme,LOCATION_UP_TYPE UpType)
{
    hal_rtc_t rtc_curr;
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_cJSON  *root = NULL;
    ws_location *location = &userData->m_base.location;
	
	uint32_t IsSameLaction = 0;

    if(pme==NULL)
    {
        return;
    }

    userData->type = KAER_CMD_IND_POS;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }

    Hal_Rtc_Gettime(&rtc_curr);
    location->time = UiGetUnixTimeStamp(&rtc_curr);

    CWatchService_LocationDataJson(userData,root, location,UpType);
	#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
	if(CWatchService_Get_First_Peroidloct() == 0)
	{
		ws_printf(
"----CWatchService_SendPeriodLocation firsttttt");
	//fist location ,send data
		CWatchService_Set_First_Peroidloct(1);
		if(g_common_pos_timer == NULL)
		{
			uos_timer_create(&g_common_pos_timer);   
		}
		g_pos_uptime_count = 0;
		uos_timer_stop(g_common_pos_timer);
		uos_timer_start(g_common_pos_timer, 60*TICKES_IN_SECOND, 60*TICKES_IN_SECOND, CWatchService_LocalPosTimerCB, (uint32_t)pme);
		CWatchService_SendJsonData(pme, root);
	}
	else
	{
		//has send location ,here only save data
		char *jsonstr = NULL;
		if(root != NULL)
		{    	   
		    jsonstr = ws_cJSON_PrintUnformatted(root);
		    ws_cJSON_Delete(root);
			if(jsonstr)
			{
	    	WS_PRINTF("CWatchService____saveSendJsonData >> %s", jsonstr);
				#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0 && USE_LV_WATCH_MUITI_GROUP_LOC_REPORT != 0
				if(g_r_pos_num < SAVE_LOC_DATA_MAX_GROUP)
				{
					memset(g_r_pos_data[g_r_pos_num],0,LOCATION_MAX_LEN);
					strcpy(g_r_pos_data[g_r_pos_num],jsonstr);
					g_r_pos_num++;
				}
				else
				{
					for(IsSameLaction=0; IsSameLaction<SAVE_LOC_DATA_MAX_GROUP-1; IsSameLaction++)
					{
						memset(g_r_pos_data[IsSameLaction],0,LOCATION_MAX_LEN);
						strcpy(g_r_pos_data[IsSameLaction] ,g_active_per_min[IsSameLaction+1]);
					}

				}
				memset(g_r_pos_data[SAVE_LOC_DATA_MAX_GROUP-1],0,LOCATION_MAX_LEN);
				strcpy(g_r_pos_data[SAVE_LOC_DATA_MAX_GROUP-1],jsonstr);
				#else
				memset(g_pos_data,0,sizeof(g_pos_data))	;
				strcpy(g_pos_data,jsonstr);
				#endif
				free(jsonstr);
			}
		}
	}
	#else
    CWatchService_SendJsonData(pme, root);
	#endif

}

static void CWatchService_SendImmediateLocation(ws_client *pme, LOCATION_UP_TYPE UpType)
{
	//ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_location* location = &userData->m_base.location;
	ws_cJSON *locationJs = NULL;
    ws_cJSON  *root = NULL;
	WS_PRINTF("CWatchService_GetCurPos_GetNetCellInfoCB--\n");

    if(pme==NULL)
    {
        return;
    }	
	userData->type = KAER_CMD_POS_REQ;
    
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
   
    locationJs = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_RSP,locationJs);
	ws_cJSON_AddStringToObject(locationJs,STRING_RESULT,"1");

	PositionToJson(userData,locationJs, location, UpType);

    CWatchService_SendJsonData(pme, root);
}

static void CWatchService_SendSosLocation(ws_client *pme, LOCATION_UP_TYPE UpType)
{
	//ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;
	WS_PRINTF("CWatchService_SendSosLocation--%x\n", UpType);
	
    ws_cJSON  *root = NULL;    

	char valueStr[30]={0};	
	ws_cJSON *req_s = NULL;
	hal_rtc_t rtc_curr;   
	float lonf=0.00;
	float latf=0.00;
	
	Hal_Rtc_Gettime(&rtc_curr);
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_IND_ALARM;
	
	req_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req_s);
	#if USE_LV_WATCH_SPAIN != 0

	if(userData->mLocationType == LOCATION_TYPE_SOS)
		sprintf(valueStr, "%d", 3);  //sos报警
	else if(userData->mLocationType == LOCATION_TYPE_FALL)
		sprintf(valueStr, "%d", 4);  //跌倒报警
	else if(userData->mLocationType == LOCATION_TYPE_CHARGER_CONNECTED)
		sprintf(valueStr, "%d", 5);  //充电器连接
	else if(userData->mLocationType == LOCATION_TYPE_CHARGER_DISCONNECTED)
		sprintf(valueStr, "%d", 6);  //充电器断开
	else if(userData->mLocationType == LOCATION_TYPE_BATTERY_LOW)
		sprintf(valueStr, "%d", 7);  //低电报警
	#else
	sprintf(valueStr, "%d", LOCATION_TYPE_SOS);  //报警类型
	#endif
	ws_cJSON_AddStringToObject(req_s,STRING_TYPE,valueStr);

	sprintf(valueStr, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,
			rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);
	ws_cJSON_AddStringToObject(req_s,"time",valueStr);

#if USE_LV_WATCH_LOCATION_BLEMAC != 0
	if(UpType == LOCATION_UP_TYPE_BLE)
	{
		ws_cJSON_AddStringToObject(req_s,"mode","3");
	}
#endif
	PositionToJson(userData, req_s, loc_data, UpType);

    CWatchService_SendJsonData(pme, root);//发送报警
}

static void CWatchService_SendLocation(ws_client *pme,LOCATION_UP_TYPE UpType)
{
	if(pme == NULL)
	{
		return;
	}

	char *posData = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_location *location = &(userData->m_base.location); 

	// userData->m_base.m_nLastTimeForUploadLoc = GETTIMESECONDS();
#if USE_LV_WATCH_ONLINE_CAR_HAILING !=0
	if(lv_watch_get_activity_obj(ACT_ID_WAITING_DEST_SELE)||lv_watch_get_activity_obj(ACT_ID_END_OF_TRIP))
	{
		posData = (char*)malloc(1000);
		int len = CWatchService_BuildPosData(pme, posData, UpType);		
	}
#endif
	#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
	if((g_serching_mac == 1)&&(userData->mLocationType == LOCATION_TYPE_IMMEDIATE))
	{
		#if USE_LV_WATCH_ONLINE_CAR_HAILING !=0
    	if(lv_watch_get_activity_obj(ACT_ID_WAITING_DEST_SELE)||lv_watch_get_activity_obj(ACT_ID_END_OF_TRIP))
    	{
    		location_status=0;
			memset(taxi_posData,0,sizeof(taxi_posData));
			strcpy(taxi_posData,posData);
			free(posData);
			return;
    	}
		else if(location_status ==1)
		{
			location_status=0;
			printf("zu zhi shangbao-------------------");
			return;
		}
		#endif
		
		CWatchService_SendGohomeLocation(pme, UpType);
	}
	else
	#endif
	if(userData->mLocationType == LOCATION_TYPE_PERIOD)  
		CWatchService_SendPeriodLocation(pme, UpType);
	else if(userData->mLocationType == LOCATION_TYPE_IMMEDIATE)  //绔嬪嵆瀹氫綅
	{
	#if USE_LV_WATCH_ONLINE_CAR_HAILING !=0
    	if(lv_watch_get_activity_obj(ACT_ID_WAITING_DEST_SELE)||lv_watch_get_activity_obj(ACT_ID_END_OF_TRIP))
    	{
    		location_status=0;
			memset(taxi_posData,0,sizeof(taxi_posData));
			strcpy(taxi_posData,posData);
			free(posData);
			return;
    	}
		else if(location_status ==1)
		{
			location_status=0;
			printf("zu zhi shangbao-------------------");
			return;
		}
	#endif
	
		CWatchService_SendImmediateLocation(pme, UpType);
	}
	#if USE_LV_WATCH_SPAIN != 0
	else if(userData->mLocationType == LOCATION_TYPE_FALL
	||userData->mLocationType == LOCATION_TYPE_CHARGER_CONNECTED
	||userData->mLocationType == LOCATION_TYPE_CHARGER_DISCONNECTED
	||userData->mLocationType == LOCATION_TYPE_BATTERY_LOW)	//跌倒报警
		CWatchService_SendSosLocation(pme, UpType); 
	#endif
	else if(userData->mLocationType == LOCATION_TYPE_SOS)	//SOS瀹氫綅
		CWatchService_SendSosLocation(pme, UpType); 
	//  uos_timer_stop(pme->adapter->timer_common);
    //  uos_timer_start(pme->adapter->timer_common, 30*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, CWatchService_CommonTimerCB, (uint32_t)pme);

#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
	if(userData->mLocationType != LOCATION_TYPE_PERIOD)
	{
		g_pos_uptime_count = 0;
		if(g_common_pos_timer != NULL)
		{
			WS_PRINTF("CWatchService_UploadPostion():RECONTTTTTT");
			uos_timer_stop(g_common_pos_timer);
			uos_timer_start(g_common_pos_timer, 60*TICKES_IN_SECOND, 60*TICKES_IN_SECOND, CWatchService_LocalPosTimerCB, (uint32_t)pme);
		}
	}	
#endif
	Flw_UpdateLactionData(userData,location);
}

static void CWathcService_SetFotaData(ws_client * pme, void *json)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *http = NULL;
	ws_cJSON *version = NULL;
	char http_1[100] = {0};
	char http_2[100] = {0};
		
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root)
    {
        return;
    }
	
	userData->type = KAER_CMD_SET_ADDR;

    req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}
	
#if UPGRADE_FOTA_MSRS != 0
	ke_fota_set_start_flag(1,NULL);
#elif FOTA_KAER != 0		
	version = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "versionName");
	if(!version)
    {
    	return;
    }
	char *sptr = strchr(version->valuestring,'-');
	if(sptr !=NULL)
	{
		*sptr = 0;
	}
	WS_PRINTF("[%s:%s:%d]:cJSON_Parse=%s,%s\n",__FILE__,__FUNCTION__,__LINE__,version->valuestring,app_adaptor_get_external_version(1));
	int ret =strncmp(version->valuestring, app_adaptor_get_external_version(1), strlen(app_adaptor_get_external_version(1)));
	if(ret != 0)
	{
		return;
	}

	http = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "httpUrl");
	if(!http)
    {
    	return;
    }

	strcpy(http_1, http->valuestring);
	strcpy(http_2, http->valuestring);
	http_2[strlen(http_2)-1] = '2';
	WS_PRINTF("CWatchService--CWathcService_SetFotaData >> http_1 = %s, http_2 = %s",  http_1, http_2);
	ke_fota_set_bin_url(http_1, http_2);
	ke_fota_set_start_flag(1,http_1);
#endif	
}

#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
void CWathcService_init_GoScene_WifiMac(uint8_t is_clear)
{
	uint8_t i = 0;
	for(i=0;i<WIFI_SCENE_TYPE_MAX;i++)
	{
		memset(&g_scene_mac.ws_gscene[i],0,sizeof(nv_watch_scene_info));
	}
	g_scene_mac.ws_scene_type=0;
	memset(&g_scene_mac,0,sizeof(nv_watch_goscene_wifimac_t));
	if(is_clear == 1)
		UI_NV_Write_Req(NV_SECTION_GOSCENE_WIFIMAC_INFO, 0,sizeof(nv_watch_goscene_wifimac_t), (uint8_t*)&g_scene_mac);
	else
		UI_NV_Read_Req(NV_SECTION_GOSCENE_WIFIMAC_INFO, 0,sizeof(nv_watch_goscene_wifimac_t), (uint8_t*)&g_scene_mac);
	WS_PRINTF("!!!!CWathcService_GetGoScene_WifiMac type 2222=%x",g_scene_mac.ws_scene_type );
}

//sflag=0 clear classroom,sflag=1 schoollbus,sflag=2 dormitory
void CWathcService_ClearGoScene_WifiMac(uint8_t sflag,uint8_t need_save)
{
	uint8_t i = 0;
	WS_PRINTF("!!!!CWathcService_ClearGoScene_WifiMac type sflag = %d,%d",sflag ,need_save);
	for(i=0;i<ONE_SCENE_WIFI_MAX_NUM;i++)
	{
		memset(g_scene_mac.ws_gscene[sflag].gscene_mac[i],0,(sizeof(uint8_t)*30));
	}
	g_scene_mac.ws_scene_type &= ~(1 << sflag) ;
	if(need_save == 1)
	{
		UI_NV_Write_Req(NV_SECTION_GOSCENE_WIFIMAC_INFO, 0,sizeof(nv_watch_goscene_wifimac_t), (uint8_t*)&g_scene_mac);
	}
}

uint8_t CWathcService_IsOtherScene_WifiMac(char *smacw)
{
	uint8_t i = 0,j=0;
	for(i=0;i<WIFI_SCENE_TYPE_MAX;i++)
	{
		if((g_scene_mac.ws_scene_type & (1 << i)) == 0)
			;
		else
		{
			for(j=0;j<ONE_SCENE_WIFI_MAX_NUM;j++)
			{
				if((strlen(g_scene_mac.ws_gscene[i].gscene_mac[j]) > 0) && (strstr(smacw,g_scene_mac.ws_gscene[i].gscene_mac[j]) != NULL))
				{
					WS_PRINTF("CWathcService_IsOtherScene_WifiMac okkkkkkk!![%d][%d] =%s",i,j,g_scene_mac.ws_gscene[i].gscene_mac[j]);
					return 1;
				}
			}
		}
	}
	return 0;
}

uint8_t CWathcService_IsGohome_WifiMac(char *smacw)
{
	if(strlen(g_gohome_mac.ws_ghome_mac)>0)
	{
		WS_PRINTF("CWathcService_IsGohome_WifiMac!!!!!!=%s",g_gohome_mac.ws_ghome_mac);
		if(strstr(smacw,g_gohome_mac.ws_ghome_mac)!=NULL)
			return 1;
	}
	//check all other scene
	if(CWathcService_IsOtherScene_WifiMac(smacw) == 1)
		return 1;
	return 0;
}

void CWathcService_SaveGohome_WifiMac(char *sbufs)
{
	memset(g_gohome_mac.ws_ghome_mac,0,sizeof(g_gohome_mac));
	if(sbufs != NULL)
	{
		strcpy(g_gohome_mac.ws_ghome_mac,sbufs);
	}
	UI_NV_Write_Req(NV_SECTION_GOHOME_WIFIMAC_INFO, 0,sizeof(nv_watch_gohome_wifimac_t), (uint8_t*)&g_gohome_mac);
}

void CWathcService_GetGohome_WifiMac()
{
	memset(g_gohome_mac.ws_ghome_mac,0,sizeof(g_gohome_mac));
	UI_NV_Read_Req(NV_SECTION_GOHOME_WIFIMAC_INFO, 0,sizeof(nv_watch_gohome_wifimac_t), (uint8_t*)&g_gohome_mac);
	WS_PRINTF("!!!!CWathcService_GetGohome_WifiMac=%s",g_gohome_mac.ws_ghome_mac);
}

void CWathcService_SetGohome_WifiMac(ws_client * pme, void *json)
{
    ws_cJSON     *root_parse = (ws_cJSON *)json;
    ws_cJSON        *pkId_s = NULL;
	 ws_cJSON        *pkId2_s = NULL;
	  ws_cJSON        *pkId3_s = NULL;
    uint16_t          i,cnt = 0;
     ws_cJSON *request_s = NULL;
     char sbuf[2] = {0};
	 uint8_t sfytpe = 0,savlue = 0;
	 int Index[10] =  {0};
	 char *Data = NULL;

    request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(!request_s)
    {
    	return;
    }

	pkId_s = ws_cJSON_GetObjectItem(request_s, "swit");
    if(!pkId_s)
    {
    	return;
    }
	pkId3_s = ws_cJSON_GetObjectItem(request_s, "scene");
	if(!pkId3_s)
    {
    	WS_PRINTF("CWathcService_SetGohome_WifiMac---scene--- ");
    	sfytpe = 1;
		savlue = 1;
    }
	else
	{
		savlue = pkId3_s->valueint;
		sfytpe = pkId3_s->valueint;
		WS_PRINTF("CWathcService_SetGohome_WifiMac---------=%d-------= ",sfytpe);
	}
	strcpy(sbuf,pkId_s->valuestring);
	WS_PRINTF("CWathcService_SetGohome_WifiMac--------onoff---------=%d",atoi(sbuf));
	g_serching_mac = 0;
	if(atoi(sbuf) == 0)
	{
	//close
		if(sfytpe <= 1)
		{
			//clear save family mac addr
			memset(g_gohome_mac.ws_ghome_mac,0,sizeof(g_gohome_mac));
			UI_NV_Write_Req(NV_SECTION_GOHOME_WIFIMAC_INFO, 0,sizeof(nv_watch_gohome_wifimac_t), (uint8_t*)&g_gohome_mac);
			//clear family mac addr
			CWathcService_ClearGoScene_WifiMac(0,1);
		}
		else
		{
			if(savlue >= 2)
				savlue -= 1;
			//clear other mac addr
			CWathcService_ClearGoScene_WifiMac(savlue,1);
		}
	}
	else
	{
	//open
		pkId2_s = ws_cJSON_GetObjectItem(request_s, "mac");
	    if(!pkId2_s)
	    {
	    	return;
	    }
		if(strlen(pkId2_s->valuestring)>2)
		{
			uint8_t sis = 0;
			WS_PRINTF("CWathcService_SetGohome_WifiMac macc111[%d] =%s",strlen(pkId2_s->valuestring),pkId2_s->valuestring);
			if(sfytpe <= 1 )
			{
			//is family mac
			//save mac
				memset(g_gohome_mac.ws_ghome_mac,0,sizeof(g_gohome_mac.ws_ghome_mac));
				if(sfytpe == 1)
					sfytpe = 0;
				CWathcService_ClearGoScene_WifiMac(sfytpe,0);
			//return low
				Data = pkId2_s->valuestring;
				cnt = split_string(Data, '|', NULL, Index, 10);
				
				WS_PRINTF("CWathcService_SetGohome_WifiMac macc000 cnt[%d]=%d",sfytpe,cnt);
				
				g_scene_mac.ws_scene_type |= (1 << sfytpe);

				for(i=0;i<cnt;i++)
				{
					char *Dat = Data + Index[i];
					if(i == 0)
					{
						ke_common_string_HighToLow(Dat,g_gohome_mac.ws_ghome_mac);
						WS_PRINTF("CWathcService_SetGohome_WifiMac macc home[ 000] =%s",g_gohome_mac.ws_ghome_mac);
					}
					else
					{
						ke_common_string_HighToLow(Dat,g_scene_mac.ws_gscene[sfytpe].gscene_mac[i]);
						WS_PRINTF("CWathcService_SetGohome_WifiMac macc home[ %d] =%s",i,g_scene_mac.ws_gscene[sfytpe].gscene_mac[i]);
					}
				}
				
				UI_NV_Write_Req(NV_SECTION_GOHOME_WIFIMAC_INFO, 0,sizeof(nv_watch_gohome_wifimac_t), (uint8_t*)&g_gohome_mac);
				UI_NV_Write_Req(NV_SECTION_GOSCENE_WIFIMAC_INFO, 0,sizeof(nv_watch_goscene_wifimac_t), (uint8_t*)&g_scene_mac);
			}
			else
			{
				//is  classromm ,shcoolbus,dormitory
				if(sfytpe >= 2)
					sfytpe -= 1;
				Data = pkId2_s->valuestring;
				cnt = split_string(Data, '|', NULL, Index, 10);
				
				WS_PRINTF("CWathcService_SetGohome_WifiMac macc cnt[%d]=%d",sfytpe,cnt);
				
				CWathcService_ClearGoScene_WifiMac(sfytpe,0);
				g_scene_mac.ws_scene_type |= (1 << sfytpe);
				
				for(i=0;i<cnt;i++)
				{
					char *Dat = Data + Index[i];	
					//return low
					ke_common_string_HighToLow(Dat,g_scene_mac.ws_gscene[sfytpe].gscene_mac[i]);
					WS_PRINTF("CWathcService_SetGohome_WifiMac macc[%d]=%s,=%x",i,g_scene_mac.ws_gscene[sfytpe].gscene_mac[i],g_scene_mac.ws_scene_type);
				}
				UI_NV_Write_Req(NV_SECTION_GOSCENE_WIFIMAC_INFO, 0,sizeof(nv_watch_goscene_wifimac_t), (uint8_t*)&g_scene_mac);
			}
		}
		else if(sfytpe <=1 )
		{
			WS_PRINTF("CWathcService_SetGohome_WifiMac macc2222=%s",pkId2_s->valuestring);
			//is family mac,mac is null,start local
			g_serching_mac = 1;
			Flw_NotPeridLocatinConfig(LOCATION_TYPE_IMMEDIATE);
			//family save one wifi
			CWathcService_ClearGoScene_WifiMac(0,1);
		}
		else
		{
		//mac is null,other scene ,clear function
			if(sfytpe >= 2)
				sfytpe -= 1;
			CWathcService_ClearGoScene_WifiMac(sfytpe,1);
		}
	}

	CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_GOHOME_WIFI, 0);
}

void PositionToJson_Gohome(ws_kaer_t *userData,ws_cJSON *LocationJs, ws_location* location, LOCATION_UP_TYPE UpType)
{
	char Macs[500] = {0};
	WS_PRINTF("PositionToJson_Gohome()GetGpsOnceFixed is %d UpType is %d",GetGpsOnceFixed(),UpType); 
	if((UpType == LOCATION_UP_TYPE_LAST) && (userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_WIFI))	
	{
        for(int i=0; i<userData->mHisLocationInfo.mLastUpWifi.wifi_count; i++)
        {
            char Macsi[50] = {0};
            sprintf(Macsi,"%s,%s,ssid_%d",userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac,userData->mHisLocationInfo.mLastUpWifi.wifs[i].rssi,i);
            strcat(Macs,Macsi);
			CWathcService_SaveGohome_WifiMac(userData->mHisLocationInfo.mLastUpWifi.wifs[i].mac);
        	break;//only get one
        }

        WS_PRINTF("PositionToJson_Gohome() send the same WIFI location");
        g_serching_mac = 0;
    }
    else if((UpType == LOCATION_UP_TYPE_WIFI) && 
   			 (location->wifi)&&
   			 (location->wifi->wifi_count>2))
    {
    
        for(int i=0; i<location->wifi->wifi_count; i++)
        {
            char Macsi[50] = {0};
            sprintf(Macsi,"%s,%s,ssid_%d",location->wifi->wifs[i].mac,location->wifi->wifs[i].rssi,i);
            strcat(Macs,Macsi);
			CWathcService_SaveGohome_WifiMac(location->wifi->wifs[i].mac);
			break;//only get one
        }

        WS_PRINTF("PositionToJson_Gohome()WIFI location22222");
        g_serching_mac = 0;
    }
	ws_cJSON_AddStringToObject(LocationJs,"macs",Macs);
}

static void CWatchService_SendGohomeLocation(ws_client *pme, LOCATION_UP_TYPE UpType)
{
	//ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_location* location = &userData->m_base.location;
	ws_cJSON *locationJs = NULL;
    ws_cJSON  *root = NULL;
	WS_PRINTF("CWatchService_GetCurPos_GetNetCellInfoCB--\n");

    if(pme==NULL)
    {
        return;
    }	
	userData->type = KAER_CMD_UP_GOHOME_WIFI;
    
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
   
    locationJs = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_REQ,locationJs);

	PositionToJson_Gohome(userData,locationJs, location, UpType);

    CWatchService_SendJsonData(pme, root);
}
#endif

#if USE_LV_WATCH_LOCATION_BLEMAC != 0
static void * Location_Ble_Match_Timer = NULL;
static void * Location_Ble_Close_Timer = NULL;
#define BLE_SCAN_TIME_OUT    15
#define BLE_CLOSE_TIME_OUT   3
#define BLACK_MAC_LEN   	17// strlen( 06:83:41:A0:08:82  )

void (*Location_func)()=NULL;
uint16_t Blacklist_Mac_Count = 0;
char *Black_mac_list = NULL;
uint8_t userDataBLE_Addr[50] = {0};
uint8_t userDataBLE_Rssi = 0;
nv_watch_location_blemac_t location_short_blemac;

void CWatchService_Ble_Close_Preper_Time_Exit()
{
    uint32_t length = sizeof(nv_watch_bluetooth_t);
    nv_watch_bluetooth_t * nvm = (nv_watch_bluetooth_t *)lv_mem_alloc(length);
    if(length != UI_NV_Read_Req(NV_SECTION_UI_BLUETOOTH, 0, length, (uint8_t *)nvm)) 
	{
        printf("read bluetooth from nvm error in %s\n",__FUNCTION__);
        lv_mem_free(nvm);
        return NULL;
    }
	if(nvm->on_off != 0) 
	{	
		bt_close_device();
		bt_list_destroy();
		bluetooth_nvm_save_switch_state(false);  
	}
	lv_mem_free(nvm);
}

void CWatchService_Ble_Close_Preper_Timer_Over(uint32_t p)
{
	if(Location_Ble_Close_Timer != NULL)							
	{			 								
		uos_timer_stop(Location_Ble_Close_Timer);
		Location_Ble_Close_Timer = NULL;
	}							
	MMI_ModemAdp_Rpc_Req(CWatchService_Ble_Close_Preper_Time_Exit, 0, 0, 0);
}

void CWatchService_Location_Ble_Close_func()
{
	if(Location_Ble_Close_Timer==NULL)							
	{											
		uos_timer_create(&Location_Ble_Close_Timer);							
	}							
	uos_timer_start(Location_Ble_Close_Timer, TICKES_IN_SECOND*BLE_CLOSE_TIME_OUT, 0, CWatchService_Ble_Close_Preper_Timer_Over, (uint32_t)0);
}

void CWatchService_Location_Ble_Mac_Match(struct bt_event_le_scan_event * scan)
{	
	char ble_addr[20]={0};
	uint8_t i = 0,j=0;
	memset(ble_addr,0,20);
	sprintf(ble_addr, "%02x:%02x:%02x:%02x:%02x:%02x",scan->address.bytes[5],scan->address.bytes[4],
							scan->address.bytes[3],scan->address.bytes[2],scan->address.bytes[1],scan->address.bytes[0]);
	ke_common_string_high_to_Low(ble_addr,ble_addr);
	for(uint8_t cnt=0;cnt < location_short_blemac.blemac_cnt;cnt++)
	{
		if(memcmp(ble_addr, location_short_blemac.ws_blemac[cnt].location_blemac,strlen( location_short_blemac.ws_blemac[cnt].location_blemac))==0)
		{
			for(uint16_t black_cnt=0;black_cnt<Blacklist_Mac_Count;black_cnt++)
			{	
				if(memcmp(ble_addr,&Black_mac_list[black_cnt*BLACK_MAC_LEN],strlen(ble_addr))==0)
				{	
					ws_printf("%s ble_addr in the Black mac list %s \n", __FUNCTION__,ble_addr);
					return;
				}
			}
			ws_printf("CWatchService_Location_Ble_Mac_Match()find ble addr %s RSSI:%ddBm   short_ble_addr %s \n",ble_addr,scan->rssi,location_short_blemac.ws_blemac[cnt].location_blemac);
			if(scan->rssi > userDataBLE_Rssi)
			{
				userDataBLE_Rssi = scan->rssi;
				memset(userDataBLE_Addr,0,sizeof(userDataBLE_Addr));
				sprintf(userDataBLE_Addr,"%s",ble_addr);
			}
		}
	
	}
#if 0
	for(i=0;i<WIFI_SCENE_TYPE_MAX;i++)
	{
		if((g_scene_mac.ws_scene_type & (1 << i)) == 0)
			;
		else
		{
			ws_printf("Ble location scene Num: %d",i);
			for(j=0;j<ONE_SCENE_WIFI_MAX_NUM;j++)
			{
				if((strlen(g_scene_mac.ws_gscene[i].gscene_mac[j]) > 0) 
					&& (memcmp(ble_addr,g_scene_mac.ws_gscene[i].gscene_mac[j],strlen(ble_addr))==0))
				{
					WS_PRINTF("CWathcService_IsOtherScene_bleMac okkkkkkk!![%d][%d] =%s",i,j,g_scene_mac.ws_gscene[i].gscene_mac[j]);
					ble_jrp_client(false, NULL);
					Close_Ble_func();
					if(Location_Ble_Match_Timer != NULL)							
					{			 								
						uos_timer_stop(Location_Ble_Match_Timer);
						Location_Ble_Match_Timer = NULL;
					}	
					ws_client *pme = MMI_ModemAdp_WS_GetClient();
					ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
					memset(userData->BLE_Addr,0,sizeof(userData->BLE_Addr));
					sprintf(userData->BLE_Addr,"%s",ble_addr);
					Flw_SendMsgUpLocation(LOCATION_UP_TYPE_BLE);

					return;
				}
			}
		}
	}
#endif
}

void CWatchService_Ble_Scan_Over_time()
{
	ble_jrp_client(false, NULL);
	CWatchService_Location_Ble_Close_func();
	if(strlen(userDataBLE_Addr) != 0)
	{
		ws_client *pme = MMI_ModemAdp_WS_GetClient();
		ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
		memset(userData->BLE_Addr,0,sizeof(userData->BLE_Addr));
		sprintf(userData->BLE_Addr,"%s",userDataBLE_Addr);
		Flw_SendMsgUpLocation(LOCATION_UP_TYPE_BLE);
		ws_printf("%s ble location success",__FUNCTION__);
	}else
	{
		ws_printf("%s ble location fail",__FUNCTION__);
		if(Location_func != NULL)
		{
			Location_func();
			Location_func = NULL;
		}
	}
	memset(userDataBLE_Addr,0,sizeof(userDataBLE_Addr));
	userDataBLE_Rssi = 0;
}
void CWatchService_Ble_Scan_Finish_Timer_Over(uint32_t p)
{
	if(Location_Ble_Match_Timer != NULL)							
	{			 								
		uos_timer_stop(Location_Ble_Match_Timer);
		Location_Ble_Match_Timer = NULL;
	}							
	MMI_ModemAdp_Rpc_Req(CWatchService_Ble_Scan_Over_time, 0, 0, 0);
}

void Ble_Scan_Start()
{
		printf("%s: processing....\n", __FUNCTION__);
		uint32_t length = sizeof(nv_watch_bluetooth_t);
		nv_watch_bluetooth_t * nvm = (nv_watch_bluetooth_t *)lv_mem_alloc(length);
		if(length != UI_NV_Read_Req(NV_SECTION_UI_BLUETOOTH, 0, length, (uint8_t *)nvm)) 
		{
			printf("read bluetooth from nvm error in %s\n",__FUNCTION__);
			lv_mem_free(nvm);
			if(Location_func != NULL)
			{
				Location_func();
				Location_func = NULL;
			}
			return NULL;
		}
		
		if(nvm->on_off != 0) 
		{	
			bt_inquiry_device_cancel();
 			ble_jrp_client(true, NULL);	
			if(Location_Ble_Match_Timer==NULL)							
			{			 								
				uos_timer_create(&Location_Ble_Match_Timer);							
			}							
			uos_timer_start(Location_Ble_Match_Timer, TICKES_IN_SECOND*BLE_SCAN_TIME_OUT, 0, CWatchService_Ble_Scan_Finish_Timer_Over, (uint32_t)0);
		}	 
		lv_mem_free(nvm);
}

void CWatchService_Ble_Scan_Start_Timer_Over(uint32_t p)
{	
	if(Location_Ble_Match_Timer != NULL)							
	{			 								
		uos_timer_stop(Location_Ble_Match_Timer);
		Location_Ble_Match_Timer = NULL;
	}							
	MMI_ModemAdp_Rpc_Req(Ble_Scan_Start, 0, 0, 0);
}

void Open_bluetooth_preper_Location(void (*Locationbk_func)(void))
{
	uint32_t length = sizeof(nv_watch_bluetooth_t);
    nv_watch_bluetooth_t * nvm = (nv_watch_bluetooth_t *)lv_mem_alloc(length);
	Location_func = Locationbk_func;
	memset(&location_short_blemac,0,sizeof(nv_watch_location_blemac_t));
	UI_NV_Read_Req(NV_SECTION_LOCATION_BLEMAC_INFO, 0,sizeof(nv_watch_location_blemac_t), (uint8_t*)&location_short_blemac);
	for(uint8_t num=0;num<location_short_blemac.blemac_cnt;num++)
		ke_common_string_high_to_Low(location_short_blemac.ws_blemac[num].location_blemac,location_short_blemac.ws_blemac[num].location_blemac);
	if((((g_scene_mac.ws_scene_type&1<<1)==0)&&((g_scene_mac.ws_scene_type&1<<3)==0))
		&&(location_short_blemac.blemac_cnt == 0))
	{
		ws_printf("scene ble mac is empty %s\n",__FUNCTION__);
        lv_mem_free(nvm);
		if(Location_func != NULL)
		{
			Location_func();
			Location_func = NULL;
		}
        return;
	}
    if(length != UI_NV_Read_Req(NV_SECTION_UI_BLUETOOTH, 0, length, (uint8_t *)nvm)) 
	{
        ws_printf("read bluetooth from nvm error in %s\n",__FUNCTION__);
        lv_mem_free(nvm);
		if(Location_func != NULL)
		{
			Location_func();
			Location_func = NULL;
		}
        return;
    }

	if(nvm->on_off == 0) 
	{
        bt_adp_task_create();
		bt_list_init();
     
		lv_refr_now(NULL);
		int32_t ret = bt_open_device();
        if(ret != 0) 
		{
			bt_adp_task_destroy();
			bt_list_destroy();
			bluetooth_nvm_save_switch_state(false);
			ws_printf("open bluetooth  error  %s\n",__FUNCTION__);
			if(Location_func != NULL)
			{
				Location_func();
				Location_func = NULL;
			}
        }else{
        	bluetooth_nvm_save_switch_state(true);
			ws_printf("open bluetooth  success  %s\n",__FUNCTION__);
			if(Location_Ble_Match_Timer==NULL)							
			{			 								
				uos_timer_create(&Location_Ble_Match_Timer);							
			}		
			uos_timer_stop(Location_Ble_Match_Timer);
			uos_timer_start(Location_Ble_Match_Timer, TICKES_IN_SECOND*4, 0, CWatchService_Ble_Scan_Start_Timer_Over, (uint32_t)0);
        }
	}else if(nvm->on_off != 0)
	{
 		CWatchService_Ble_Scan_Start_Timer_Over(0);
	}
	 lv_mem_free(nvm);
}

void CWathcService_SetBlacklist_BleMac(ws_client * pme, void *json)
{
	ws_cJSON *root_parse = (ws_cJSON *)json;
	ws_cJSON *Blaclist_Mac = NULL,*Task_Id = NULL;
	ws_cJSON *request_s = NULL;
	char *Data = NULL;
	char tmp1[20]={0};
	char taskId[20]={0};
	request_s = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
	if(!request_s)
	{
		return;
	}
	Blaclist_Mac = ws_cJSON_GetObjectItem(request_s, "mac");
	if(Blaclist_Mac==NULL || Blaclist_Mac->valuestring==NULL)
	{
		return;
	}

	if(BLACK_MAC_LEN !=(strlen(Blaclist_Mac->valuestring)))
	{
		ws_printf("%s Blacklist Mac length error",__FUNCTION__);
		return;
	}
	memset(tmp1,0,sizeof(tmp1));
	sprintf(tmp1,"%s",Blaclist_Mac->valuestring);
	ws_printf("%s tmp1 %s  %d",__FUNCTION__,tmp1,strlen(tmp1));

	
	if(Blacklist_Mac_Count == 0)
	{
		Black_mac_list = (char *)malloc(BLACK_MAC_LEN);
		memset(Black_mac_list,0,BLACK_MAC_LEN);
	}
	else
	{
		int length = BLACK_MAC_LEN*(Blacklist_Mac_Count+1);
		Black_mac_list = (char *)realloc(Black_mac_list,length+1);
	}
	strncat(Black_mac_list,tmp1,BLACK_MAC_LEN);
	Blacklist_Mac_Count++;
	Task_Id = ws_cJSON_GetObjectItem(request_s, "taskId");
	if(Task_Id!=NULL && Task_Id->valuestring!=NULL)
	{
		sprintf(taskId,"%s",Task_Id->valuestring);
	}
	CWatchService_Res_Black_Data(taskId);
	ws_printf("kaer_blacklist_mac_st %d  %s",Blacklist_Mac_Count,Black_mac_list);
		

}


void CWatchService_Res_Black_Data(char *taskId)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
  	ws_cJSON *macJs = NULL;
    ws_cJSON  *root = NULL;
  
	userData->type = KAER_CMD_SET_BLACK_MAC;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	
	macJs = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_RSP,macJs);


	ws_cJSON_AddStringToObject(macJs,STRING_RESULT,"1");
	ws_cJSON_AddStringToObject(macJs,"taskId",taskId);
	
	CWatchService_SendJsonData(pme, root);
}

#endif

#if USE_LV_WATCH_UPLOAD_CALLLOG !=0
static void CWatchService_UpCallLog(ws_client * pme, ws_evt_msg_t  *cmd_info, double seq)
{
	if(cmd_info == NULL)
	{
		return;
	}
	ws_call_log *CallLogPtr = &(cmd_info->cmd.CallLog);
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
  	ws_cJSON *locationJs = NULL;
    ws_cJSON  *root = NULL;
	int  len;
    char Data[40] = {0};
	hal_rtc_t rtc_curr;
  
	userData->type = KAER_CMD_UP_CALLLOG;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
   
    locationJs = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_REQ,locationJs);
	sprintf(Data,"%d",CallLogPtr->mDuration);
	ws_cJSON_AddStringToObject(locationJs,"duration",Data);
	#if USE_LV_WATCH_SPAIN == 0
	ws_cJSON_AddStringToObject(locationJs,"money","");
	#endif
	memset(Data,0,sizeof(Data));
	seconds_to_time(CallLogPtr->mStartTime , &rtc_curr);
	sprintf(Data,"%04d-%02d-%02d %02d:%02d:%02d", rtc_curr.tm_year, rtc_curr.tm_mon, rtc_curr.tm_mday,rtc_curr.tm_hour, rtc_curr.tm_min, rtc_curr.tm_sec);
	ws_cJSON_AddStringToObject(locationJs,"time",Data);
	ws_cJSON_AddStringToObject(locationJs,"number",CallLogPtr->mNumber);
	#if USE_LV_WATCH_SPAIN == 0
	ws_cJSON_AddStringToObject(locationJs,"cardId","");
	ws_cJSON_AddStringToObject(locationJs,"iccid","");
	#endif

	if(CallLogPtr->mFlag == 0)
	{
		ws_cJSON_AddStringToObject(locationJs,"direction","1");
	}
	else if((CallLogPtr->mFlag == 2) || (CallLogPtr->mFlag == 1))
	{
		ws_cJSON_AddStringToObject(locationJs,"direction","2");
	}

	CWatchService_SendJsonData(pme, root);
}
#endif

#if USE_LV_WATCH_STUDENT_INFO != 0
void CWathcService_ReceiveStuInfo(ws_client * pme, void *json)
{
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_cJSON *version = NULL;
	ws_cJSON *version2 = NULL;
	ws_cJSON *version3 = NULL;
	ws_cJSON *version4 = NULL;
	ws_cJSON *version5 = NULL;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_STUDENT_INFO;
	req_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
	if(!req_s)
	{
		return;
	}
	memset(&nh_userInfo,0,sizeof(nv_watch_nh_stuinfo_t));
	
	version2 = ws_cJSON_GetObjectItem(req_s, "phone");
	if(version2 != NULL && version2->valuestring != NULL)
    {
    	WS_PRINTF("phone===%s\n",version2->valuestring);
		strcpy(nh_userInfo.phoneNum,version2->valuestring);
    }

	version3 = ws_cJSON_GetObjectItem(req_s, "grade");
	if(version3 != NULL && version3->valuestring != NULL)
    {
    	WS_PRINTF("grade===%s\n",version3->valuestring);
		strcpy(nh_userInfo.grade,version3->valuestring);
    }

	version = ws_cJSON_GetObjectItem(req_s, "name");
	if(version != NULL && version->valuestring != NULL)
    {
		WS_PRINTF("name===%s\n",version->valuestring);
		strcpy(nh_userInfo.name,version->valuestring);
    }

	version5 = ws_cJSON_GetObjectItem(req_s, "school");
	if(version5 != NULL && version5->valuestring != NULL)
    {
		WS_PRINTF("school===%s\n",version5->valuestring);
		strcpy(nh_userInfo.school,version5->valuestring);
    }

	version4 = ws_cJSON_GetObjectItem(req_s, "avatar");
	if(version4 != NULL && version4->valuestring != NULL)
    {
		strcpy(nh_userInfo.avatar,version4->valuestring);
		WS_PRINTF("avatar===%s\n",version4->valuestring);
    }
	
	UI_NV_Write_Req(NV_SECTION_UI_NH_STU_INFO, 0, sizeof(nv_watch_nh_stuinfo_t), (uint8_t *)&nh_userInfo);

	kaer_https_DownloadFile(nh_userInfo.avatar, nh_photo_HttpRsp_png);
}
#endif

#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
void CWathcService_Set_Group_Period(ws_client * pme, void *json)
{
    ws_cJSON     *root = (ws_cJSON *)json;
	const uint8_t wrp[7] = {0,1,2,3,4,5,6};
	uint8_t  w;
    uint16_t          i,cnt = 0;
	ws_cJSON *Group_s = NULL;
	ws_cJSON *week = NULL;
	ws_cJSON *posPeriod = NULL;
	ws_cJSON *begtime = NULL;
	ws_cJSON *endtime = NULL;
	

    ws_cJSON *request_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
    if(!request_s)
    {
    	return;
    }
	clear_group_list();

	ws_cJSON * GroupList= ws_cJSON_GetObjectItem(request_s, "groupList");
    if(!GroupList)
    {
    	return;
    }
	cnt = ws_cJSON_GetArraySize(GroupList);	
	if(cnt >= WS_GROUPLIST_MAX_COUNT)
		cnt = WS_GROUPLIST_MAX_COUNT;
	ke_group_list.count = cnt;

	if(cnt)
	{
		for(i=0;i<cnt;i++)
		{
			Group_s = ws_cJSON_GetArrayItem(GroupList, i);
			week = ws_cJSON_GetObjectItem(Group_s, "week");
	        posPeriod = ws_cJSON_GetObjectItem(Group_s, "posPeriod");
	        begtime = ws_cJSON_GetObjectItem(Group_s, "begtime");
			endtime = ws_cJSON_GetObjectItem(Group_s, "endtime");
			for(int n=0; n<strlen(week->valuestring); n++)
			{
				if(week->valuestring[n] == ',')
					continue;
				w= week->valuestring[n]-'0';
				ke_group_list.ws_groupList[i].repeat += (1<<wrp[w]);
			}		
			ke_group_list.ws_groupList[i].repeat |= 0x80;

			ke_group_list.ws_groupList[i].posPeriod = atoi(posPeriod->valuestring);
			ke_group_list.ws_groupList[i].start = (atoi(begtime->valuestring)/100)*60+(atoi(begtime->valuestring)%100);
			ke_group_list.ws_groupList[i].end = (atoi(endtime->valuestring)/100)*60+(atoi(endtime->valuestring)%100);
			ws_printf("CWathcService_Set_Group_Period[%d]=%d,0x%x,star=%d,end=%d",i,ke_group_list.ws_groupList[i].posPeriod,ke_group_list.ws_groupList[i].repeat,ke_group_list.ws_groupList[i].start,ke_group_list.ws_groupList[i].end);
		}

		UI_NV_Write_Req(NV_SECTION_GROUP_PERIOD, 0,sizeof(nv_watch_groupList_t), (uint8_t*)&ke_group_list);
	}
}

void CWathcService_Check_Group_Period()
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	hal_rtc_t rtc_curr;
    uint8_t week = 0;
	int i, ret = 0;
	int time = 0;

	if(ke_group_list.count == 0)
		return;
	
    Hal_Rtc_Gettime(&rtc_curr);
	if(rtc_curr.tm_year >= 2024)
		;
	else
		return;
    rtc_calc_weekday(&rtc_curr);
	week = rtc_curr.tm_wday;
	
	for(i=0;i<ke_group_list.count;i++)
	{
		 if(0 == (ke_group_list.ws_groupList[i].repeat & (1<<week)))		 	
		 {
		 	if(userData->m_base.ws_reg.upPosTime != ke_upPosTime)
				userData->m_base.ws_reg.upPosTime = ke_upPosTime;
			continue;
		 }
		 else
		 {
		 	time = rtc_curr.tm_hour*60 + rtc_curr.tm_min;
			if(time >= ke_group_list.ws_groupList[i].start && time < ke_group_list.ws_groupList[i].end)
			{
				if(userData->m_base.ws_reg.upPosTime != 60*(ke_group_list.ws_groupList[i].posPeriod))
				{
					userData->m_base.ws_reg.upPosTime = 60*(ke_group_list.ws_groupList[i].posPeriod);
					//userData->mPreSendLocCnt = 0;
				}					
				#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
				if((userData->m_base.ws_reg.upPosTime == 60))
				{
					if(g_high_freq_s.high_freq_open == 0)
					{
						g_high_freq_s.high_freq_open = 1;
						UI_NV_Write_Req(NV_SECTION_HIGH_FREQ_LOC_INFO, 0, sizeof(nv_watch_high_freq_flag_t), (uint8_t *)&g_high_freq_s);
					}
				}
				else if(g_high_freq_s.high_freq_open == 1)
				{
					g_high_freq_s.high_freq_open = 0;
					UI_NV_Write_Req(NV_SECTION_HIGH_FREQ_LOC_INFO, 0, sizeof(nv_watch_high_freq_flag_t), (uint8_t *)&g_high_freq_s);
				}
				#endif
				return;
			}
		 }
	}
	if(userData->m_base.ws_reg.upPosTime != ke_upPosTime)
		userData->m_base.ws_reg.upPosTime = ke_upPosTime;

	#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
	WS_PRINTF("!!!!!!!!!!!!! CWathcService_Check_Group_Period opne=%d",g_high_freq_s.high_freq_open);
	//not cur week,clear
	if(g_high_freq_s.high_freq_open == 1)
	{
		g_high_freq_s.high_freq_open = 0;
		UI_NV_Write_Req(NV_SECTION_HIGH_FREQ_LOC_INFO, 0, sizeof(nv_watch_high_freq_flag_t), (uint8_t *)&g_high_freq_s);
	}
	#endif

}

void clear_group_list()
{
	int i = 0;
	
	ke_group_list.count = 0;
	while(i < WS_GROUPLIST_MAX_COUNT)
	{
		memset(&ke_group_list.ws_groupList[i], 0, sizeof(ws_nv_groupList));
		i++;
	}
}
void CWathcService_Group_Period_Init()
{
	ke_upPosTime = myNVMgr_GetWsDevPosUpTime();//second
	clear_group_list();
	UI_NV_Read_Req(NV_SECTION_GROUP_PERIOD, 0,sizeof(nv_watch_groupList_t), (uint8_t*)&ke_group_list);
}

#if defined(__XF_PRO_DSHANG__)
void CWatchService_Default_GroupPerid(nv_watch_groupList_t *grplist)
{
	grplist->count = 6;
	grplist->ws_groupList[0].repeat = 0xBE;///1~5
	grplist->ws_groupList[0].posPeriod = 1;
	grplist->ws_groupList[0].start  = 6*60+30;
	grplist->ws_groupList[0].end= 8*60;

	grplist->ws_groupList[1].repeat = 0xBE;///1~5
	grplist->ws_groupList[1].posPeriod = 5;
	grplist->ws_groupList[1].start  = 8*60;
	grplist->ws_groupList[1].end= 16*60+30;

	grplist->ws_groupList[2].repeat = 0xBE;///1~5
	grplist->ws_groupList[2].posPeriod = 1;
	grplist->ws_groupList[2].start  =16*60+30;
	grplist->ws_groupList[2].end= 19*60+30;

	grplist->ws_groupList[3].repeat = 0xBE;///1~5
	grplist->ws_groupList[3].posPeriod = 5;
	grplist->ws_groupList[3].start  =19*60+30;
	grplist->ws_groupList[3].end= 23*60;

	grplist->ws_groupList[4].repeat = 0xBE;///1~5
	grplist->ws_groupList[4].posPeriod = 5;
	grplist->ws_groupList[4].start  =5*60;
	grplist->ws_groupList[4].end= 6*60+30;

	grplist->ws_groupList[5].repeat = 0xC1;///6~7
	grplist->ws_groupList[5].posPeriod = 3;
	grplist->ws_groupList[5].start  =5*60;
	grplist->ws_groupList[5].end= 23*60;

}
#endif
#endif

#if USE_LV_WATCH_MIGU != 0
void CWathcService_ReceiveMGurlInfo(ws_client * pme, void *json)
{
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_cJSON *version2 = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_MIGU_URL;
	req_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
	if(!req_s)
	{
		return;
	}
	version2 = ws_cJSON_GetObjectItem(req_s, "resourceAddr");
	if(version2 != NULL && version2->valuestring != NULL)
	{
		set_url_update_status(1);
		save_mgu_play_url(version2->valuestring,strlen(version2->valuestring));
		mgu_audio_hint();
	}
}

void CWathcService_SetMGPlan(ws_client * pme, void *json)
{
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_MIGU_PLAN;
	req_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	CWatchService_PlanList_Parse(req_s);
}

#endif

#if USE_LV_WATCH_KAER_SETTINGS != 0
static void CWathcService_ReceiveKeConfig(ws_client * pme, void *json)
{
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_CONFIG;
	req_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	ws_cJSON *type_s = ws_cJSON_GetObjectItem(req_s,"type");
	if(NULL!=type_s && NULL!=type_s->valuestring)
	{
		if(atoi(type_s->valuestring) == 2)
		{
			ws_cJSON *info_s = ws_cJSON_GetObjectItem(req_s,"info");
			if(NULL!=info_s && NULL!=info_s->valuestring)
			{
				ke_tcs_set_config(info_s->valuestring);
			}
		}
	}

}
#if USE_LV_WATCH_KEY_KW19 != 0
void CWatchService_UpHeartRate_Data(int hr,int rid,char *begin_time,char *end_time)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
  	ws_cJSON *locationJs = NULL;
    ws_cJSON  *root = NULL;
	int  len;
    char Data[40] = {0};
	hal_rtc_t rtc_curr;
  
	userData->type = KAER_CMD_UP_HR_DATA;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	ws_cJSON_AddStringToObject(root,STRING_TYPE,STRING_TERMINAL);
   
    locationJs = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_REQ,locationJs);
	ws_cJSON_AddNumberToObject(locationJs,"heartRate",hr);

	hal_rtc_t rtc_curr_update;
    Hal_Rtc_Gettime(&rtc_curr_update);
	char time[32]={0};
	sprintf(time,"%04d%02d%02d%02d%02d%02d",rtc_curr_update.tm_year,rtc_curr_update.tm_mon,rtc_curr_update.tm_mday,rtc_curr_update.tm_hour,rtc_curr_update.tm_min,rtc_curr_update.tm_sec);
	ws_cJSON_AddStringToObject(locationJs,"time",time);
	#if USE_LV_WATCH_SPAIN == 0
	if(rid != 0)
		ws_cJSON_AddNumberToObject(locationJs,"rid",rid);
	ws_cJSON_AddStringToObject(locationJs,"angular","1");
	ws_cJSON_AddNumberToObject(locationJs,"step",mmi_get_steps_count());
	if((strlen(begin_time)>0)&&(strlen(end_time)>0))
	{
		ws_cJSON_AddStringToObject(locationJs,"sleepBeginTime",begin_time);
		ws_cJSON_AddStringToObject(locationJs,"sleepEndTime",end_time);
	}
	#endif
	CWatchService_SendJsonData(pme, root);//发送心率
}
void CWatchService_HealthRate_Data(int data, int type)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
  	ws_cJSON *locationJs = NULL;
    ws_cJSON  *root = NULL;
	int  len;
    char Data[40] = {0};
	hal_rtc_t rtc_curr;
  
	userData->type = KAER_CMD_UP_HEALTH_DATA;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	ws_cJSON_AddStringToObject(root,STRING_TYPE,STRING_TERMINAL);
   
    locationJs = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_REQ,locationJs);

	hal_rtc_t rtc_curr_update;
    Hal_Rtc_Gettime(&rtc_curr_update);
	char time[32]={0};
	sprintf(time,"%04d%02d%02d%02d%02d%02d",rtc_curr_update.tm_year,rtc_curr_update.tm_mon,rtc_curr_update.tm_mday,rtc_curr_update.tm_hour,rtc_curr_update.tm_min,rtc_curr_update.tm_sec);
	ws_cJSON_AddStringToObject(locationJs,"time",time);
	ws_cJSON_AddNumberToObject(locationJs,"value",data);
	ws_cJSON_AddNumberToObject(locationJs,"type",type);
	CWatchService_SendJsonData(pme, root);
}
#endif

#if USE_LV_WATCH_SMART_JUMPING_KR != 0 || USE_LV_WATCH_RUNNING_SPORT_KR != 0
char jump_plan_id[40]={0};
char run_plan_id[40]={0};
typedef enum{
	JUMP_SPORT = 1,
	RUN_SPORT = 2,  
}SPORT_PLAN_TYPE;
typedef enum{
	JUMPTYPE_TIME = 0,	 
	JUMPTYPE_COUNT = 1, 
	JUMPTYPE_FREE = 2, 
	JUMPTYPE_MAX,
}TYPE_JUMP;

extern void jump_notice_create_task(lv_task_t * task);

void CWathcService_SetSportPlan(ws_client * pme, void *json)
{
	ws_cJSON *request_s = NULL;
	ws_cJSON *device_mac = NULL;  
	ws_cJSON *task_time = NULL; 
	ws_cJSON *task_count = NULL; 
	ws_cJSON *task_type = NULL; 
	ws_cJSON *jump_state = NULL; 
	ws_cJSON *task_ID = NULL;
	int jumptype=0,jumptime=0,jumpcount=0,jumpstate=2,taskid=0,plan_type = 0;
	char mac[30];
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_SPORT_PLAN;
	req_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	task_type = ws_cJSON_GetObjectItem(req_s,"st");
	if(task_type !=NULL )
	{
		plan_type = task_type->valueint;
	}
		
	task_time = ws_cJSON_GetObjectItem(req_s,"sc");
	if(task_time !=NULL )
	{
		jumptime = task_time->valueint;
	}

	jump_state = ws_cJSON_GetObjectItem(req_s,"sf");
	if(jump_state !=NULL )
	{
		jumpstate = jump_state->valueint;
	}
	if(plan_type == JUMP_SPORT)
	{
		task_ID = ws_cJSON_GetObjectItem(req_s,"hwid");
		if(task_ID !=NULL && task_ID->valuestring != NULL)
		{
			if(strlen(task_ID->valuestring) < 40)
			{
				memset(jump_plan_id,0,sizeof(jump_plan_id));
				strcpy(jump_plan_id, task_ID->valuestring);
			}
		}
	}else if(plan_type == RUN_SPORT)
	{
		task_ID = ws_cJSON_GetObjectItem(req_s,"hwid");
		if(task_ID !=NULL && task_ID->valuestring != NULL)
		{
			if(strlen(task_ID->valuestring) < 40)
			{
				memset(run_plan_id,0,sizeof(run_plan_id));
				strcpy(run_plan_id, task_ID->valuestring);
			}
		}
	}
	task_count = ws_cJSON_GetObjectItem(req_s,"sn");
	if(task_count !=NULL )
	{
		jumpcount = task_count->valueint;
	}
	ws_printf("%s jumptype=%d,jumptime=%d,jumpcount=%d",__FUNCTION__,jumptype,jumptime,jumpcount);

	if(jumptime != 0)
		jumptype = JUMPTYPE_TIME;
	
    set_task_info(jumptype, jumpcount, jumptime,0);
	if(plan_type == JUMP_SPORT)
	{
		Wakeup_GuiTask(true);
		watch_wakeup_time_reset();
		lv_task_t * task = lv_task_create(jump_notice_create_task, 50, LV_TASK_PRIO_HIGHEST, NULL);
		lv_task_once(task);
	}else if(plan_type == RUN_SPORT)
	{
		#if USE_LV_WATCH_RUNNING_SPORT_KR != 0
		lv_task_t * task_once = lv_task_create(running_sport_create_once, 10, LV_TASK_PRIO_HIGHEST, NULL);
		lv_task_once(task_once);
		#endif
	
	}
}
void CWathcService_SetSportMAC(ws_client * pme, void *json)
{
	ws_cJSON *request_s = NULL;
	ws_cJSON *device_mac = NULL;  
	char mac[30];
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_SPORT_PLAN;
	req_s = ws_cJSON_GetObjectItem(root, STRING_REQ);
	if(!req_s)
	{
		return;
	}

	device_mac = ws_cJSON_GetObjectItem(req_s,"mac");
	if(device_mac !=NULL && device_mac->valuestring!=NULL)
	{
		if(strlen(device_mac->valuestring) < 20)
		{
			memset(mac,0,sizeof(mac));
			strcpy(mac, device_mac->valuestring);
			set_jump_bt_addr(mac);
		}
	}
}

void CWatchService_UpJump_Data(uint32_t num,uint32_t begin_time,uint32_t end_time)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_cJSON *data = NULL; 
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
  	ws_cJSON *locationJs = NULL;
    ws_cJSON  *root = NULL;
	int  len;
    char Data[40] = {0};
	hal_rtc_t rtc_curr;
  	char temp[32] = {0};
	uint32_t now_time; 
	userData->type = KAER_CMD_UP_JUMP_DATA;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
   
    data = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_REQ,data);



	ws_cJSON_AddNumberToObject(data,"sn",num);

	if(begin_time != 0)
	{
		memset(temp,0,sizeof(temp));
		sprintf(temp,"%d000",begin_time);
		ws_cJSON_AddStringToObject(data,"bt",temp);
	}
	if(end_time != 0)
	{
		memset(temp,0,sizeof(temp));
		sprintf(temp,"%d000",end_time);
		ws_cJSON_AddStringToObject(data,"ct",temp);
	}else
	{
		now_time = PMIC_RTC_GetTime_Count(0)-28800;
		memset(temp,0,sizeof(temp));
		sprintf(temp,"%d000",now_time);
		ws_cJSON_AddStringToObject(data,"ct",temp);
	}
	if(strlen(jump_plan_id)>0)
	{
		ws_cJSON_AddStringToObject(data,"hwid",jump_plan_id);
		memset(jump_plan_id,0,sizeof(jump_plan_id));
	}else
	{
		sprintf(jump_plan_id,"%s","0146014521ab");
		sprintf(&jump_plan_id[12],"%d",begin_time);
		sprintf(&jump_plan_id[22],"%d",end_time);
		ws_cJSON_AddStringToObject(data,"hwid",jump_plan_id);
		memset(jump_plan_id,0,sizeof(jump_plan_id));
	}
	CWatchService_SendJsonData(pme, root);
}

#endif

#if USE_LV_WATCH_RUNNING_SPORT_KR != 0
typedef struct _ws_run_gps
{	
    char     latf[50];
    char     lonf[50];
    char     time[50];
}ws_run_gps;

typedef struct _ws_run_gps_list
{	
    uint32_t     count;
	ws_run_gps run_gps[50];

}ws_run_gps_list;

typedef struct _ws_start_stop_run_gps
{	
    float     latf;
    float     lonf;
    char     time[50];
	uint32_t stip;
	uint32_t sec;
}ws_start_stop_run_gps;

ws_start_stop_run_gps run_start_stop_list;
ws_run_gps_list run_gps_list;
static void *up_stop_run_repeat_Timer = NULL;
int run_timer_stop_flag = 0;
int run_has_start = 0;

void CWathcService_Start_Stop_Repeat();

bool CWathcService_Start_Stop_Repeat_Send(int flag ,int status)
{
	ws_printf("CWathcService_Start_Stop_Repeat_Send");
	ws_cJSON *root = NULL;
	ws_cJSON *req_s = NULL;
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};
	hal_rtc_t rtc_curr;   

	userData->type = KAER_CMD_UP_RUN_STATUS;
	root = ws_cJSON_CreateObject();
	Hal_Rtc_Gettime(&rtc_curr);
	if(NULL == root)
	{
		 return;
	}
	 
	req_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_REQ,req_s);

	if(flag ==0)
	{
		if(run_timer_stop_flag >= 8)
		{
			run_timer_stop_flag = 0;
			uos_timer_stop(up_stop_run_repeat_Timer);
		}
		else
		{
			if(NULL == up_stop_run_repeat_Timer) 						
			{											
				uos_timer_create(&up_stop_run_repeat_Timer); 						
			}	
			uos_timer_stop(up_stop_run_repeat_Timer);							
			uos_timer_start(up_stop_run_repeat_Timer, TICKES_IN_SECOND*15,0, CWathcService_Start_Stop_Repeat, 0);
		}
		if(MMI_ModemAdp_WS_Is_Online())
		{
			run_timer_stop_flag++;
		}
		else
		{
			return;
		}
	}

	ws_cJSON_AddNumberToObject(req_s,"status",status);
	ws_cJSON_AddNumberToObject(req_s,"steps",run_start_stop_list.stip);
	memset(valueStr,0,sizeof(valueStr));
	sprintf(valueStr, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);
	ws_cJSON_AddStringToObject(req_s,"time",run_start_stop_list.time);
	ws_cJSON_AddStringToObject(req_s,"hwid",run_plan_id);

	if(userData->m_base.is_connected == TRUE)
	{
		CWatchService_SendJsonData(pme, root);
	}	
}
void CWathcService_Start_Stop_Repeat()
{
	CWathcService_Start_Stop_Repeat_Send(0,2);
}
bool CWathcService_Start_Stop_RUN(uint8_t on_off,uint32_t stip)
{
	ws_cJSON *root = NULL;
	ws_cJSON *req_s = NULL;
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};
	hal_rtc_t rtc_curr;   
	userData->type = KAER_CMD_UP_RUN_STATUS;
	root = ws_cJSON_CreateObject();
	Hal_Rtc_Gettime(&rtc_curr);
    if(NULL == root)
    {
        return;
    }

    req_s = ws_cJSON_CreateObject();
    ws_cJSON_AddItemToObject(root,STRING_REQ,req_s);

	if( on_off == 1 )
		run_has_start = 0;
	
	ws_cJSON_AddNumberToObject(req_s,"status",on_off);
	ws_cJSON_AddNumberToObject(req_s,"steps",stip);
	memset(valueStr,0,sizeof(valueStr));
	sprintf(valueStr, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);
	ws_cJSON_AddStringToObject(req_s,"time",valueStr);

	if(strlen(run_plan_id)>0)
	{
		ws_cJSON_AddStringToObject(req_s,"hwid",run_plan_id);
	}else if((on_off == 1) && (strlen(run_plan_id)==0))
	{
		sprintf(run_plan_id,"%s","015F014521ab");
		sprintf(&run_plan_id[12],"%d",PMIC_RTC_GetTime_Count(0));
		sprintf(&run_plan_id[22],"%d",PMIC_RTC_GetTime_Count(0));
		ws_cJSON_AddStringToObject(req_s,"hwid",run_plan_id);
	}
	if(on_off == 3)
	{
		memset(run_plan_id,0,sizeof(run_plan_id));
		memset(&run_gps_list,0,sizeof(ws_run_gps_list));
		run_gps_list.count=0;
	}
	
	if(userData->m_base.is_connected == TRUE)
	{
		ws_printf("(userData->m_base.is_connected == TRUE)");
		CWatchService_SendJsonData(pme, root);
	}
	else
	{
		memset(&run_start_stop_list,0,sizeof(ws_start_stop_run_gps));
		strcpy(run_start_stop_list.time,valueStr);
		
		if(on_off == 3)
		{		
			if(NULL == up_stop_run_repeat_Timer)							
			{											
				uos_timer_create(&up_stop_run_repeat_Timer); 						
			}	
			uos_timer_stop(up_stop_run_repeat_Timer);							
			uos_timer_start(up_stop_run_repeat_Timer, TICKES_IN_SECOND*15,0, CWathcService_Start_Stop_Repeat, 0);
			ws_printf("uos_timer_start(up_stop_run_repeat_Timer, TICKES_IN_SECOND*30,0, CWathcService_Start_Stop_Repeat, 0);");
		}
	}
}
//state = 0 :normal ; 1:suspend ; 2:continue

bool CWathcService_Send_Running(uint32_t stip,uint32_t state,uint32_t sec)
{
	ws_cJSON *root = NULL;
	ws_cJSON *req_s = NULL;
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	char valueStr[30]={0};

	hal_rtc_t rtc_curr;   
	float lonf=0.00;
	float latf=0.00;
	int num=0;
	userData->type = KAER_CMD_UP_RUN_DATA;
	root = ws_cJSON_CreateObject();
	Hal_Rtc_Gettime(&rtc_curr);

    if(NULL == root)
    {
        return;
    }

#if USE_CRANE_WATCH_GPS != 0
	GPS_getPostionInfo(&lonf, &latf);	
#endif
	memset(valueStr,0,sizeof(valueStr));
	sprintf(run_gps_list.run_gps[run_gps_list.count].latf,"%.6f", latf);
	ws_printf("run_gps_list.run_gps[run_gps_list.count].latf=%d,%s",run_gps_list.count,run_gps_list.run_gps[run_gps_list.count].latf); 	
	
	memset(valueStr,0,sizeof(valueStr));
	sprintf(run_gps_list.run_gps[run_gps_list.count].lonf,"%.6f", lonf);
	ws_printf("run_gps_list.run_gps[run_gps_list.count].lonf=%d,%s",run_gps_list.count,run_gps_list.run_gps[run_gps_list.count].lonf); 	
	
	memset(valueStr,0,sizeof(valueStr));
	sprintf(run_gps_list.run_gps[run_gps_list.count].time, "%04d%02d%02d%02d%02d%02d", rtc_curr.tm_year,rtc_curr.tm_mon,rtc_curr.tm_mday,rtc_curr.tm_hour,rtc_curr.tm_min, rtc_curr.tm_sec);

	run_gps_list.count++;
	if(run_gps_list.count>=12)
	{
		req_s = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req_s);
		
		//ws_cJSON_AddNumberToObject(req_s,"heartRate",0);
		//ws_cJSON_AddNumberToObject(req_s,"oximetry",0);
		ws_cJSON_AddNumberToObject(req_s,"oximetry",0);
		ws_cJSON_AddNumberToObject(req_s,"heartRate",0);
		ws_cJSON_AddNumberToObject(req_s,"steps",stip);
		ws_cJSON_AddNumberToObject(req_s,"sportTime",sec);

		ws_cJSON_AddStringToObject(req_s,"hwid",run_plan_id);


		ws_cJSON *list_array = ws_cJSON_CreateArray();
		ws_cJSON_AddItemToObject(req_s, "list", list_array);

		for(num=0;num<12;num++ )
		{	
			ws_cJSON *arrayitem = ws_cJSON_CreateObject();;

			ws_cJSON_AddStringToObject(arrayitem,"lat",run_gps_list.run_gps[num].latf);
			ws_printf("latlatlatlatlatlatlatlatlat=%d,%s",num,run_gps_list.run_gps[num].latf);		
			ws_cJSON_AddStringToObject(arrayitem,"lon",run_gps_list.run_gps[num].lonf);
			ws_printf("lonlonlonlonlonlonlonlon=%d,%s",num,run_gps_list.run_gps[num].lonf);		
			ws_cJSON_AddStringToObject(arrayitem,"time",run_gps_list.run_gps[num].time);
			ws_cJSON_AddItemToArray(list_array,arrayitem);
		}
	    if(userData->m_base.is_connected == TRUE)
		{
			CWatchService_SendJsonData(pme, root);
		}
		memset(&run_gps_list,0,sizeof(ws_run_gps_list));
		run_gps_list.count=0;	
	}
}
extern int running_status;

void CWathcService_ReceiveRunningData(ws_client * pme, void *json)
{
	ws_cJSON *root = (ws_cJSON *)json;
	ws_cJSON *req_s = NULL;
	ws_cJSON *code_s = NULL;
	ws_cJSON *msg_s = NULL;
	ws_cJSON *runSpeed = NULL;
	ws_cJSON *sportValue = NULL;
	ws_cJSON *calorie = NULL;
	ws_cJSON *time_s = NULL;	
	ws_cJSON *step_s = NULL;
	ws_cJSON *runStepSpeed = NULL;
	ws_cJSON *method_s = NULL;
	ws_printf("CWathcService_ReceiveRunningData ");
	app_adaptor_pedometer_record_t running_data={0};

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_UP_RUN_DATA;

	req_s = ws_cJSON_GetObjectItem(root, STRING_RSP);
	if(!req_s)
	{
		return;
	}	
	memset(&running_data,0,sizeof(app_adaptor_pedometer_record_t));	
	runSpeed = ws_cJSON_GetObjectItem(req_s, "runSpeed");
	if(runSpeed != NULL && runSpeed->valuestring != NULL)
    {
    	WS_PRINTF("runSpeed===%s\n",runSpeed->valuestring);
		strcpy(running_data.runspeed,runSpeed->valuestring);
    }

	sportValue = ws_cJSON_GetObjectItem(req_s, "sportValue");
	if(sportValue != NULL)
    {
		WS_PRINTF("sportValue===%f\n",sportValue->valuedouble);
		running_data.km = sportValue->valuedouble;
    }
	running_data.kcal = running_data.km*55*1.036;
	WS_PRINTF("calorie===%d\n",calorie->valueint);


	
	if( runSpeed==NULL && sportValue==NULL && calorie==NULL )
		return;
	if( running_status == 1)
		running_sport_set_running_sport_record(&running_data);
	else if(running_status == 0)
	{
		running_sport_set_running_sport_record(&running_data);		
		if(time_s != NULL)
			running_sport_set_running_sport_time(time_s->valueint);			
		if(step_s != NULL)
			running_sport_set_running_sport_step(step_s->valueint);
			//running_sport_set_running_sport_step(200);
	}
	else
	{
		running_sport_set_running_over_record(&running_data);
	}
}

#endif

static void CWathcService_KeConfig_Rsp(ws_client * pme)
{
    ws_cJSON  *root = NULL;    
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_cJSON *response_s = NULL;
	
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }
	userData->type = KAER_CMD_SET_CONFIG;
	
	response_s = ws_cJSON_CreateObject();
	ws_cJSON_AddItemToObject(root,STRING_RSP,response_s);

	ws_cJSON_AddStringToObject(response_s, "type", "2");
	ws_cJSON_AddStringToObject(response_s, "Result", "1");
	ws_cJSON_AddStringToObject(response_s, "pkId", "0");
	ws_cJSON_AddStringToObject(response_s, "pkCount", "1");

    CWatchService_SendJsonData(pme, root);
}
#endif


#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
void CWatchService_Send_PeriodLocalDat(ws_client *pme)
{
    hal_rtc_t rtc_curr;
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_cJSON  *root = NULL;
	

    if(pme==NULL)
    {
        return;
    }

    userData->type = KAER_CMD_IND_POS;
    root = ws_cJSON_CreateObject();
    if(NULL == root)
    {
        return;
    }

	ws_cJSON *LocationJs = ws_cJSON_CreateObject();

	ws_cJSON_AddItemToObject(root,STRING_REQ,LocationJs);
	
	PositionToJson_upload(userData,LocationJs);

	uint16_t percent=0;
	char BatteryStatus = 3;
	percent = Hal_Battery_Get_Status();
	ws_cJSON_AddNumberToObject(LocationJs,"step",mmi_get_steps_count());	
	ws_cJSON_AddNumberToObject(LocationJs,"battery",percent);	
	if(Hal_Charger_Get_Status() == HAL_CHG_CONNECTED)
	{
		BatteryStatus = 2;
	
	}
	else if(Hal_Charger_Get_Status() == HAL_CHG_DISCONNECTED)
	{
		BatteryStatus = 3;
	}
	ws_cJSON_AddNumberToObject(LocationJs,"batteryStatus",BatteryStatus);	
	
    CWatchService_SendJsonData(pme, root);

}
#endif

#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
void CWatchService_Highfreq_param_init()
{
	memset(g_r_pos_data,0,sizeof(g_r_pos_data));
	memset(&g_high_freq_s,0,sizeof(g_high_freq_s));
	UI_NV_Read_Req(NV_SECTION_HIGH_FREQ_LOC_INFO, 0, sizeof(nv_watch_high_freq_flag_t), (uint8_t *)&g_high_freq_s);
	WS_PRINTF("---- CWatchService_Highfreq_param_init flag = %d ",g_high_freq_s.high_freq_open );
}

uint8_t CWatchService_Is_Highfreq_Loc()
{
	WS_PRINTF("~~~~~~~~~~~ CWatchService_Is_Highfreq_Loc is=%d",g_high_freq_s.high_freq_open);
	return g_high_freq_s.high_freq_open;
}
#endif
#if USE_LV_WATCH_SPAIN != 0
void SPO2_Report_Timer_Task(void *data)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	while (1)
	{
		ws_printf("$$$$$SPO2_UP_TIMER is %d\n",userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN]);
		if ( get_eco_enable()==false && MMI_ModemAdp_WS_Is_Online()==true )
		{
			//判断当前是否正在测量血氧
			if(blood_measure_flag == 0)
			{
				ws_printf("cccccccccc  Blood oxygen measurement is not running!!!!!!!!!!!!!!!!!!!!!!!\n");
				if(blood_measure_task_handle!= NULL)
				{
					uos_delete_task(blood_measure_task_handle);
					blood_measure_task_handle = NULL;

				}
				blood_measure_task_handle = uos_create_task(
					blood_oxygen_measure_thread,
					NULL,
					16,          
					1024 * 8,  
					212,         
					"blood_measure_task"
				);
			}
			else
			{
				ws_printf("cccccccccc  Blood oxygen measurement is running!!!!!!!!!!!!!!!!!!!!!!!\n");	
			}
		}
		else
		{
			ws_printf("cccccccccc Blood oxygen  ECO mode or disconnect  !!!!!!!!!!!!!!!!!!!!!!!\n");
		}
		//延时进入下一周期，采用分段式睡眠确保准确性
		uint32_t Loop_count = (userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN])/(10*60); // 10分钟
		uint32_t remaining_time = (userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN])%(10*60);
		ws_printf("cccccccccc Blood oxygen   Loop_count=%d,remaining_time=%d\n",Loop_count,remaining_time);
		for (uint32_t i = 0; i < Loop_count; i++) {
			uos_sleep(10*60*200); // 10分钟
		}
		uos_sleep(remaining_time*200); // 剩余时间
	}	
}

//创建周期血氧上报定时线程
void Start_SPO2_Report_Timer_Task()
{
	if(SPO2_report_timer_thread == NULL)
	{
		ws_printf("Blood oxygen timer task is not running!!!!!!!!!!!!!!!!!!!!!!!\n");
		SPO2_report_timer_thread = uos_create_task(
			SPO2_Report_Timer_Task,
			NULL,
			16,          
			1024 * 4,   
			212,         
			"blood_measure_timer_task"
		);
	}
}

//销毁周期血氧上报定时线程
void Stop_SPO2_Report_Timer_Task()
{
	if(blood_measure_task_handle!= NULL)
	{
		ws_printf("111111  delete SPO2 timer task!!!!!!!!!!!!!!!!!!!!!!!\n");
		uos_suspend_task(blood_measure_task_handle);
		uos_delete_task(blood_measure_task_handle);
		blood_measure_task_handle = NULL;
		
		HR_drv_exit(HEALTH_MOTION_HR_APP);
		HR_drv_exit(HEALTH_MOTION_BO_APP);
		set_heart_rate_state(HEART_DORMANT);

		blood_measure_flag = 0; // 重置标志
		gsensor_step_resume();
	}

	if(SPO2_report_timer_thread != NULL)
	{
		ws_printf("**********  delete SPO2 timer task!!!!!!!!!!!!!!!!!!!!!!!\n");
		uos_suspend_task(SPO2_report_timer_thread);
		uos_delete_task(SPO2_report_timer_thread);
		SPO2_report_timer_thread = NULL;
	}
	gsensor_step_resume();
}

void HR_Report_Timer_Task(void *data)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	while (1)
	{
		ws_printf("$$$$$   HR_UP_TIMER is %d\n",userData->m_base.health_freq[WS_HD_HEAT_RATE]);

		if (get_eco_enable()==false && MMI_ModemAdp_WS_Is_Online()==true)
		{
			//判断当前是否正在测量心率
			if(heart_measure_flag == 0)
			{
				ws_printf("TTTTTTTT   Heart measure task is not running!!!!!!!!!!!!!!!!!!!!!!!\n");
				if(heart_measure_task_handle != NULL)
				{
					uos_delete_task(heart_measure_task_handle);
					heart_measure_task_handle = NULL;
					
				}
				heart_measure_task_handle = uos_create_task(
					heart_measure_thread,
					NULL,
					16,
					1024 * 8,
					211,
					"heart_measure_task"
				);
			}
			else
			{
				ws_printf("TTTTTTTTTTTTT   Heart measure task already running!!!!!!!!!!!!!!!!!!!!!!!\n");
			}
		}
		else
		{
			ws_printf("TTTTTTTTTTTTT  Heart measure ECO mode or disconnect !!!!!!!!!!!!!!!!!!!!!!!\n");
		}
		//延时进入下一周期，采用分段式睡眠确保准确性
		uint32_t Loop_count = (userData->m_base.health_freq[WS_HD_HEAT_RATE])/(10*60); // 10分钟
		uint32_t remaining_time = (userData->m_base.health_freq[WS_HD_HEAT_RATE])%(10*60);
		ws_printf("TTTTTTTTTTTTT  Heart measure Loop_count = %d,remaining_time = %d\n",Loop_count,remaining_time);
		for (uint32_t i = 0; i < Loop_count; i++) {
			uos_sleep(10*60*200); // 10分钟
		}
		uos_sleep(remaining_time*200); // 剩余时间
	}	
}

//创建周期心率上报定时线程
void Start_HR_Report_Timer_Task()
{
	if(HR_report_timer_thread == NULL)
	{
		ws_printf("heart rate timer task is not running!!!!!!!!!!!!!!!!!!!!!!!\n");
		HR_report_timer_thread = uos_create_task(
			HR_Report_Timer_Task,
			NULL,
			16,          
			1024 * 4,   
			211,         
			"heart_rate_measure_timer_task"
		);
	}
}

//销毁周期心率上报定时线程
void Stop_HR_Report_Timer_Task()
{
	if(heart_measure_task_handle!= NULL)
	{
		ws_printf("111111  delete heart rate measure task!!!!!!!!!!!!!!!!!!!!!!!\n");
		uos_suspend_task(heart_measure_task_handle);
		uos_delete_task(heart_measure_task_handle);
		heart_measure_task_handle = NULL;
			
		HR_drv_exit(HEALTH_MOTION_HR_APP);
		set_heart_rate_state(HEART_DORMANT);

		heart_measure_flag = 0; // 重置标志
		gsensor_step_resume();
	}

	if(HR_report_timer_thread != NULL)
	{
		ws_printf("2222222 delete heart rate timer task!!!!!!!!!!!!!!!!!!!!!!!\n");
		uos_suspend_task(HR_report_timer_thread);
		uos_delete_task(HR_report_timer_thread);
		HR_report_timer_thread = NULL;
	}
	gsensor_step_resume();
}


int get_ECO_connect()
{
	return ECO_connect;
}
void set_ECO_connect(int data)
{
	ECO_connect=data;
}
void CWatchService_ECO_mode(int data){
	if (data==1){
		set_eco_enable(true);
		set_ECO_connect(0);
		status_update_location_state(false);
		if (ECO_mode_strat_Timer_thread_HANDLE != NULL) 
		{
			uos_suspend_task(ECO_mode_strat_Timer_thread_HANDLE);
			uos_delete_task(ECO_mode_strat_Timer_thread_HANDLE);
			ECO_mode_strat_Timer_thread_HANDLE = NULL;
		}
		ECO_mode_strat_Timer_thread_HANDLE = uos_create_task(
        ECO_mode_strat_Timer_thread,
        NULL,
        16,
        1024 * 5,
        211,
        "ECO_mode_strat_Timer_thread"
    	);
	}
	else{
		set_eco_enable(false);
		set_ECO_connect(1);
		if(MMI_ModemAdp_WS_GetClient() != NULL)
		{
			MMI_ModemAdp_WS_ServiceSwitch(true);	
		}
		printf("$$$$$$ end ECO mode\n");
		if (ECO_mode_strat_Timer_thread_HANDLE != NULL) 
		{
			printf("$$$$$$ECO_mode_strat_Timer_thread_HANDLE set null....");
			uos_suspend_task(ECO_mode_strat_Timer_thread_HANDLE);
			uos_delete_task(ECO_mode_strat_Timer_thread_HANDLE);
			ECO_mode_strat_Timer_thread_HANDLE = NULL;
		}
		
	}
}
void ECO_mode_strat_Timer_thread(void *data)
{
	static int delay_time = 0;  //分钟
	static int keep_connect_time = 10;  //分钟
	while(1){
		ws_printf("$$$$$$ strat timer,after %d start connect plat\n",get_eco_period());
		set_ECO_connect(0);
		status_update_location_state(false);
		MMI_ModemAdp_WS_ServiceSwitch(false);
		uos_sleep((get_eco_period()-delay_time)*60*200);

		ws_printf("$$$$$$$$$ ECO timer end,start connect plat\n");
		set_ECO_connect(1);
		if(MMI_ModemAdp_WS_GetClient() != NULL)
		{
			MMI_ModemAdp_WS_ServiceSwitch(true);
		}
		uos_sleep(keep_connect_time*60*200);   //eco模式下，定期连接数据时间
		delay_time = keep_connect_time;	//下个周期需要提前开始的时间，确保周期准确
}
}


static int convert_to_internal_timezone(int standard_tz) {
    return standard_tz * TIMEZONE_SCALE;
}

static int convert_to_standard_timezone(int internal_tz) {
    return internal_tz / TIMEZONE_SCALE;
}

void set_timezone(int timezone)
{
    enableAutoTimeZone(FALSE);
    enableAutoTime(FALSE);
    
    uint32_t utc_seconds = PMIC_RTC_GetTime_Count(0);
    hal_rtc_t utc_time;
    seconds_to_time(utc_seconds, &utc_time);
    
    int current_tz = PMIC_RTC_GetTimezone() / TIMEZONE_SCALE;
    
    ws_printf("Current UTC time (actually UTC+%d): %04d-%02d-%02d %02d:%02d:%02d", 
        current_tz, utc_time.tm_year, utc_time.tm_mon, utc_time.tm_mday, 
        utc_time.tm_hour, utc_time.tm_min, utc_time.tm_sec);
    
    // 设置新时区值 (内部使用分钟表示)
    int internal_tz = timezone * TIMEZONE_SCALE;
    PMIC_RTC_SetTimezone(internal_tz);
    
    // 获取系统计算的本地时间
    uint32_t original_local_seconds = PMIC_RTC_GetTime_Count(1);
    hal_rtc_t original_local_time;
    seconds_to_time(original_local_seconds, &original_local_time);
    
    ws_printf("Original local time: %04d-%02d-%02d %02d:%02d:%02d", 
        original_local_time.tm_year, original_local_time.tm_mon, original_local_time.tm_mday, 
        original_local_time.tm_hour, original_local_time.tm_min, original_local_time.tm_sec);
    
    uint32_t true_utc_seconds = utc_seconds - (current_tz * 3600);
    
    // 计算新时区下的正确本地时间
    uint32_t correct_local_seconds = true_utc_seconds + (timezone * 3600);
    hal_rtc_t correct_local_time;
    seconds_to_time(correct_local_seconds, &correct_local_time);
    
    ws_printf("Timezone changed from %d to %d", current_tz, timezone);
    ws_printf("Calculated correct local time: %04d-%02d-%02d %02d:%02d:%02d", 
        correct_local_time.tm_year, correct_local_time.tm_mon, correct_local_time.tm_mday, 
        correct_local_time.tm_hour, correct_local_time.tm_min, correct_local_time.tm_sec);
    
    Hal_Rtc_Settime(&correct_local_time);
    
    return;
}

int get_timezone(void) {
    int internal_tz = PMIC_RTC_GetTimezone();
    return convert_to_standard_timezone(internal_tz);
}

void CWathcService_SetTimeZoneAndFormat(ws_client *pme, void *pdata)
{
    ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *timezone = NULL;
    ws_cJSON *dateformat = NULL;
	ws_cJSON *timeformat = NULL;
    int result = 0; 

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_TIMEZONE_FORMAT;

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        goto response;
    }

    timezone = ws_cJSON_GetObjectItem(req, "timezone");
    if (timezone != NULL) {
        int8_t nvdata = timezone->valueint;
        UI_NV_Write_Req(NV_SECTION_UI_SETTINGS, NV_OFFSETOF(nv_watch_settings_t, time_zone), 1, &nvdata);
        
        set_timezone(timezone->valueint);
        result = 1;
    }

    dateformat = ws_cJSON_GetObjectItem(req, "dateformat");
    if (dateformat != NULL) {
        uint8_t format = (uint8_t)dateformat->valueint;
        if (UI_NV_Write_Req(NV_SECTION_UI_SETTINGS,
                           NV_OFFSETOF(nv_watch_settings_t, date_format),
                           1,
                           &format) == 0) {
            result = 0;
            goto response;
        }
		g_date_format = format;
        result = 1;
    }
	timeformat = ws_cJSON_GetObjectItem(req, "timeformat");
    if (timeformat != NULL) {
        uint8_t format = (uint8_t)timeformat->valueint;  // 0:12小时制, 1:24小时制
        if (format <= 1) {  // 验证值的合法性
            if (UI_NV_Write_Req(NV_SECTION_UI_SETTINGS,
                               NV_OFFSETOF(nv_watch_settings_t, time_format),
                               1,
                               &format) == 0) {
                result = 0;
                goto response;
            }
            g_time_format = format;  // 更新全局变量
            result = 1;
        }
    }
response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");

    CWatchService_SendJsonData(pme, resp_root);
}
uint8_t get_date_temp_format(void)
{
    return g_date_format;
}
static uint8_t CWatchService_HandleReminders(ws_client * pme, void *json, ws_alarm_list *alarm_list)
{
    ws_cJSON *root_parse = (ws_cJSON *)json;
    ws_cJSON *request = NULL, *reminders_array = NULL, *reminder = NULL;
    ws_cJSON *time = NULL, *weekdays = NULL, *type = NULL;
    uint8_t mint = 0, hour = 0;

    if(NULL == pme || NULL == json || NULL == alarm_list)
    {
        return false;
    }

    request = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(NULL == request || !ws_cJSON_IsObject(request))
    {
        return false;
    }

    reminders_array = ws_cJSON_GetObjectItem(request, "reminders");
    if(NULL == reminders_array || !ws_cJSON_IsArray(reminders_array))
    {
        return false;
    }
    
    uint8_t cnt = ws_cJSON_GetArraySize(reminders_array);
    if(cnt == 0 || cnt > NV_ALARM_MAX_ALARM_NUM)
    {
        return false;
    }

    alarm_list->alarm = (ws_alarm*)lv_mem_alloc(NV_ALARM_MAX_ALARM_NUM * sizeof(ws_alarm));
    if(alarm_list->alarm == NULL)
    {
        return false;
    }
    memset(alarm_list->alarm, 0, NV_ALARM_MAX_ALARM_NUM * sizeof(ws_alarm));
    alarm_list->count = 0;
    alarm_list->single = 0;

    for(uint8_t reminderid = 0; reminderid < cnt; reminderid++)
    {
        reminder = ws_cJSON_GetArrayItem(reminders_array, reminderid);
        if(NULL == reminder || !ws_cJSON_IsObject(reminder))
        {
            continue;
        }

        type = ws_cJSON_GetObjectItem(reminder, "type");
        weekdays = ws_cJSON_GetObjectItem(reminder, "weekdays");
        time = ws_cJSON_GetObjectItem(reminder, "time");

        if(!type || !ws_cJSON_IsNumber(type) || 
           !weekdays || !weekdays->valuestring || 
           !time || !time->valuestring)
        {
            continue;
        }

        if(type->valueint < 0 || type->valueint > 2)
        {
            continue;
        }

        if(strlen(weekdays->valuestring) > 7)
        {
            continue;
        }

        if(strlen(time->valuestring) != 5 || time->valuestring[2] != ':')
        {
            continue;
        }

        alarm_list->count++;
        
        // 5. 设置类型
        alarm_list->alarm[reminderid].type = type->valueint;
        
        // 6. 解析星期
        uint8_t Wk = 0;
        bool valid_weekdays = true;
        for(int j = 0; j < strlen(weekdays->valuestring); j++)
        {
            int day = weekdays->valuestring[j] - '0';
            if(day < 1 || day > 7)
            {
                valid_weekdays = false;
                break;
            }
            switch(day)
            {
                case 1: Wk |= ALARM_MON; break;
                case 2: Wk |= ALARM_TUE; break;
                case 3: Wk |= ALARM_WED; break;
                case 4: Wk |= ALARM_THU; break;
                case 5: Wk |= ALARM_FRI; break;
                case 6: Wk |= ALARM_SAT; break;
                case 7: Wk |= ALARM_SUN; break;
            }
        }
        
        if(!valid_weekdays)
        {
            alarm_list->count--;
            continue;
        }

        alarm_list->alarm[reminderid].repeat = Wk;
        if(SET_REPEAT_ONCE != Wk)
            alarm_list->alarm[reminderid].repeat |= ALARM_REPEAT;

        // 7. 解析时间
        hour = ((time->valuestring[0] - '0') * 10) + (time->valuestring[1] - '0');
        mint = ((time->valuestring[3] - '0') * 10) + (time->valuestring[4] - '0');
        if(hour >= 24 || mint >= 60)
        {
            alarm_list->count--;
            continue;
        }
        sprintf(alarm_list->alarm[reminderid].time, "%02d:%02d:00", hour, mint);

        // 8. 设置其他字段
        alarm_list->alarm[reminderid].id = reminderid;
        alarm_list->alarm[reminderid].is_on = 1;
        alarm_list->alarm[reminderid].ring = 0;
        memset(alarm_list->alarm[reminderid].name, 0, WS_ALARM_NAME_MAX_LEN+1);
        memset(alarm_list->alarm[reminderid].token, 0, WS_ALARM_TOKEN_MAX_LEN+1);
        alarm_list->alarm[reminderid].extra = NULL;

        #if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
        alarm_list->alarm[reminderid].ring_en = 1;
        alarm_list->alarm[reminderid].vib_en = 1;
        #endif

        #if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
        alarm_list->alarm[reminderid].realert_delay = 0;
        alarm_list->alarm[reminderid].is_skip_holiday = 0;
        memset(alarm_list->alarm[reminderid].repeat_str, 0, 16);
        memset(alarm_list->alarm[reminderid].namegbk, 0, NV_ALARM_TOKEN_LEN_MAX*2);
        #endif
    }

    // 9. 检查是否解析到有效数据
    if(alarm_list->count == 0)
    {
        lv_mem_free(alarm_list->alarm);
        alarm_list->alarm = NULL;
        // return false;
    }

    return true;
}

static void CWathcService_SetFallDetection(ws_client *pme, void *pdata)
{
    ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *enabled = NULL;
    ws_cJSON *sensivity = NULL;
    nv_watch_settings_t settings = {0};
    int result = 0;

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_FALL_DETECTION;	

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        goto response;
    }

    UI_NV_Read_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (uint8_t *)&settings);

    enabled = ws_cJSON_GetObjectItem(req, "enabled");
    if (enabled != NULL) {
        settings.fall_detection_enabled = enabled->valueint;
        g_fall_detection.enabled = enabled->valueint;
    }

    sensivity = ws_cJSON_GetObjectItem(req, "sensivity"); 
    if (sensivity != NULL) {
        settings.fall_detection_sensitivity = sensivity->valueint;
        g_fall_detection.sensitivity = sensivity->valueint;
    }

    uint32_t written = UI_NV_Write_Req(NV_SECTION_UI_SETTINGS, 
                                      0,
                                      sizeof(nv_watch_settings_t),
                                      (uint8_t *)&settings);
    result = (written > 0) ? 1 : 0;  

response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");

    CWatchService_SendJsonData(pme, resp_root);
}
static void CWatchService_SetMessage(ws_client *pme, void *pdata)
{
    ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *msg = NULL;
    int result = 0;  

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SHOW_MESSAGE;	

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        goto response;
    }

    msg = ws_cJSON_GetObjectItem(req, "msg");
    if (msg != NULL && msg->valuestring != NULL) {
		// if(lv_watch_get_activity_obj(ACT_ID_SHORT_MSG))
		// {
		// 	msg_destory_spain();
		// }
        // msg_create_spain(NULL, msg->valuestring);

		char *message_copy = lv_mem_alloc(strlen(msg->valuestring) + 1);
		if (message_copy) {
			strcpy(message_copy, msg->valuestring);
			app_adaptor_msg_rcv_ind(message_copy);
		}
		result = 1; 
    }

response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");

    CWatchService_SendJsonData(pme, resp_root);
}
static void CWathcService_ECO_open(ws_client *pme, void *pdata)
{

	ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *status = NULL;
	ws_cJSON *period = NULL;
	int result = 1;  

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_ECO_OPEN;	

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        result = 0;  
        goto response;
    }
	status = ws_cJSON_GetObjectItem(req, "status");
	if (status==NULL){
		result = 0;  
        goto response;
	}
	period = ws_cJSON_GetObjectItem(req, "period");
	if (period==NULL){
		result = 0;  
        goto response;
	}
	ws_printf("open ECO mode");
	nv_watch_settings_t settings;
	UI_NV_Read_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (UINT8 *)&settings);
	if (strcmp(status->valuestring,"enable")==0)	// 平台协议开启ECO模式
	{	
		if (settings.eco_enable == 1)  // 当前已经开启ECO模式
		{  	
			if (settings.eco_period == period->valueint)
			{
				// 相同的ECO模式周期，无需操作
				return;
			}
			else
			{	
				// ECO下发的周期不相同，重新启动ECO
				settings.eco_period = period->valueint;
				set_eco_period(period->valueint);
				CWatchService_ECO_mode(1);
				result = 1;
			}
		}     
		else
		{	
			// 当前未开启ECO模式，开启ECO
			settings.eco_enable = 1;
			settings.eco_period = period->valueint;
			set_eco_period(period->valueint);
			CWatchService_ECO_mode(1);
			result = 1;
		}
		ws_printf("CWatchService_ECO settings.eco_enable %d,settings.eco_period=%d",settings.eco_enable,settings.eco_period);
		UI_NV_Write_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (UINT8 *)&settings);
	}
	else
	{
		result=0;
		goto response;
	}
		
	response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");
    CWatchService_SendJsonData(pme, resp_root);
}
static void heart_measure_thread(void *param) {

	heart_measure_flag = 1; // 标记开始测量心率

    uint8_t measure_time = 35;  // 35秒测量时间
    uint32_t total = 0;         // 心率累计值
    uint8_t valid_count = 0;    // 有效值计数
    bool hr_started = false;
    
	while(blood_measure_flag != 0) {
        ws_printf("Waiting for SPO2 measurements to complete...");
        uos_sleep_ms(3000);  // 每3秒检查一次
    }
    // 启动心率测量
    if(get_heart_rate_state() == HEART_DORMANT) {
        ws_printf("Start heart rate measure\n");
        HR_drv_start(HEALTH_MOTION_HR_APP);
        hr_started = true;
        set_heart_rate_state(HEART_APP_WORKING);
    }
    
    while(measure_time > 0) {
        ws_printf("Heart measure time: %d\n", measure_time);
        
        // 获取心率值
        int temp_value = HR_get_heart_value();
        ws_printf("Heart rate value: %d\n", temp_value);
        
        // 有效值处理
        if(temp_value > 0 && temp_value < 255) {
            total += temp_value;
            valid_count++;
            ws_printf("Valid heart rate count: %d, total: %d\n", valid_count, total);
        }
        
        uos_sleep_ms(1000);
        measure_time--;
    }

    int heart_value = 0;
    // 测量完成，处理结果
    if(valid_count > 0) {
        heart_value = total / valid_count;
        ws_printf("Final heart rate value: %d\n", heart_value);
    }
    
	CWatchService_UpHeartRate_Data(heart_value, 0, "", "");

    // 清理资源
    ws_printf("Heart measure complete\n");
    if(hr_started) {
        HR_drv_exit(HEALTH_MOTION_HR_APP);
    }
    set_heart_rate_state(HEART_DORMANT);
    Set_heart_rate_show_num(heart_value);

	if(heart_value > 0)
		Set_need_show_heart_rate_status(true);
		
	Set_heart_rate_test_ok_time();
	gsensor_step_resume();

	heart_measure_flag = 0; // 标记结束测量心率
}
static void blood_oxygen_measure_thread(void *param) {
	
	blood_measure_flag = 1; // 标记开始测量

    while(heart_measure_flag != 0) {
        ws_printf("Waiting for HR measurements to complete...");
        uos_sleep_ms(3000); // 每3秒检查一次状态
        
    }

    int spo2_measure_time = 0;
    bool hr_started = false;
    bool bo_started = false;
    
    // 初始化测量时间
	ws_printf("Start from 70s, need heart rate first\n");
	spo2_measure_time = MAX_TEST_TIME;  // 70秒
	ws_printf("get_heart_rate_state: %d\n", get_heart_rate_state());
	if(get_heart_rate_state()== HEART_DORMANT) {
		ws_printf("Start heart rate measure\n");
		HR_drv_start(HEALTH_MOTION_HR_APP);
		set_heart_rate_state(HEART_APP_WORKING);
		hr_started = true;
	}
    
    while(spo2_measure_time > 0) {
        ws_printf("SPO2 Measure time: %d\n", spo2_measure_time);
        
		if(spo2_measure_time == 35) {
			int heart_value = HR_get_heart_latest_valid_value();
			ws_printf("Check at 35s - heart: %d, wear: %d\n", heart_value, HR_get_wear_state());
			
			// 先检查状态
			if(HR_get_wear_state() != 1 || heart_value <= 0) {
				ws_printf("Check failed - invalid wear state or heart value\n");
				spo2_measure_time = 0;
				blood_oxygen_data.oxygen_value = 0;
				if(hr_started) {
					HR_drv_exit(HEALTH_MOTION_HR_APP);
					hr_started = false;
				}
				set_heart_rate_state(HEART_DORMANT);
				break;
			}
			
			// 状态正常才退出心率测量
			if(hr_started) {
				ws_printf("Exit heart rate measure\n");
				HR_drv_exit(HEALTH_MOTION_HR_APP);
				hr_started = false;
			}
			
			ws_printf("Check passed - starting blood oxygen measurement\n");
		}
        
        // 30秒时启动血氧测量
        if(spo2_measure_time == 30 && !bo_started) {
            ws_printf("Start blood oxygen measure at 30s\n");
            HR_drv_spo2_start(HEALTH_MOTION_BO_APP);	
			set_heart_rate_state(BLOOD_APP_WORKING);
            bo_started = true;
        }
        
        // 只在血氧测量启动后且时间小于30秒时获取血氧值
        if(bo_started && spo2_measure_time < 30) {
            int temp_value = HR_get_blood_spo2();
            ws_printf("Blood oxygen value: %d at time: %d\n", temp_value, spo2_measure_time);
            
            if(temp_value >= 100 || (temp_value == 0 && spo2_measure_time == 1)) {
                // 测量失败
                ws_printf("Measure failed - invalid blood oxygen value\n");
                spo2_measure_time = 0;
                blood_oxygen_data.oxygen_value = 0;
            } else if(spo2_measure_time == 1) {
                // 测量成功
                ws_printf("Measure success - value: %d\n", temp_value);
                blood_oxygen_data.cur_time = PMIC_RTC_GetTime_Count(1);
                blood_oxygen_data.oxygen_value = temp_value < 95 ? 95 : temp_value;
            }
        }
        
        uos_sleep_ms(1000);
        spo2_measure_time--;
    }

	CWatchService_HealthRate_Data(blood_oxygen_data.oxygen_value, 1);
    
    // 测量完成，清理资源
    ws_printf("Measure complete, bo_started: %d\n", bo_started);
    if(bo_started) {
        HR_drv_exit(HEALTH_MOTION_BO_APP);
    } else if(hr_started) {
        HR_drv_exit(HEALTH_MOTION_HR_APP);
    }
    set_heart_rate_state(HEART_DORMANT);
	gsensor_step_resume();
	blood_measure_flag = 0; // 标记测量完成
}
static void CWathcService_StartMeasure(ws_client *pme, void *pdata)
{
    ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *type = NULL;
    int result = 1; 
	
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_START_MEASURE;	

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        result = 0; 
        goto response;
    }

    type = ws_cJSON_GetObjectItem(req, "type");
    if (type == NULL) {
        result = 0;
        goto response;
    }

	switch(type->valueint) {
		case MEASURE_TYPE_HEART:
			#if USE_DRV_HEART != 0
			if(heart_measure_flag == 0)
			{
				ws_printf("PPPPP   Heart measure task is not running!!!!!!!!!!!!!!!!!!!!!!!\n");
				if(heart_measure_task_handle != NULL)
				{
					uos_delete_task(heart_measure_task_handle);
					heart_measure_task_handle = NULL;
					
				}
				heart_measure_task_handle = uos_create_task(
					heart_measure_thread,
					NULL,
					16,
					1024 * 8,
					211,
					"heart_measure_task"
				);
			}
			else
			{
				ws_printf("PPPPP   Heart measure task already running!!!!!!!!!!!!!!!!!!!!!!!\n");
			}
			#endif
			break;
			
		case MEASURE_TYPE_SPO2:
			#if USE_DRV_HEART != 0
			if(blood_measure_flag == 0)
			{
				ws_printf("PPPPPP   Blood measure task is not running!!!!!!!!!!!!!!!!!!!!!!!\n");
				if(blood_measure_task_handle!= NULL)
				{
					uos_delete_task(blood_measure_task_handle);
					blood_measure_task_handle = NULL;
				}
				blood_measure_task_handle = uos_create_task(
					blood_oxygen_measure_thread,
					NULL,
					16,          
					1024 * 8,   
					212,         
					"blood_measure_task"
				);
			}
			else
			{
				ws_printf("PPPPPP  Blood measure task already running!!!!!!!!!!!!!!!!!!!!!!!\n");	
			}

			#endif
			break;
			
		default:
			break;
	}


response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");

    CWatchService_SendJsonData(pme, resp_root);
}
static void CWatchService_SetLocationConfig(ws_client *pme, void *pdata)
{
    ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *frequency = NULL;
    ws_cJSON *gps = NULL;
    int result = 1; 

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_LOCATION_CONFIG;	

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        result = 0;  
        goto response;
    }

    frequency = ws_cJSON_GetObjectItem(req, "frequency");
    if (frequency != NULL) {
        ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
        userData->m_base.ws_reg.upPosTime = 60 * (frequency->valueint);
        myNVMgr_SetWsDevPosUpTime(userData->m_base.ws_reg.upPosTime);
    }

    gps = ws_cJSON_GetObjectItem(req, "gps");
    if (gps != NULL) {
        ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
        userData->mGpsON = (char)gps->valueint;
        myNVMgr_SetGpsEnable((uint8_t)userData->mGpsON);
    }

response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");

    CWatchService_SendJsonData(pme, resp_root);
}
static void CWatchService_SetMeasureFreq(ws_client *pme, void *pdata)
{
    ws_cJSON *root = (ws_cJSON *)pdata;
    ws_cJSON *req = NULL;
    ws_cJSON *heart_freq = NULL;
    ws_cJSON *spo2_freq = NULL;
    int result = 1;

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->type = KAER_CMD_SET_HEALTH_FREQ;	

    req = ws_cJSON_GetObjectItem(root, "req");
    if (req == NULL) {
        result = 0;
        goto response;
    }

    // 设置心率上报频率
    heart_freq = ws_cJSON_GetObjectItem(req, "heart_freq");
    if (heart_freq != NULL) {
        ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
		if(heart_freq->valueint >= 5*60 && heart_freq->valueint <= 60*60*24) {
			userData->m_base.health_freq[WS_HD_HEAT_RATE] = (uint16_t)heart_freq->valueint;
		}
		else 
		{
			userData->m_base.health_freq[WS_HD_HEAT_RATE] = heart_rate_UP_TIMER;
		}
        myNVMgr_SetHealthFreq(userData->m_base.health_freq[WS_HD_HEAT_RATE],WS_HD_HEAT_RATE);
    }

    // 设置血氧上报频率
    spo2_freq = ws_cJSON_GetObjectItem(req, "spo2_freq");
    if (spo2_freq != NULL) {
        ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
		if(spo2_freq->valueint >= 60*60 && spo2_freq->valueint <= 60*60*24) {
			userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN] = (uint16_t)spo2_freq->valueint;
		}
		else 
		{
			userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN] = SPO2_UP_TIMER;
		}
        myNVMgr_SetHealthFreq(userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN],WS_HD_BLOOD_OXYGEN);
    }

response:
    ws_cJSON *resp_root = ws_cJSON_CreateObject();
    if (resp_root == NULL) {
        return;
    }

    ws_cJSON *resp = ws_cJSON_CreateObject();
    if (resp == NULL) {
        ws_cJSON_Delete(resp_root);
        return;
    }

    ws_cJSON_AddItemToObject(resp_root, "res", resp);
    ws_cJSON_AddStringToObject(resp, "result", result ? "1" : "0");

    CWatchService_SendJsonData(pme, resp_root);
}
#endif
void CWatchService_CmdProcess(ws_client * po, ws_evt_id_e cmd_id, void *pdata, double seq)
{
	if(is_hwtest_mode())   //产测模式下不处理协议任务
		return;

    ws_client *pme = (ws_client*)po;

    if(NULL == pme){
        WS_PRINTF("CWatchService--CWatchService_CmdProcess >> service not creat  cmd_id:%d",  cmd_id);
        return;
    }
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

	WS_PRINTF("CWatchService--CWatchService_CmdProcess >> cmd=%d \n", cmd_id);
    
	if(WS_SERVICE_EVENT_MAX<= cmd_id || cmd_id < CMD_C2S_DEV_LOGIN)
	{
		WS_PRINTF("CWatchService--CWatchService_CmdProcess >> cmd error\n", cmd_id, 0, 0);
        return;
	}

	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));	

    if(cmd_info == NULL)
    {
		WS_PRINTF("CWatchService--CWatchService_CmdProcess >> no memory!\n");
        return;
    }

    memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
    cmd_info->evt = cmd_id;
    cmd_info->seq = seq; 
    cmd_info->client = po; 
	switch(cmd_id)
	{
		case CMD_DEV_IND_KAER_UPDATA:
			CWatchService_SendSimpleCmd(pme, KAER_CMD_UPDATA, seq);
			CWathcService_SetFotaData(pme, pdata);	
            break;
		case CMD_LOCAL_STOP_BEAT_TIMER:
			CWatchService_HeartbeatStop(pme);
            break;
        case CMD_C2S_DEV_LOGIN: 
			
//			dm_start_send_msg_active();

			#if USE_LV_WATCH_PREVENT_BULLYING != 0 || USE_LV_WATCH_ONLINE_CAR_HAILING !=0
			MMI_ModemAdp_Send_AT_Handler("AT*IMSLOCURI?@*IMSLOCURI",CWathcService_GetPhoneNum_Callback);
			#endif
            CWatchService_DevLogin(pme, (ws_regInfo*)&userData->m_base.ws_reg);
			CWathcService_Login_Retry_Timer_Create();
			#if USE_LV_WATCH_POWEROFF_CHANGE != 0
			alarm_init();
			#endif
			#if USE_LV_WATCH_HEBAO_CHUXING != 0
			CWatchService_Get_Seid();
			modlue_init_queue();
			uos_sleep(20);
			CWatchService_Get_Hebao_Balance_CardNo();
			#endif
            break;
        case CMD_S2C_DEV_LOGIN_RSP: 
            WS_PRINTF("$$$$$$$======CWatchService--CWatchService_CmdProcess >> login success!\n", 0, 0, 0);
			CWathcService_Login_Retry_Timer_Stop();//停止重连定时器
			set_last_link_success();
			CWathcService_GetServiceLoginRsp(pme,&cmd_info->cmd.s_rsp, pdata);
			CWatchService_SetOnLine(pme, true);//设置当前套接字连接状态
			Flw_InitLocationData(); 		
			CWatchService_NofityClient(pme, cmd_info);
            //有报警信息需要发送，优先发送报警
			if(get_sos_send_type() != 0)
			{
				ws_printf("$$$$$$$ have sos event  !!!!!!!!!!!!!!!");
				MMI_ModemAdp_WS_AlarmInd(get_sos_send_type());
				status_update_location_state(true);
				Flw_NotPeridLocatinConfig(LOCATION_TYPE_IMMEDIATE);
				set_sos_send_type(0);
			}
			else if(get_eco_enable()==true&&get_ECO_connect()!=0)
			{
				ws_printf("--------------- ECO mode need send 03dd !!!!!!!!!!!!!!!");
				status_update_location_state(true);
				Flw_NotPeridLocatinConfig(LOCATION_TYPE_IMMEDIATE);
			}

            CWatchService_SendVersionInfo(pme, NULL);//连接成功后发送版本号
			#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
			CWatchService_Highfreq_param_init();
			#endif
			#if USE_LV_WATCH_GET_LAST_SAVE_LATLON != 0
			CWatchService_Get_Last_LatLon();
			#endif
            uos_sleep(2);
			CWatchService_CmdProcess((ws_client*)pme, CMD_C2S_PONG, NULL, 0);	
            uos_sleep(2);

			userData->m_base.m_nLastTimeForUploadLoc = 0;	//重连平台后重启开始周期定位
			if (!get_eco_enable()){
				CWatchService_CmdProcess(pme, CMD_DEV_IND_LOCATION, 0, 0);
			}
			else{
				ws_printf("$$$$ECO mode no send Cycle LOCATION");	
			}

            uos_sleep(2);
			if(WatchUtility_GetWsSwitch()->openCloseAlert && userData->prelogin)
			{
				if(Hal_Powerup_Get_Reason() != HAL_POWERUP_SILENT_RESET)
					CWatchService_AlarmIndCmd(pme, KAER_ALARM_TYPE_POWER_ON, seq);
				userData->prelogin = false;
			}	
           	CWatchService_SetNewUrlPort();
		#if USE_LV_WATCH_WS_KAER != 0	
			MMI_ModemAdp_WS_Reconnect_Check(pme,0,0);
		#endif
		#if USE_LV_WATCH_KR_WEILIAO != 0
			CWatchService_Get_Upfile_A_Login();
		#endif
            uos_sleep(2);
            if(pme->adapter->timer_common == NULL)
            {
                uos_timer_create(&pme->adapter->timer_common);
            }
            uos_timer_stop(pme->adapter->timer_common);
            uos_timer_start(pme->adapter->timer_common, 30*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, CWatchService_CommonTimerCB, (uint32_t)pme);
			if (!get_eco_enable())
			{
				CWatchService_HeartbeatStart(pme);		//ECO模式下不周期上报心跳，只在连接上时上报一次
			}              
			if(Hal_Powerup_Get_Reason() == HAL_POWERUP_SILENT_RESET)
				;
			else if(s_connect_success_fag == 0)
			{
				s_connect_success_fag++;
				if(gui_is_lcd_valid() == false)
				{
				#if USE_LV_WATCH_LOCK_ICCID != 0
					if((ke_GetLockIccidFlag() == 1) && (check_iccid_is_same() == 1)&& (CWatchServe_No_screen_Card_Plugout()==1))
					{
						CWatchServe_No_screen_Set_Card_Plugout(0);
					}
				#endif
				#if USE_LV_WATCH_TTS != 0
					if((WatchUtility_is_night_mute_time() == false))
					{
						if(Hal_Powerup_Get_Reason() != HAL_POWERUP_SILENT_RESET) 
						    tts_play_start(VOICE_WS_LOGIN_SUCESS, NULL, HAL_AUDIO_SPK_LEVEL_MAX, tts_read_immediately);
					}
				#endif
				}
			}

			#if USE_LV_WATCH_CONFIG_MAINMENU != 0 && USE_LV_WATCH_YIXIN==0
			uos_sleep(50);
			CWatchService_Send_function_list(pme);
			#endif
			#if USE_LV_WATCH_HEBAO_CHUXING != 0
			uos_sleep(10);
			MMI_Modem_Get_Msisdn_Req(MMI_ModemAdp_Get_UI_SimId(RIL_SIM_1));
			uos_sleep(10);
			MMI_ModemAdp_Send_AT_Handler("AT*IMSLOCURI?@*IMSLOCURI",CWathcService_GetPhoneNum_Callback);
			#elif USE_LV_WATCH_AT_GET_OWNNUMBER != 0
			if(strlen(CWathcService_Get_OwnNumber()) < 3)
			{
				MMI_ModemAdp_Send_AT_Handler("AT*IMSLOCURI?@*IMSLOCURI",CWathcService_GetPhoneNum_Callback);
			}	
			#endif

			#if USE_LV_WATCH_PULL_CONFIG != 0
			if(CWatchService_get_plat_type()==0)
			{
				g_pull_config = KAER_PULL_CONFIG_FAMILYNUM_SOS;
				Http_pull_config_timer_Create();
			}
			#endif
			
			#if USE_LV_WATCH_MIGU != 0
			On_request_PlanList();
			#endif
			#if USE_LV_WATCH_KEY_KW19 != 0
			#if USE_LV_WATCH_SPAIN == 1
				if (!get_eco_enable()){
					Start_SPO2_Report_Timer_Task();//血氧
					Start_HR_Report_Timer_Task();//心率
				}
			#endif
			#endif
			break;       
        case CMD_C2S_PONG_RSP:  
            break;
		case CMD_C2S_POWER_LOW_MODE:            
            CWatchService_SendBatteryInfo(pme, NULL);
            break;
		 case CMD_S2C_SET_PERIOD:
            CWathcService_SetPeriod(pme,&cmd_info->cmd.s_rsp, pdata);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_HEART_S, seq);
            break;
		case CMD_S2C_SETPARAM_COURSE:
#if USE_LV_WATCH_COURSE != 0
			if(CWathcService_GetCourse(pme, &cmd_info->cmd.course_list, pdata))
            	CWatchService_NofityClient(pme, cmd_info);
#endif
			CWatchService_SendCourseRspCmd(pme, KAER_CMD_COURSE, seq);
            break;
        case CMD_C2S_PONG:	
        	CWatchService_SendBatteryInfo(pme, NULL);   //蹇冭烦鍙戦€佺數閲?
            break;
        case CMD_DEV_IND_LOCATION_RSP:
        	userData->mLocationRspFlg = 1;
        	break;
	#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
		case CMD_LOCAL_LOCAL_POS_TIMER_EXPIRE:///1min interval
			uos_timer_stop(g_common_pos_timer);
			uos_timer_start(g_common_pos_timer, 60*TICKES_IN_SECOND, 60*TICKES_IN_SECOND, CWatchService_LocalPosTimerCB, (uint32_t)pme);
			WS_PRINTF("__________ CMD_LOCAL_LOCAL_POS_TIMER_EXPIRE TYPE=%d,cnt=%d",userData->mLocationType,g_pos_uptime_count);
			if(userData->mLocationType == LOCATION_TYPE_PERIOD) 
			{
			//after report loc data ,clear g_pos_uptime_count
				g_pos_uptime_count++;
				uint16_t uptime_min = (userData->m_base.ws_reg.upPosTime/60);
				if(uptime_min == 0)
					uptime_min = 10;
				//minute ji suan
				if(g_pos_uptime_count >= uptime_min)
				{
					g_pos_uptime_count = 0;
					if(userData->m_base.is_connected)
					{
						CWatchService_Send_PeriodLocalDat(pme);
					}
				}
			}
			break;
   #endif
		
        case CMD_DEV_IND_LOCATION:
		{
#if USE_CRANE_WATCH_GPS != 0
			uint8_t is_had_gps = GetGpsHaveFixed();
#else
			uint8_t is_had_gps = 0;
#endif
			uint32_t up_time = 0;
			if(userData->mPreSendLocCnt	== 0)
			{
				#if USE_LV_WATCH_YIXIN !=0 || USE_LV_WATCH_SPAIN != 0
				up_time = userData->m_base.ws_reg.upPosTime;
				#else
				if(is_had_gps)
				{
					up_time = userData->m_base.ws_reg.upPosTime - 30;
				}
				else
				{
					up_time = userData->m_base.ws_reg.upPosTime - 30;
				}	
				#endif
			}
			if(get_eco_enable()==true)
			{
				up_time = 0;
			}
			
			WS_PRINTF("CWatchService-LOCATION is_cold_gps=%d %d,%d,%d\n",is_had_gps,userData->m_base.m_nLastTimeForUploadLoc, (GETTIMESECONDS() - userData->m_base.m_nLastTimeForUploadLoc), up_time);
			#if USE_LV_WATCH_SAME_TIME_OPEN_STOP != 0 || USE_LV_WATCH_LOCK_PHONE != 0
			#if USE_LV_WATCH_SAME_TIME_OPEN_STOP != 0
			if(ke_GetFixLocFlag()==1)
			#else
			if(check_iccid_is_same()==0)
			#endif
			{
				userData->mPreSendLocCnt = 0;
				userData->mLocationRspFlg = 1;

				/* restart next upload period */
				// userData->m_base.m_nLastTimeForUploadLoc = GETTIMESECONDS();
				
				// uos_timer_stop(pme->adapter->timer_common);
				// uos_timer_start(pme->adapter->timer_common, 30*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, CWatchService_CommonTimerCB, (uint32_t)pme);
			}
			else
			#endif
			if((GETTIMESECONDS() - userData->m_base.m_nLastTimeForUploadLoc) >= up_time
			&& (userData->m_base.is_connected == true))   
			{
				//触发定位时即记录定位时间，消除周期渐进延迟
				if(userData->mPreSendLocCnt	== 0)		
				{
					// ws_printf("11111111111111111111111 m_nLastTimeForUploadLoc");
					userData->m_base.m_nLastTimeForUploadLoc = GETTIMESECONDS();
				}

				userData->mPreSendLocCnt++;
				WS_PRINTF("CMD_DEV_IND_LOCATION userData->mPreSendLocCnt is %d %d",userData->mPreSendLocCnt,pdata);
				if(pdata == NULL)	 
				{
					WS_PRINTF("CMD_DEV_IND_LOCATION pdata is NULL");
					if(userData->mPreSendLocCnt == 1)
					{
					#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
						if(userData->mLocationType != LOCATION_TYPE_PERIOD)
							g_pos_uptime_count = 0;
					#endif

						if((ke_GetGpsFirstSwitch() == GPS_FIRST)
							#if USE_LV_WATCH_HIGH_FREQ_SCHOOL_LOC != 0
							|| (CWatchService_Is_Highfreq_Loc() == 1)
							#endif
							#if USE_LV_WATCH_SPAIN != 0
							|| (userData->mGpsON == 1)
							#endif
						)
						{
						ws_printf("___111111111___Flw_JudgeWifiLocationType_______mGpsON is %d", userData->mGpsON);
						#if USE_LV_WATCH_LOCATION_BLEMAC != 0
							Open_bluetooth_preper_Location(Flw_JudgeLocationType);					
						#else
							Flw_JudgeLocationType();
						#endif
						}
						else
						{
						ws_printf("___**********___Flw_JudgeWifiLocationType_______mGpsON is %d", userData->mGpsON);
						    #if USE_LV_WATCH_LOCATION_BLEMAC != 0
								Open_bluetooth_preper_Location(Flw_JudgeWifiLocationType);
							#else
								Flw_JudgeWifiLocationType();
							#endif
						}
						status_update_location_state(true);
					}
				}
				else   
				{
					ws_evt_msg_t *info = (ws_evt_msg_t*)pdata;
					uint32_t UpType = info->cmd.LocationUpType;
					WS_PRINTF("CMD_DEV_IND_LOCATION UpType is %d ", UpType);
					WS_PRINTF("mHisLocationInfo.mLastUpType is %d",userData->mHisLocationInfo.mLastUpType);

					if(userData->mLocationType == LOCATION_TYPE_PERIOD) 
					{
						userData->mLocationRspFlg = 0;
					}

					if(((userData->mLocationType == LOCATION_TYPE_PERIOD))
						&& ((userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_LBS	&& UpType == LOCATION_UP_TYPE_LAST) || (UpType == LOCATION_UP_TYPE_LBS)) 
						&& WatchUtility_IsMuteTime())
					{
						WS_PRINTF("____RUN_HERE____");
							#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
							g_pos_uptime_count = 0;
							#endif
							userData->mPreSendLocCnt = 0;
							userData->mLocationRspFlg = 1;

							/* restart next upload period */
							// userData->m_base.m_nLastTimeForUploadLoc = GETTIMESECONDS();
							
							// uos_timer_stop(pme->adapter->timer_common);
							// uos_timer_start(pme->adapter->timer_common, 30*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, CWatchService_CommonTimerCB, (uint32_t)pme);

							CWatchService_SendLocation(pme,UpType);	//西班牙都上报位置
					}
					else if(((userData->mLocationType == LOCATION_TYPE_PERIOD))
						&& ((userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_LBS	&& UpType == LOCATION_UP_TYPE_LAST) || (UpType == LOCATION_UP_TYPE_LBS))
						&& s_up_lbs_flag <= 3)
					{
						WS_PRINTF("____RUN_HERE____2");
						#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
						g_pos_uptime_count = 0;
						#endif
 
						s_up_lbs_flag ++;
						WS_PRINTF("s_up_lbs_flag is %d ", s_up_lbs_flag);
						userData->mPreSendLocCnt = 0;
						userData->mLocationRspFlg = 1;
						/* restart next upload period */
						// userData->m_base.m_nLastTimeForUploadLoc = GETTIMESECONDS();
						// uos_timer_stop(pme->adapter->timer_common);
						// uos_timer_start(pme->adapter->timer_common, 30*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, CWatchService_CommonTimerCB, (uint32_t)pme);

						CWatchService_SendLocation(pme,UpType);	//西班牙都上报位置
					}
					else
					{
						WS_PRINTF("____RUN_HERE____3");
						if((userData->mHisLocationInfo.mLastUpType == LOCATION_UP_TYPE_LBS	&& UpType == LOCATION_UP_TYPE_LAST) || (UpType == LOCATION_UP_TYPE_LBS))
							;
						else
							s_up_lbs_flag = 0;
						
						CWatchService_SendLocation(pme,UpType);
					}
					status_update_location_state(false);
				}
			}								
		}
            break;
			case CMD_C2S_SHUTDOWN_REQ:
	        pme->adapter->srv->host = NULL; // Don't reconnect again!
	        break;
		case CMD_DEV_IND_ALARM:		
			#if USE_LV_WATCH_SPAIN == 0
			if(((ws_alarmInfo*)pdata)->type==KAER_ALARM_TYPE_POWER_OFF)
			{
				if(WatchUtility_GetWsSwitch()->openCloseAlert)
					CWatchService_AlarmIndCmd(pme, ((ws_alarmInfo*)pdata)->type, seq);
			}        		
			else   //sos
			#endif
			{
				#if USE_LV_WATCH_SPAIN != 0
				// status_update_location_state(true);
				// Flw_NotPeridLocatinConfig(((ws_alarmInfo*)pdata)->type);
				CWatchService_AlarmIndCmd(pme, ((ws_alarmInfo*)pdata)->type, seq);
				#else
				Flw_NotPeridLocatinConfig(LOCATION_TYPE_SOS);
				#endif
			}
            break;
        case CMD_DEV_IND_SMS:
            CWatchService_SendSmsInfo(pme, (ws_sms_info*)pdata);
            break;
		case CMD_DOWNLOAD_DATI:
			#if USE_LV_WATCH_DATI != 0
			{
				uint8 sresult = 0;
				//cplog_printf("\n CWatchService--CWatchService_CmdProcess >> CMD_DOWNLOAD_DATI\n" );
				sresult = CWathcService_DownDatiProcess(pme, pdata);
				if(sresult >= 1)
				{
					lv_watch_go_home();
					if(CWatchService_Is_Dati_Status() == 1)
					{
						set_led2_stu(1);
						tts_play_start(TTS_START_ANSWER, NULL, HAL_AUDIO_SPK_LEVEL_MAX, tts_read_immediately);
						watch_set_suspend_enable(false);
						watch_set_lcd_status(true);
		        		//watch_wakeup_time_reset();
					} 
					else
					{
						set_led2_stu(0);
						tts_play_start(TTS_END_ANSWER, NULL, HAL_AUDIO_SPK_LEVEL_MAX, tts_read_immediately); 
						watch_set_suspend_enable(true); 
					}
				}

			}
			#endif
			break;
		case CMD_UPLOAD_DATI:
			#if USE_LV_WATCH_DATI != 0
			//cplog_printf("\n CWatchService--CWatchService_CmdProcess >> CMD_UPLOAD_DATI\n" );
			CWathcService_UpDatiProcess(pme, (key_stu_t*)pdata);
			#endif
			break;
		case CMD_UPLOAD_DATI_RESPONSE:
			#if USE_LV_WATCH_DATI != 0
			{
				uint8 sresult = 0;
				sresult = CWathcService_UpDatiResponse(pme, pdata);
				//cplog_printf("\n CMD_UPLOAD_DATI_RESPONSE res= %d\n" ,sresult);
				if(sresult >= 2)
				{
					CWatchService_NofityClient(pme, cmd_info);
				}
				else if(sresult == 3)
				{
					CWatchService_NofityClient(pme, cmd_info);
				}
				else
				{
				}
			}
			#endif
			break;
        case CMD_S2C_SETPARAM_CONTACT_LIST:
            CWathcService_ContactsProcess(pme, pdata, cmd_info);
            CWatchService_NofityClient(pme, cmd_info);            
            break;
        case CMD_S2C_SETPARAM_SLIENT_PERIOD:
            CWathcService_GetMuteList(pme, pdata, &cmd_info->cmd.mute_list);
            CWatchService_NofityClient(pme, cmd_info);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_STUDY_TIME, seq);
            break;
		case CMD_S2C_SETPARAM_POWER_ONOFF:
            CWathcService_OpenClose(pme, pdata, &cmd_info->cmd.power_period);
            //atchService_NofityClient(pme, cmd_info);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_POWER_ONOFF, seq);
            break;	
		case CMD_S2C_SETPARAM_SET_ONOFF:
            CWathcService_SetOnOff(pme, pdata, &cmd_info->cmd.setOnOff);
            CWatchService_NofityClient(pme, cmd_info);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_ONOFF, seq);
            break;
		case CMD_S2C_SETPARAM_SET_FENCE:
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_FENCE, seq);
            break;
 		case CMD_S2C_SETPARAM_INIT_PWD:
			CWatchService_SendSimpleCmd(pme, KAER_CMD_INIT_PWD, seq);
       	 	break;
		case CMD_S2C_SETPARAM_SET_ADDR:
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_ADDR, seq);
           	CWathcService_SetUrl(pme, pdata, &cmd_info->cmd.setOnOff);
            break;	
       	case CMD_S2C_SETPARAM_NEW_TEXT_MSG:
			CWatchService_SendSimpleCmd(pme, KAER_CMD_NEW_TEXT_MSG, seq);
           	CWathcService_PushMsg(pme, pdata, &cmd_info->cmd.setOnOff);
            break;
        case CMD_S2C_EXEC_REBOOT:
			CWatchService_SendSimpleCmd(pme, KAER_CMD_REBOOT, seq);
           	if(CWathcService_Reboot(pme, pdata, &cmd_info->cmd.setOnOff) == true);
           	{
           		//CWatchService_NofityClient(pme, cmd_info);
           	}
            break;
			
        case CMD_S2C_SETPARAM_ALARM_LIST:
            CWathcService_GetAlarmInfo(pme, pdata, &cmd_info->cmd.alarm_list);
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_ALARM_LIST, seq);
            break;
        case CMD_S2C_SETPARAM_LOC_INTERVAL:
            CWathcService_GetLocInterval(pme, pdata, &cmd_info->cmd.uint32Parma);
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_POSUP_TIME, seq);
            CWatchService_CmdProcess(pme, CMD_DEV_IND_LOCATION, NULL, 0);
            break;
        case CMD_S2C_SETPARAM_WHITE_LIST_FLAG:
            CWathcService_GetWitelistOn(pme, pdata, &cmd_info->cmd.boolParma);
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_WHITE_LIST_FLAG, seq);
            break;
        case CMD_S2C_SETPARAM_VOLUME:
            CWathcService_GetVolume(pme, pdata, &cmd_info->cmd.int16Parma);
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_VOLUME, seq);
            break;
        case CMD_S2C_EXEC_MONITOR:
            CWathcService_GetMonitorNum(pme, pdata, cmd_info->cmd.monitor_num);
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_MONITER, seq);
            break;
        case CMD_S2C_EXEC_LOCATION:
		#if USE_LV_WATCH_SPAIN != 0
			status_update_location_state(true);
		#endif
        	Flw_NotPeridLocatinConfig(LOCATION_TYPE_IMMEDIATE);
            break;
        case CMD_LOCAL_DEVICE_OFFLINE: 
            userData->m_base.is_connected = false;
            WS_PRINTF("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!CWatchService--CWatchService_CmdProcess CMD_LOCAL_DEVICE_OFFLINE!\n", 0, 0, 0);
            CWatchService_NofityClient(pme, cmd_info);
			CWatchService_HeartbeatStop(pme);
			#if USE_LV_WATCH_SPAIN!=0
			// SPO2_value_timer_thread_stop();
			// CWatchService_Stop_Heart_rate();
			Stop_HR_Report_Timer_Task();
			Stop_SPO2_Report_Timer_Task();
			#endif
            break;
        case CMD_LOCAL_DEVICE_ONLINE:
            userData->m_base.is_connected = true;
            WS_PRINTF("CWatchService--CWatchService_CmdProcess CMD_LOCAL_DEVICE_ONLINE!\n", 0, 0, 0);
            CWatchService_NofityClient(pme, cmd_info);
            break;
        case CMD_S2C_SETPARAM_RESET:
            CWatchService_SendSimpleCmd(pme, KAER_CMD_RESET_FACTORY, seq);
			CWatchService_NofityClient(pme, cmd_info);
			#if USE_LV_WATCH_KR_WEILIAO != 0
			if(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_SESSION)||lv_watch_get_activity_obj(ACT_ID_VOICE_MSG)|| lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT))
			{
				lv_watch_go_home();
			}
			CwatchService_Get_Contactlist();
			#endif
            break;
        case CMD_S2C_EXEC_SHUTDOWM:
            pme->adapter->srv->host = NULL; // Don't reconnect again!
            CWatchService_SendSimpleCmd(pme, KAER_CMD_PWROFF, seq);
            CWatchService_NofityClient(pme, cmd_info);
            break;
        case CMD_S2C_EXEC_CAPTURE:
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_CAPTURE, seq);
            break;
        case CMD_S2C_EXEC_RING:
            CWatchService_NofityClient(pme, cmd_info);
            CWatchService_SendSimpleCmd(pme, KAER_CMD_FIND_WATCH, seq);
            break;
        case CMD_S2C_QUIT_FAMILY_RESET:
            CWatchService_NofityClient(pme, cmd_info);
            break;
        case CMD_C2S_QUIT_FAMILY_RESET:
            CWatchService_SendSimpleCmd(pme, KAER_CMD_RESET_FACTORY_T, KAER_IDENT);
            break;
        case CMD_LOCAL_COMMON_TIMER_EXPIRE:		
			ke_fota_check();
		    Flw_UpdateActiveCount();
			#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
			CWathcService_Check_Group_Period();
			#endif
			#if USE_LV_WATCH_MIGU != 0 && USE_LV_WATCH_MGU_ZJ != 1
			mgu_plan_check_time();
			#endif
            if(userData->m_base.is_connected)
            {
                WS_PRINTF("CMD_LOCAL_COMMON_TIMER_EXPIRE!\n");
				#if USE_LV_WATCH_SPAIN != 0
					if (!get_eco_enable())
					{
						
						CWatchService_CmdProcess(pme, CMD_DEV_IND_LOCATION, NULL, 0);
					}
					else
					{
						printf("7919$$$$ ECO mode no send LOCATION\n");
					}  
				#else
					CWatchService_UpdateBatteryInfor(pme);
					CWatchService_CmdProcess(pme, CMD_DEV_IND_LOCATION, NULL, 0); 
				#endif
            }
            break;
        case CMD_LOCAL_START_BEAT_LISTENER_TIMER:
            CWatchService_HeartbeatTimerExpire(pme);
            break;
        case CMD_LOCAL_START_BEAT_CHECKOUT_TIMER:
            CWatchService_CheckbeatTimeOutExpire(pme);
            break;
   		case CMD_S2C_SETPARAM_SMS_INTERCEPT:
			#if USE_LV_WATCH_SMS_INTERCEPT !=0
			CWathcService_SetSMSIntercept_Old(pme,pdata);// old protocal not support
			#endif
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SMS_INTERCEPT, seq);
			break;
		case CMD_S2C_SET_GOHOME_WIFI:
			#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
			CWathcService_SetGohome_WifiMac(pme,pdata);
			#endif
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_GOHOME_WIFI, seq);
			break;
		case CMD_S2C_UP_CALL_LOG:  //涓婃姤閫氳瘽璁板綍
			#if USE_LV_WATCH_UPLOAD_CALLLOG !=0
			CWatchService_UpCallLog(pme, (ws_evt_msg_t*)pdata, seq);
			#endif
            break;
		case CMD_S2C_SET_FUNCTION_LIST:
			#if USE_LV_WATCH_CONFIG_MAINMENU != 0
			CWathcService_ReceiveFunctionList(pme, pdata);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_FUNCTION_LIST, seq);
			#endif
			break;
		case CMD_S2C_SET_SLEEP_TIME:
#if USE_LV_WATCH_DORMAT_TIME != 0
			WS_PRINTF("CMD_S2C_SET_SLEEP_TIME\n",);
			CWathcService_SetRedayMode(pme, pdata, cmd_info);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_SLEEP_TIME, seq);
#endif
			break;
#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
		case CMD_S2C_SET_GROUP_PERIOD:
			CWathcService_Set_Group_Period(pme,pdata);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_GROUP_PERIOD, seq);
			break;
#endif	
#if USE_LV_WATCH_HEBAO_CHUXING!=0
		case CMD_S2C_SET_HEBAO_START_APDU:
			int ret = CWathcService_Hebao_StartApdu(pdata);
			CWathcService_Hebao_StartApduRsp(pme,ret);
			break;
		case CMD_S2C_HEBAO_UP_APDU_RSP:
			CWathcService_Hebao_SetApdu(pdata);
			break;
		case CMD_S2C_GET_HEBAO_INFO:
			if(g_hebao_status == HEBAO_IDLE)
		    {
				MMI_Modem_Get_Msisdn_Req(MMI_ModemAdp_Get_UI_SimId(RIL_SIM_1));
				uos_sleep(5);
				MMI_ModemAdp_Send_AT_Handler("AT*IMSLOCURI?@*IMSLOCURI",CWathcService_GetPhoneNum_Callback);
				CWatchService_Get_Seid();
				uos_sleep(20);
	            CWatchService_Get_Hebao_Balance_CardNo();
			}
			CWatchService_SendHebaoInfo(pme);

			break;
		case CMD_S2C_GET_HEBAO_ORDER_LIST:
			if(g_hebao_status == HEBAO_IDLE)
			{
			    CWatchService_Get_Record(GET_RECORD_MULTIPLE);
			}
			break;
#endif
		case CMD_S2C_SET_STUDENT_INFO:
            #if USE_LV_WATCH_STUDENT_INFO != 0
			CWathcService_ReceiveStuInfo(pme, pdata);
            #endif
            CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_STUDENT_INFO, seq);
		break;
		case CMD_S2C_SET_MIGU_URL:
			#if USE_LV_WATCH_MIGU != 0
			CWathcService_ReceiveMGurlInfo(pme, pdata);
			#endif
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_MIGU_URL, seq);
			break;
		case CMD_S2C_SET_MIGU_PLAN:
			#if USE_LV_WATCH_MIGU != 0
			CWathcService_SetMGPlan(pme, pdata);
			#endif
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_MIGU_PLAN, seq);
			break;
		case CMD_S2C_SET_CONFIG:
			CWathcService_ReceiveKeConfig(pme, pdata);
			CWathcService_KeConfig_Rsp(pme);
			break;
		case CMD_S2C_SET_SPORT_PLAN:
		#if USE_LV_WATCH_SMART_JUMPING_KR != 0 || USE_LV_WATCH_RUNNING_SPORT_KR != 0
			CWathcService_SetSportPlan(pme, pdata);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_SPORT_PLAN, seq);
		#endif
		break;
		case CMD_S2C_SET_BLACK_MAC:
		#if USE_LV_WATCH_LOCATION_BLEMAC != 0
			CWathcService_SetBlacklist_BleMac(pme, pdata);
		#endif
		break;
			case CMD_S2C_SET_SPORT_MAC:
		#if USE_LV_WATCH_SMART_JUMPING_KR != 0
			CWathcService_SetSportMAC(pme, pdata);
			CWatchService_SendSimpleCmd(pme, KAER_CMD_SET_BLE_MAC, seq);
		#endif
			break;
		case CMD_S2C_REC_RUN_DATA:
		#if USE_LV_WATCH_RUNNING_SPORT_KR != 0
			CWathcService_ReceiveRunningData(pme, pdata);
		#endif
			break;
#if USE_LV_WATCH_KR_WEILIAO != 0 
		case CMD_S2C_GET_USR_LOGIN_RSP:
			CWathcService_Set_user_login(pme,pdata);
			//CWatchService_sGetContactList_Init();
			//CWatchService_school_GetContactList(pme);
			break;
		case CMD_S2C_GET_CONTACT_LIST:
			CWathcService_parse_Get_contact_list(pme,pdata);
			break;
		case CMD_S2C_JCOM_HOME_SCHOOL:
			if(seq==CMD_GET_FRIENDS_FIND)
			{
			#if USE_LV_WATCH_DEV_CODE_ADD_FRIEND != 0
				strcpy(userData->dev_code,pdata);
			#else
				strcpy(userData->phone,pdata);
			#endif
				free(pdata);
			}
			else if(seq==CMD_REPORT_SESSION_ACTION_DEL_CONTACT)
			{
				strcpy(userData->toDeluserID,pdata);
			}
			else if(seq==CMD_REPORT_SESSION_ACTION_CMD_CLICK)
			{
				char* pCheck=(char*)pdata;
				if(pCheck[0]=='1')
					userData->add_friend_ok = 1;
				else
					userData->add_friend_ok = 0;
				seq = CMD_ADD_FRIENDS_RESULT;
			}
			else if(seq==CMD_REPORT_SESSION_ACTION_OPEN 
				|| seq==CMD_REPORT_SESSION_ACTION_MESSAGE_READ 
				|| seq==CMD_REPORT_SESSION_ACTION_DEL_SESSION
				|| seq==CMD_REPORT_SESSION_ACTION_CLOSE
			)
			{
				break;
			}
			else if(seq==CMD_GET_SESSION_CONTENT)
			{
				 char* pChecks=(char*)pdata;
				 char *sptr = NULL;
				 sptr = strstr(pChecks,",");
				 if(sptr!=NULL)
				 {
					*sptr = 0;
					sptr++;
				 }
				 memset(userData->sessionId,0,sizeof(userData->sessionId));
				 strcpy(userData->sessionId,pChecks);//sessionId=选定人的userid
				 memset(userData->sessionType,0,sizeof(userData->sessionType));
				 strcpy(userData->sessionType,sptr);
				 free(pdata);
			}
			CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)seq);
			break;
		case CMD_S2C_GET_SESSION_LIST:
			CWathcService_parse_Get_session_list(pme,pdata);
			break;
		case CMD_MAKE_FRIEND_REQ:
			CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_GET_FRIENDS_NEARBY);
			break;
		case CMD_S2C_GET_FRIEND_NEARBY:
			CWathcService_parse_Get_friend_nearby(pme,pdata);
			break;
		case CMD_S2C_GET_ADD_FRIEND:
			//设备 申请添加好友回复
			CWathcService_parse_Request_addfriend_response(pme,pdata);
			break;
		case CMD_S2C_SET_CHAT_INFO:
			CWathcService_parse_Plat_Download(pme,pdata);
			break;
		case CMD_S2C_GET_FRIEND_FIND:
			CWathcService_parse_Get_friend_find(pme,pdata);
			break;
		case CMD_S2C_DEL_FRIEND:
			//ɾ�����Ѻ���ȡ
			g_need_Save_friend_nv = 1;
			CwatchService_Get_Contactlist();
			break;
		case CMD_S2C_GET_SESSION_CONTENT:
			if(g_clear_cur_seesion_unread == 1)
			{
				g_clear_cur_seesion_unread = 0;
				break;
			}
			CWathcService_parse_Get_session_content(pme,pdata);
			break;
		case CMD_S2C_GET_FRIEND_DEV_CODE:
			CWathcService_parse_Get_Dev_code(pme,pdata);
			break;
		case CMD_SEND_WECHAT_MSG_EMOJI:
			ws_mo_msg *sdat = (ws_mo_msg *)pdata;
			userData->emo_id = sdat->content.emoji_msg.emoji_list[0];
			userData->up_type = WATCH_VOICE_MSG_TYPE_EMOJI_LIST;
			if(sdat->content.emoji_msg.emoji_list)
			{
				ws_printf("CMD_SEND_WECHAT_MSG_EMOJI reeee=%d",userData->emo_id);
				free(sdat->content.emoji_msg.emoji_list);
			}
			CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_REPORT_SESSION_ACTION_ADDCONTENT);
			break;
#endif
#if USE_LV_WATCH_SPAIN != 0
		case CMD_S2C_SET_TIMEZONE_FORMAT:
            CWathcService_SetTimeZoneAndFormat(pme, pdata);
			g_need_update_bak_flag = 1;
            break;
		case CMD_REQ_SET_PLATFORM_ALARM:
			cmd_info->evt = CMD_S2C_SETPARAM_ALARM_LIST;
			uint8_t set = CWatchService_HandleReminders(pme, pdata,&cmd_info->cmd.alarm_list);
			if(set == true){
				CWatchService_NofityClient(pme, cmd_info);
			}
			CWatchService_SendSimpleCmd(pme, KAER_CMD_PLATFORM_ALARM, seq);//暂时用平台闹钟的，等平台支持了，再改
			//TODO: 消息提醒
			break;
		case CMD_S2C_SET_FALL_DETECTION:
			CWathcService_SetFallDetection(pme, pdata);
			break;
        case CMD_S2C_SHOW_MESSAGE:
            CWatchService_SetMessage(pme, pdata);
            break;
        case CMD_S2C_START_MEASURE:
            CWathcService_StartMeasure(pme, pdata);
            break;
		case CMD_SET_LOCATION_CONFIG:
			CWatchService_SetLocationConfig(pme, pdata);
			break;
		case CMD_KAER_SET_HEALTH_FREQ:
			CWatchService_SetMeasureFreq(pme, pdata);
			break;
		case CMD_KAER_CMD_ECO_OPEN:
			CWathcService_ECO_open(pme, pdata);
#endif								
    	default:	
            break; /*涓嶉渶瑕佸鐞嗗～鍏呭弬鏁扮殑鍛戒护鐩存帴put鍒伴槦鍒椾腑*/
	}

    free(cmd_info);

}

static void CWatchServiceParseJson(ws_client *client, ws_raw_t *json_dataEx) 
{
	WS_PRINTF("_____________CWatchServiceParseJson json_dataEx->pData = %s",json_dataEx->pData);
    ws_cJSON *pMsgtype = NULL;	
    ws_cJSON *pMsgCmd = NULL;
    ws_evt_id_e cmd = WS_SERVICE_EVENT_MAX;

    ws_cJSON *root = ws_cJSON_Parse(json_dataEx->pData);

    if (root) {
        double seq = 0; 
        ws_cJSON *pSeq = NULL;

        do{
			cmd = CWatchService_GetCmdid(json_dataEx->nCmdType);
			if(WS_SERVICE_EVENT_MAX == cmd)
			{
				WS_PRINTF("CWatchServiceParseJson >> cmd error!", 0, 0, 0);
				break ;
			}    

			hearttick_expire_cnt = 0;
			#if USE_LV_WATCH_LOCK_PHONE != 0
		 	if((check_iccid_is_same()==0)&&(cmd!=CMD_S2C_DEV_LOGIN_RSP)&&(cmd!=CMD_C2S_PONG_RSP)&&(cmd!=CMD_S2C_SET_CONFIG))
			{
				WS_PRINTF("CWatchServiceParseJson >> check_iccid_is_same=0++++++++++++++++");
				break;
			}
			#endif
            CWatchService_CmdProcess(client, cmd, (void*)root, seq);            

			if (!get_eco_enable())
			{
				CWatchService_HeartbeatStart(client);		//ECO模式下不周期上报心跳，只在连接上时上报一次
			}   
			
            ws_cmd_wait_cb *cwcb = (ws_cmd_wait_cb*)client->adapter->cmd_wait_cb;
            if(cwcb)
            {
                if(cwcb->cb && cwcb->index == seq && cwcb->Cmd == cmd)
                {
                    ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));  /*命令数据结构*/
                    
                    memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
                    cmd_info->seq = seq;
                    cmd_info->client = client;
                    cmd_info->evt = CMD_C2S_CMD_RSP_CALLBACK;
                    memcpy(&cmd_info->cmd.cmd_callback, cwcb, sizeof(ws_cmd_wait_cb));
                    CWatchService_NofityClient(client, cmd_info);
                    free(cmd_info);
                    
                    client->adapter->cmd_wait_cb = NULL;
                }
            }
        }while(0);
        
        ws_cJSON_Delete(root);
    }
	else 
		WS_PRINTF("CWatchServiceParseJson error", 0, 0, 0);
    return ;
}

static void CWatchService_SetUrlDirect(char *Data)
{
	if(Data == NULL)
	{
		return;
	}
	char *Dat = Data;
	char UrlPort[100] = {0};
	strcpy(UrlPort, Dat);
		
	char *Index = strstr(UrlPort, "@");
	if(Index != NULL)
	{
		*Index = 0;
		Index++;
		int Port = atoi(Index);
		if(Port > 0 && Port < 0xffff)
		{
			memset(sUrl,0,sizeof(sUrl));
			strcpy(sUrl, UrlPort);
		
			server.port = Port;
			WS_PRINTF("CWatchService_SetUrl() myNVMgr_SetWsUrlPort Data is %s", Data);
			myNVMgr_SetWsUrlPort(Data);
		}
	}
	WS_PRINTF("%s server.host IS %s, server.port is %d",__FUNCTION__, server.host, server.port);
}

#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
//{"res":{"result":1,"proaddr":"**************:14002","fileaddr":"**************:14003","fileurl":"http://**************:8014/cp","time":"20220426174049"}} 
void CWatchService_HandleRegisteData(ws_client *client,char *pData, uint16_t len)
{
	ws_cJSON *pMsgtype = NULL;	
	char sbuf[100]={0};
	uint8_t slen = 0;
    ws_cJSON *root = ws_cJSON_Parse(pData+6);
	WS_PRINTF("CWatchServiceParseJson >> CWatchService_HandleRegisteData=%s",(pData+6) );
	if(root)
	{
		pMsgtype = ws_cJSON_GetObjectItem(root, "res");
        if(NULL == pMsgtype)
		{
                WS_PRINTF("CWatchServiceParseJson >> dddjson no type!" );
				ws_cJSON_Delete(root);
               return;
        }
		char *taskId = ws_cJSON_GetObjectItem(pMsgtype,"proaddr")->valuestring;
		if(taskId==NULL)
		{
			WS_PRINTF("CWatchServiceParseJson >>taskId!null" );
		}
		else
		{
			memset(sbuf,0,sizeof(sbuf));
			strcpy(sbuf,taskId);
			WS_PRINTF("CWatchServiceParseJson >>taskId!-==%s",sbuf );
			char *sprt = sbuf;
			sprt = strchr(sprt,':');
			if(sprt !=NULL)
			{
				*sprt = '@';
			}
			if(strlen(sbuf)>0)
			{
				slen = 1;
			}
		}
		ws_cJSON_Delete(root);
		if(slen == 1)
		{			
			slen = 0;
			CWatchService_SetUrlDirect(sbuf);
			WS_PRINTF("CWatchServiceParseJson >>taskId2222==%s",sbuf );

			MMI_ModemAdp_WS_Stop_Ext();
			//start link 
			MMI_ModemAdp_WS_Start(client);
		}
	}
}
#endif
//CWatchService_HandleServiceData===[90]=@P,cmd=0,4050 
void CWatchService_HandleServiceData(ws_client *client, char *pData, uint32_t len, void *user_data)
{	
    uint16_t pos = 0;

	uint16_t nPkBfLen=0;
	
    ws_raw_t   raw_data ={0};
    ws_kaer_t *userData = (ws_kaer_t*)client->adapter->data;
	#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
	WS_PRINTF("CWatchService_HandleServiceData===[%d]=%s,cmd=%x,%x",len,pData,raw_data.nCmdType,GETUINT16(pData+0));
	if(CWatchService_GetProtoServerInfo() == 0)
	{
		CWatchService_HandleRegisteData(client,pData,len);
		return;
	}
	#endif
	WS_PRINTF("CWatchService_HandleServiceData===[%d]=%s,cmd=%x,%x",len,pData,raw_data.nCmdType,GETUINT16(pData+0));

	raw_data.nCmdType = GETUINT16(pData+WS_FIELD_PK_TYPE);
    raw_data.nLen = GETUINT16(pData+WS_FIELD_PK_LEN);
    raw_data.pData = pData+WS_FRIST_READ_LENGTH;

    pData[len] = 0;
	
	char *  pPkData=NULL;
	
	if(userData->pJsonPkData)
	{
		nPkBfLen = strlen(userData->pJsonPkData);
	}
	pPkData = (char *)malloc(nPkBfLen+raw_data.nLen+1);
	if(pPkData)
	{
		memcpy((char*)pPkData, (char*)userData->pJsonPkData, nPkBfLen);
		if(NULL != userData->pJsonPkData)
		{
			free(userData->pJsonPkData);
		}

		memcpy(pPkData+nPkBfLen, raw_data.pData, raw_data.nLen);
		raw_data.pData = pPkData;
		pPkData[nPkBfLen+raw_data.nLen] = 0;
		userData->pJsonPkData = pPkData;
	}

    WS_PRINTF("rcv:%d,pklen:%d,type:%04x :%s", len, raw_data.nLen, raw_data.nCmdType, raw_data.pData);
    
    CWatchServiceParseJson(client, &raw_data);

	if(NULL != userData->pJsonPkData)
	{
		free(userData->pJsonPkData);
		userData->pJsonPkData = NULL;
	}
    return ;

}

static uint16_t CWatchService_PacketData(ws_client *pme, char* jsonstr, char **jsonstrSend)
{
    uint16_t  len=0;
    char*   ForSend=NULL;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	uint32_t nIdx = KAER_IDENT;
	if(jsonstr)
	    len = strlen(jsonstr);
	else
		len = 0;
	
	ForSend = malloc(6+len);		
	if(ForSend)
	{
		ForSend[0] = 0x40;
		ForSend[1] = 0x50;
		ForSend[2] = (char)((0xff00 & (userData->type))>>8);
		ForSend[3] = (char)(0x00ff & (userData->type));
		ForSend[4] = (char)((0xff00&(len))>>8);		
		ForSend[5] = 0xff&(len);

		if(jsonstr)
			memcpy(ForSend+6, jsonstr, len);
		*jsonstrSend = ForSend;
		len += 6;
	}
	
	if(jsonstr)
		free(jsonstr);

    return len;
}

static void CWatchService_SendData(ws_client * pme,char *jsonstr, uint16_t json_len, char *pbainary_data, uint16_t nbainary_len)
{
    ws_client_msg_t *msg;
    char *psend = jsonstr;
    uint16_t len = json_len;

    if(NULL == jsonstr || json_len == 0){
        return;
    }

    if(MAX_SIO_BUFFER_LEN < len)
    {
        WS_PRINTF("CWatchService--CWatchService_SendData >> compose data is too length:%d", len, 0, 0);
        free(psend);
        return;
    }

    //WS_PRINTF("CWatchService--CWatchService_SendData >> :sending...");
    
    msg = (ws_client_msg_t *)malloc(sizeof(ws_client_msg_t));
    msg->msgID = MSG_WS_CLIENT_NET_WRITE_REQ;
    msg->param.buffer.ptr = jsonstr;
    msg->param.buffer.len = json_len;
    msg->rspTask = NULL;
    
    uos_send_msg((void *)(msg), pme->modem->task->nMailBoxId, UOS_SEND_MSG);

    return;   
}

static void CWatchService_SendJsonData(ws_client *pme, ws_cJSON* root)
{
    char *jsonstr = NULL;
	
	WS_PRINTF("CWatchService_SendJsonData root>> %d", root);
    if(root != NULL)
	{    	   
	    jsonstr = ws_cJSON_PrintUnformatted(root);
	    ws_cJSON_Delete(root);
    	WS_PRINTF("$$$$$$CWatchService_SendJsonData >> %s", jsonstr);
	}	
    
    char*  jsonstrSend = NULL;
    uint16_t len = CWatchService_PacketData(pme, jsonstr, &jsonstrSend);
    if(jsonstrSend)
    {
        CWatchService_SendData(pme,jsonstrSend,len,NULL,(uint16_t)0);
    }
}

uint32_t CWatchService_GetFrameHeadLen(ws_client *pme)
{
    return WS_FRIST_READ_LENGTH;
}

uint32_t CWatchService_GetFrameBodyLen(ws_client *pme, char *data)
{
    uint16_t big_bodyLen = 0;
   
    big_bodyLen = GETUINT16(data+WS_FIELD_PK_LEN);

    return big_bodyLen;
}

static void CWatchService_ReLogInTimerCb(uint32_t pUser)
{
    ws_client * pme = (ws_client*)pUser;    
	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));	/*鍛戒护鏁版嵁缁撴瀯*/
    memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
    cmd_info->client = pme;
    cmd_info->evt = CMD_C2S_DEV_LOGIN;
    CWatchService_NofityClient(pme, cmd_info);
    free(cmd_info);
}

static void CWatchService_ReLogInTimer(ws_client *pme)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

    WS_PRINTF("CWatchService_ReLogInTimer() tTime=60 \n");
    
	uos_timer_stop(pme->adapter->timer_beat);
	uos_timer_start(pme->adapter->timer_beat, 10*TICKES_IN_SECOND, 0, CWatchService_ReLogInTimerCb, (uint32_t)pme);
}

void CWatchService_FirstCmd(ws_client *pme)
{
    #if USE_LV_WATCH_LOCK_ICCID != 0 ||USE_LV_WATCH_KAER_SETTINGS !=0 
	if((ke_GetLockOperator_Type()!=0) || ke_GetLockIccidFlag()!=0)
	{	
		if(watch_modem_sim_present_check_req() != 1)
		{
			WS_PRINTF("ke lock_iccid or lock_operator,so stop net.");
			MMI_ModemAdp_WS_Stop_Ext();
			return;
		}
	}
    #endif
	
	static char FirstEnter = 0;
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, userData->m_base.ws_reg.sn);

    userData->m_base.ws_reg.sn[15] = 0;
    MMI_Modem_Query_Imsi_Req(MMI_MODEM_SIM_1, userData->m_base.ws_reg.imsi);
#if USE_LV_WATCH_DATI
	memset(g_imei,0,WS_SN_MAX_LEN);
	strcpy(g_imei,userData->m_base.ws_reg.sn);
#endif

    if(FirstEnter == 0)
	{
		#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
		CWathcService_GetGohome_WifiMac();
		CWathcService_init_GoScene_WifiMac(0);
		#endif
		CWatchService_CmdProcess(pme, CMD_C2S_DEV_LOGIN, (void*)&userData->m_base.ws_reg, 0);
		FirstEnter = 1;
	}
	else
	{
		#if USE_LV_WATCH_DELAY_POWEROFF_FOR_DORMAT != 0
		if(get_need_link_net_flag() >0)
		{
			WS_PRINTF("-------- CWatchService_FirstCmd again");
			CWatchService_CmdProcess(pme, CMD_C2S_DEV_LOGIN, (void*)&userData->m_base.ws_reg, 0);
			return;
		}
		#endif

		CWatchService_ReLogInTimer(pme);  
	}
}

void CWatchService_GetDeviceID(ws_client *pme, char* sn)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(userData->m_base.ws_reg.sn[0])
    {
        strcpy(sn, userData->m_base.ws_reg.sn);
    }
    else 
    {
        MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, sn);
    }
}

uint8_t CWatchService_UpdateUrl(char *url_port,uint16_t port)
{
	if(!strcmp(sUrl, url_port)&&server.port == port)
		return 0;
	memset(sUrl, 0, sizeof(sUrl));
	strcpy(sUrl, url_port);
	server.port = port;
	
	WS_PRINTF("%s server.host IS %s, server.port is %d",__FUNCTION__, server.host, server.port);
	return 1;
}

//0--kaer 1--other
uint8_t CWatchService_get_plat_type()
{
    uint8_t ret=0;
	
    if(ke_GetPlatUrlPortLen() != 0)
	{
		if((memcmp(sUrl,WS_URL,strlen(WS_URL)) == 0)
			#if USE_LV_WATCH_CONN_REGISTER_SERVER != 0
			|| (memcmp(sUrl,WS_REGISTER_SERVER,strlen(WS_REGISTER_SERVER)) == 0)
			#endif
		)
		;
		else
			ret=1;//all use kaer proto ,no kaer plat addr 
	}

    WS_PRINTF("CWatchService_get_plat_type= %d",ret);
	return ret;
}

ws_client* CWatchService_InitData(ws_client *pme, ws_adp_t	*adp)
{
    static ws_kaer_t userData_kaer;
	static char FirstEnter = 0;
	static char kaer_pro_flag = 0;
	uint32_t val = 0;
	
	if(pme == NULL){
			pme = ws_client_create(adp);
	}

    if(pme)
    {
        ws_kaer_t *userData = &userData_kaer;
		if(FirstEnter == 0)
        {
        memset(userData, 0, sizeof(ws_kaer_t));
        	FirstEnter = 1;
        	userData->prelogin = true;
        }
        userData->m_base.client = pme;
        userData->last_voicemsg_seq = -1;
		#if USE_LV_WATCH_SPAIN != 0
			userData->mGpsON = (char)myNVMgr_GetGpsEnable();
			val = myNVMgr_GetHealthFreq(WS_HD_HEAT_RATE);
			if(val != 0)
				userData->m_base.health_freq[WS_HD_HEAT_RATE] = val;
			else
				userData->m_base.health_freq[WS_HD_HEAT_RATE] = heart_rate_UP_TIMER;
			val = myNVMgr_GetHealthFreq(WS_HD_BLOOD_OXYGEN);
			if(val != 0)
				userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN] = val;
			else
				userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN] = SPO2_UP_TIMER;
			WS_PRINTF("CWatchService_InitData >> userData->m_base.health_freq[WS_HD_HEAT_RATE] = %d", userData->m_base.health_freq[WS_HD_HEAT_RATE]);
			WS_PRINTF("CWatchService_InitData >> userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN] = %d", userData->m_base.health_freq[WS_HD_BLOOD_OXYGEN]);
			
		#endif
        
		userData->m_base.ws_reg.sn[0] = 0;
		userData->m_base.ws_reg.imsi[0] = 0;
        userData->m_base.ws_reg.model = KAER_MODULES;
        userData->m_base.ws_reg.version = KAER_SW_VERION;
        #if USE_LV_WATCH_SPAIN!=0
		userData->m_base.ws_reg.heartTime =0;
		#else
 		userData->m_base.ws_reg.heartTime = myNVMgr_GetWsDevPingTime();
		#endif
        //WS_PRINTF("CWatchService_InitData >> ws_ping_time:%d", userData->ws_reg.heartTime);
        if(userData->m_base.ws_reg.heartTime == 0)
        {
            userData->m_base.ws_reg.heartTime = KAER_PING_MAX_TIMER;
            myNVMgr_SetWsDevPingTime(userData->m_base.ws_reg.heartTime);
        }
        
        userData->m_base.ws_reg.upPosTime = myNVMgr_GetWsDevPosUpTime();
		WS_PRINTF("CWatchService_InitData >> userData->m_base.ws_reg.upPosTime = %d", userData->m_base.ws_reg.upPosTime);
        if(userData->m_base.ws_reg.upPosTime == 0)
        {
            userData->m_base.ws_reg.upPosTime = KAER_LOC_UP_TIMER;
            myNVMgr_SetWsDevPosUpTime(userData->m_base.ws_reg.upPosTime);
        }
	#if 0//USE_CRANE_WATCH_GPS != 0
		GpsSetSleepTime(userData->m_base.ws_reg.upPosTime*1000*1000); 
	#endif
	#if USE_LV_WATCH_DORMAT_TIME != 0
		printf("myNVMgr_GetWsDormantTime!!!!!!")
		myNVMgr_GetWsDormantTime();
	#endif
		#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
		CWathcService_Group_Period_Init();
		#endif

		WS_PRINTF("$$$$$$$$$$========CWatchService_InitData >> ws_ping_time:%d, upPosTime=%d", userData->m_base.ws_reg.heartTime, userData->m_base.ws_reg.upPosTime);
		CWatchService_FromNvmGetUrl();
        pme->adapter->data = userData;
        pme->adapter->srv = &server;
        pme->adapter->srv->host = sUrl;
		WS_PRINTF("$$$$$$$$$$========CWatchService_InitData >> pme->adapter->srv->host=%s",pme->adapter->srv->host);

		ke_fota_init();
		if(kaer_pro_flag == 0)
		{
			kaer_pro_flag = 1;
			#if USE_LV_WATCH_SPAIN == 0
			if(CWatchService_get_plat_type()==1)
			#endif
			{
			    ke_tcs_init();//all use kaer proto ,no kaer plat addr ,support tcs
			}
		}
#if USE_LV_WATCH_STUDENT_INFO != 0
		CWatchService_nh_userInfo_init();
#endif
    }
	return pme;
}

void CWatchService_UploadPictureFile_Post(ws_client * pme, uint8_t *data, uint32_t len, char* id)
{      

}

#if USE_LV_WATCH_VOICE_MSG_YNYD != 1
void CWatchService_UploadVoiceFile_Post(ws_client * pme, uint8_t *data, uint32_t len, char* id)
{

}
#else

static int CWathcService_UploadFileCB(char * data, int len, int num, void *cbdata)
{
    WS_PRINTF("ynyd-CWathcService_UploadFileCB() len=%d, num=%d\n",  len, num);
    if(!data && !len && !num){
        return -1;
    }
	
    if(len==0){
        return -1;
    }
	int i=0;
    WS_PRINTF("ynyd-CWathcService_UploadFileCB() data=%s\n", data);
	ws_client * pme=MMI_ModemAdp_WS_GetClient();
	if(pme)
	{
	    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

		ws_cJSON *root = ws_cJSON_Parse(data);
		if(root)
		{
			ws_cJSON *code_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, "code");
			if(code_s->valueint==0)
			{
				WS_PRINTF("ynyd-CWathcService_UploadFileCB() data jason SUCCESS\n");

				ws_cJSON *pItem = ws_cJSON_GetObjectItem(root, "data");
				if(pItem)
				{
					ws_cJSON *groupItem =ws_cJSON_GetArrayItem(pItem,0);
					ws_cJSON *userids = ws_cJSON_GetObjectItem(groupItem, "fileName");
					ws_cJSON *fileUrl_s = ws_cJSON_GetObjectItem(groupItem, "fileSucc");
					if(fileUrl_s)
					{	
						memset(userData->up_voice_path,0,sizeof(userData->up_voice_path));
						snprintf(userData->up_voice_path,256,"%s",fileUrl_s->valuestring);
						userData->up_type = WATCH_VOICE_MSG_TYPE_VOICE;
							
						CWatchService_CmdProcess(pme,CMD_S2C_JCOM_HOME_SCHOOL, NULL,(double)CMD_REPORT_SESSION_ACTION_ADDCONTENT);
						#if USE_LV_WATCH_KR_WEILIAO != 0
						//�Լ���������һ����¼
						CWatchService_Organize_owner_voice_info(pme,WATCH_VOICE_MSG_TYPE_VOICE);
						#endif


					}
				}
				app_adaptor_voice_msg_send_cnf(WATCH_VOICE_MSG_RESULT_SUCCESS);
				
		
			}
			else
			{
				WS_PRINTF("ynyd-CWathcService_UploadFileCB() data jason fail\n");
				app_adaptor_voice_msg_send_cnf(WATCH_VOICE_MSG_RESULT_FAILURE);
			}
			
			ws_cJSON_Delete(root);
			
		}

	}
	
    return 0;
}

char* CWatchService_Build_UploadVoiceFile_PostData(ws_client * pme, uint8_t *data, uint32_t len, char* id, uint32_t *post_len)
{
	char* post_data=NULL;
	uint32_t temp_len=0;

	post_data = (char*) malloc(len+3000);
 	char Imei[15] = {0};
	MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, Imei);

	if(post_data)
	{
		char tmp_str[1024]={0};
		uint32_t str_len;

        snprintf(tmp_str, sizeof(tmp_str), NEW_LINE BOUNDARY_LINE
                 "Content-Disposition: form-data; name=\"type\""
                 NEW_LINE NEW_LINE
                 "%s" NEW_LINE, "msg");
        str_len = strlen(tmp_str);
        memcpy(post_data + temp_len, tmp_str, str_len);
        temp_len += str_len;

		memset(tmp_str,0,sizeof(tmp_str));
	    snprintf(tmp_str, sizeof(tmp_str), BOUNDARY_LINE
                 "Content-Disposition: form-data; name=\"identity\""
                 NEW_LINE NEW_LINE
                 "%s" NEW_LINE, Imei);
        str_len = strlen(tmp_str);
        memcpy(post_data + temp_len, tmp_str, str_len);
        temp_len += str_len;
	

		memset(tmp_str,0,sizeof(tmp_str));
	    snprintf(tmp_str, sizeof(tmp_str), BOUNDARY_LINE
                 "Content-Disposition: form-data; name=\"time\""
                 NEW_LINE NEW_LINE
                 "%d000" NEW_LINE, PMIC_RTC_GetTime_Count(0)-28800);
        str_len = strlen(tmp_str);
        memcpy(post_data + temp_len, tmp_str, str_len);
        temp_len += str_len;

		
		memset(tmp_str,0,sizeof(tmp_str));
        snprintf(tmp_str, sizeof(tmp_str), BOUNDARY_LINE
                 "Content-Disposition: form-data; name=\"file\"; filename=\"record.amr\""
                 NEW_LINE NEW_LINE);
        str_len = strlen(tmp_str);
        memcpy(post_data + temp_len, tmp_str, str_len);
        temp_len += str_len;

   
        memcpy(post_data + temp_len, data, len);
        temp_len += len;

    
		memset(tmp_str,0,sizeof(tmp_str));
		snprintf(tmp_str, sizeof(tmp_str), BOUNDARY_END);
        str_len = strlen(tmp_str);
        memcpy(post_data + temp_len, tmp_str, str_len);
        temp_len += str_len;

		
	}

	*post_len = temp_len;

	return post_data;
}
void CWatchService_UploadVoiceFile_Post(ws_client * pme, uint8_t *data, uint32_t len, char* id,uint32_t duration)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    void *http_req=NULL;
	char url[128+2];
	char Imei[WS_IMEI_MAX_LEN] = {0};
    uint32_t post_len=0;
    char *post_data=NULL;
	if(data==NULL)
	{
		WS_PRINTF("UploadVoiceFile_Post:-data is null ,return");
		return ;
	}
	
    watch_modem_get_imei_req(Imei);
	
	WS_PRINTF("CWatchService_UploadVoiceFile_Post imei:%s,id:%s,duration:%d", Imei,id,duration );


	userData->duration=duration;  /*max test*/
	//memset(userData->sessionId,0,sizeof(userData->sessionId));
	//sprintf(userData->sessionId,"%s",id);

	post_data = CWatchService_Build_UploadVoiceFile_PostData(pme, data, len, id, &post_len);

	
	WS_PRINTF("UploadVoiceFile_Post[%d]%s ",post_len,post_data );

    http_req = ws_client_http_req_msg_create_POST(UP_VOICE_FILE_URL, CWathcService_UploadFileCB, NULL, POST_HEADER_BOUNDARY, post_data, post_len, 3);
	
	free(post_data);

    MMI_ModemAdp_SendHttpReq(pme, http_req, WS_MODEMADP_CMD_HTTP_RSP);
    
}
#endif

uint8_t CWatchService_GetSettingOnline(ws_client *pme, uint8_t	type, void* value)
{
    return WS_SETTING_VALUE_INVALID;
}

#if USE_LV_WATCH_DORMAT_TIME != 0

static void CWathcService_SetRedayMode(ws_client * pme, void *json,ws_evt_msg_t  * pEvtMsg)
{
	ws_cJSON		   *root = (ws_cJSON *)json;	
	ws_cJSON *req_s = NULL;
	ws_cJSON *sleep_time = NULL;
	char Data[100] = {0};
		
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	if(NULL == root)
	{
		return;
	}

	req_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, STRING_REQ);
	if(!req_s)
	{
		return;
	}
		
	sleep_time = ws_cJSON_GetObjectItem((ws_cJSON*)req_s, "period");
	if(!sleep_time)
	{
		return;
	}
	strcpy(Data, sleep_time->valuestring);

	nv_watch_dormantime_t *dormant_time = malloc(sizeof(nv_watch_dormantime_t));
	
	WS_PRINTF("CWathcService_SetRedayMode() Data is %s", Data);
	dormant_time->status = 1;
	dormant_time->dormt_start_h = ((Data[0]-'0')*10 + (Data[1]-'0'))*60+((Data[2]-'0')*10 + (Data[3]-'0'));
	dormant_time->dormt_end_h = ((Data[4]-'0')*10 + (Data[5]-'0'))*60+((Data[6]-'0')*10 + (Data[7]-'0'));
	dormant_time->null_flag = 1;
	WS_PRINTF("CWathcService_SetDormantTime =%d,%d",dormant_time->dormt_start_h,dormant_time->dormt_end_h);
	myNVMgr_SetWsDormantTime(dormant_time);
	free(dormant_time);
}

#endif
#if USE_LV_WATCH_PULL_CONFIG != 0

static void Http_ContactsProcess(ws_client * pme, void *json,ws_evt_msg_t  * pEvtMsg )
{
    ws_cJSON     *root_parse = (ws_cJSON *)json;
    ws_cJSON        *type_s = NULL;
    ws_cJSON         *last_s = NULL;
    ws_cJSON    *phb_array_s = NULL;
    ws_cJSON          *phb_s = NULL;
    ws_cJSON         *name_s = NULL;
    ws_cJSON        *phone_s = NULL;
    ws_cJSON        *avatar_s = NULL;
    ws_cJSON        *pkId_s = NULL;
    ws_cJSON        *pkCount_s = NULL;	
    uint16_t          i,cnt = 0;
    ws_cJSON *list_s = NULL;
    ws_cJSON *typelist_s = NULL;
    ws_cJSON *request_s = NULL;
    ws_contact_list *pContact = &pEvtMsg->cmd.contatc_list;    
    ws_sos_list *sos_list = &pEvtMsg->cmd.sos_list;
	uint16_t snum = 0;

    WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
    if(NULL == root_parse || NULL == pEvtMsg)
    {
        return;
    }

	typelist_s = ws_cJSON_GetObjectItem(root_parse, "familyNumber");
	if(typelist_s)
	{
		cnt = ws_cJSON_GetArraySize(typelist_s);
		WS_PRINTF("%s:%d, cnt=%d", __FUNCTION__, __LINE__, cnt);
		
	    pEvtMsg->evt = CMD_S2C_SETPARAM_CONTACT_LIST;
	    pContact->index_start = 0;//pkId_s->valueint;
	    pContact->index_end = cnt;//pkCount_s->valueint;    
	   
	   // WS_PRINTF("KAERKernelC::ParsingPHBList(): pkId=%d, pkCount=%d, cnt:%d", pkId_s->valueint , pkCount_s->valueint, cnt);
	    
	    pContact->count = cnt;
	    pContact->max = WS_PHB_MAX;	   
	    pContact->contact = NULL;
	    if(cnt)
	    {
			uint8_t *FamilyNoSer =  WatchUtility_GetFamilyNoSer();
		    memset(FamilyNoSer, 0, 3);
	    	ws_cJSON    *familyNoSer_s = NULL;
			#if 1
			if(cnt >= 3)
				cnt = 3;
			#endif
	        pContact->contact = lv_mem_alloc(cnt*sizeof(ws_contact));
	        if(pContact->contact==NULL)
	        {
		        WS_PRINTF("contact alloc fail !!");
		        return;
	        }
			memset(pContact->contact, 0, cnt*sizeof(ws_contact));

	        for(i=0; i<cnt; i++)
	        {
		        phb_s = ws_cJSON_GetArrayItem(typelist_s, i);
		        name_s = ws_cJSON_GetObjectItem(phb_s, "name");
		        phone_s = ws_cJSON_GetObjectItem(phb_s, "number");
		        familyNoSer_s = ws_cJSON_GetObjectItem(phb_s, "serialnumber");
			
		        if((familyNoSer_s) && (i < 3) && (atoi(familyNoSer_s->valuestring) < 3) && (atoi(familyNoSer_s->valuestring) >= 0))
		        {
		        	WS_PRINTF("familyNoSer_s->valueint IS %d i is %d",atoi(familyNoSer_s->valuestring), i);
					pContact->contact[i].speed_dial_pos = atoi(familyNoSer_s->valuestring);
					pContact->speed_dial_has_pos = 1;
					FamilyNoSer[atoi(familyNoSer_s->valuestring)] = i + 1 ;
		        }else{
					pContact->contact[i].speed_dial_pos = 255;
		        }
		        
		        if(name_s)
		        {
		            strncpy(pContact->contact[i].name, name_s->valuestring, WS_CONTACT_NAME_MAX_LEN);
					WS_PRINTF("pContact->contact[i].name=====%s",pContact->contact[i].name);
		        }
		        if((phone_s)
				#if 1
					&&(strlen(phone_s->valuestring) > 0)
				#endif
				)
		        {
		            strncpy(pContact->contact[i].phone, phone_s->valuestring, WS_CONTACT_PHONE_MAX_LEN);
		        }
				
				#if 1
		        pContact->contact[i].avatar_id =9;
				#endif
	        }
			myNVMgr_SetFamilyNoSer(FamilyNoSer);
	    }  
	}
#if 1	
	request_s = ws_cJSON_GetObjectItem(root_parse, "sosNumber");
	if(request_s)
	{		
		cnt = ws_cJSON_GetArraySize(request_s);
		WS_PRINTF("%s:%d, ,cnt=%d", __FUNCTION__, __LINE__,cnt);
		
		pEvtMsg->evt = CMD_S2C_SETPARAM_SOS_LIST;
		sos_list->count = cnt;
		sos_list->single = 0xff;
		//WS_PRINTF("KAERKernelC::sos pkId=%d, pkCount=%d, cnt:%d", pkId_s->valueint , pkCount_s->valueint, cnt);
		for(i=0; i<cnt; i++)
		{
			phb_s = ws_cJSON_GetArrayItem(request_s, i);
			phone_s = ws_cJSON_GetObjectItem(phb_s, "number");
			if(phone_s )
			{
				memset(sos_list->sos[i],0,WS_CONTACT_PHONE_MAX_LEN+1);
				strcpy(sos_list->sos[i], phone_s->valuestring);
				//cplog_printf("\n KAERKernelC::sos[%d]=%s\n ", i,sos_list->sos[i]);
			}
		}
	}
#endif
	list_s = ws_cJSON_GetObjectItem(root_parse, "whiteNumber");
	if(list_s)
	{
		i = 0;
		cnt = ws_cJSON_GetArraySize(list_s);
		WS_PRINTF("%s:%d, cnt=%d", __FUNCTION__, __LINE__, cnt);
		pEvtMsg->evt = CMD_S2C_SETPARAM_WHITE_LIST;

		pContact->max = WS_PHB_MAX;    
		pContact->contact = NULL;
		if(cnt)
		{
			pContact->contact = lv_mem_alloc((cnt)*sizeof(ws_contact));
			if(pContact->contact==NULL)
	        {
		        WS_PRINTF("contact alloc fail !!");
		        return;
	        }
			memset(pContact->contact, 0, (cnt)*sizeof(ws_contact));

			WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
			WS_PRINTF("CWathcService_ContactsProcess()i IS  (%d) %s", i, list_s);
			while(i < cnt)
			{
				WS_PRINTF("CWathcService_ContactsProcess()  i is %d", i);
				phb_s = ws_cJSON_GetArrayItem(list_s, i);
				name_s = ws_cJSON_GetObjectItem(phb_s, "name");
				phone_s = ws_cJSON_GetObjectItem(phb_s, "number");
				WS_PRINTF("CWathcService_ContactsProcess()name_s->valuestring IS %s phone_s->valuestring (%s)",
				name_s->valuestring, phone_s->valuestring);
				WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
				if((name_s && phone_s)
				#if 1
					&& strlen(phone_s->valuestring) > 0
				#endif
				)
				{
					strncpy(pContact->contact[snum].name, name_s->valuestring, WS_CONTACT_NAME_MAX_LEN);
					strncpy(pContact->contact[snum].phone, phone_s->valuestring, WS_CONTACT_PHONE_MAX_LEN);
					pContact->contact[snum].avatar_id =10;
					#if 0
					if((pkCount_s->valueint > (pkId_s->valueint +1))
					&& (pkId_s->valueint == sWhiteCntCurrPac)) //
					{
						
						strncpy(TempWhiteContact[TempWhiteCnt].name, name_s->valuestring, WS_CONTACT_NAME_MAX_LEN);
						strncpy(TempWhiteContact[TempWhiteCnt++].phone, phone_s->valuestring, WS_CONTACT_PHONE_MAX_LEN);	
					}
					#endif
					WS_PRINTF("CWathcService_ContactsProcess snmu=%d,%s",snum,pContact->contact[snum].phone);
					snum++;
				}	
	 
				i++;
				 if(i >= (WS_PHB_MAX - 3))	 //鍘绘帀3涓翰鎯呭彿 鏈€澶氾紙WS_PHB_MAX - 3锟?涓櫧鍚嶅崟
				{
					break;
				}
			}
		}	 
		pContact->count = snum;
	}
	WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);

}

//{"type":"terminal","command":"hiding","request":{"swit":"1","list":[{"timeList":[],"week":"0"},{"timeList":[],"week":"1"},{"timeList":[],"week":"2"},{"timeList":[{"endTime":"1800","begTime":"1626"},{"endTime":"2300 
//{"type":"terminal","command":"hiding","request":{"swit":1,  "list":[{"week":1,"timeList":[{"begTime":"0500","endTime":"2003"},{"begTime":"0801","endTime":"1500"}]},{"week":2,"timeList":[{"begTime":"1002","en 
static void Http_GetMuteList(ws_client * pme, void *json,ws_mute_list *pMute)
{
    ws_cJSON           *root = (ws_cJSON *)json;	
    ws_cJSON *req_s = NULL;
    ws_cJSON *swit_s = NULL;
    ws_cJSON *list_s = NULL;	
    ws_cJSON *week_s = NULL;	
    ws_cJSON *timeList_s = NULL;	
    ws_cJSON *endTime_s = NULL;	
    ws_cJSON *begTime_s = NULL;
    uint16_t          i,cnt = 0;
    uint16_t          j,nTimCnt = 0;	
	uint16_t		  nPos = 0;
	int sk = 0;
	uint8_t  w=0;
	const uint8_t wrp[7] = {0,1,2,3,4,5,6};
	char    sBegTime[10]={0};				
	char    sEndTime[10]={0};
	char week_d[91] ={0};
	
    ws_cJSON *disturb_array_s = NULL;
	WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
	//root=ws_cJSON_Parse("{\"req\":{\"swit\":1,\"list\":[{\"timeList\":[{\"name\":\"1000-1200\",\"endtime\":\"1200\",\"begtime\":\"1000\",\"idx\":\"1\"},{\"name\":\"1300-1700\",\"endtime\":\"1700\",\"begtime\":\"1300\",\"idx\":\"2\"}],\"week\":\"8\"}],\"taskId\":\"2470fc0d4d94429c98188dd15bf34ce0\"}}");
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(NULL == root || NULL == pMute)
    {
        return;
    }
	
	userData->type = KAER_CMD_STUDY_TIME;

    memset(pMute, 0, sizeof(ws_mute_list));

	swit_s = ws_cJSON_GetObjectItem((ws_cJSON*)root, "swit");
	if(!swit_s)
    {
    	//return;
    }
	list_s = ws_cJSON_GetObjectItem(root , "timeList");
    if(!list_s)
    {
    	return;
    }
	cnt = ws_cJSON_GetArraySize(list_s);
	WS_PRINTF("%s:%d cnt====%d\n", __FUNCTION__, __LINE__, cnt);
	for(i=0; i<cnt; i++)
	{
		week_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, i) , "sweek");	
		
		week_d[i]=atoi(week_s->valuestring);
		
		WS_PRINTF("%s:%d, week_d[%d]==%d", __FUNCTION__, __LINE__,i,week_d[i]);
	}
	pMute ->count = cnt ;
	if(cnt)
	{
		char WatchUtilityLimited = WatchUtility_GetCallinLimited();
		int Limited = 0; 
		Limited = 0;//sos call in
		Limited	&= 0x01;
		WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);

		WatchUtilityLimited &= ~((1 << 7) | (1 << 6)) ;  
		WatchUtilityLimited |= (Limited << 7);

		if(swit_s != NULL)
		{
			if(swit_s->valueint == 1)		
				Limited = 0; //sos禁止呼叫
			else if(swit_s->valueint == 3)		
				Limited = 1; //sos允许呼叫
			else
				Limited = 1;
		}

		WatchUtilityLimited |= (Limited << 6);     
				
		myNVMgr_SetCallinLimited(&WatchUtilityLimited);

		pMute->mutes = (ws_mute*)lv_mem_alloc(cnt*sizeof(ws_mute));
		memset(pMute->mutes, 0 , cnt*sizeof(ws_mute));
		for(j=0; j<cnt; j++)
		{	
			begTime_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, j) , "beginTime");
			endTime_s= ws_cJSON_GetObjectItem(ws_cJSON_GetArrayItem(list_s, j) , "endTime");
			if(begTime_s && endTime_s)
			{

				WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
				sprintf(sBegTime, "%s", begTime_s->valuestring);
				sprintf(sEndTime, "%s", endTime_s->valuestring);
				nPos =CWathcService_FindMuteListPos(pMute, sBegTime, sEndTime);
				if(nPos >= WS_MUTE_MAX_COUNT)
					break;

				pMute->mutes[nPos].id = nPos;
	
				if(swit_s != NULL)
					pMute->mutes[nPos].is_on = swit_s->valueint;
				else
					pMute->mutes[nPos].is_on = 1;
				
				w= week_d[j];
				pMute->mutes[nPos].repeat |= (1<<wrp[w]);
				
				pMute->mutes[nPos].repeat |= 0x80;
				strcpy(pMute->mutes[nPos].start, sBegTime);
				strcat(pMute->mutes[nPos].start, ":00");
				strcpy(pMute->mutes[nPos].end, sEndTime);
				strcat(pMute->mutes[nPos].end, ":00");
				WS_PRINTF("start %s, end :%s, nPos:%d, repeat:%x,w:%d,is_on:%d"
							, pMute->mutes[nPos].start, pMute->mutes[nPos].end, nPos, pMute->mutes[nPos].repeat, w, pMute->mutes[nPos].is_on);
			
			}
		}			
	}
	pMute ->count = CWathcService_GetMuteListCnt(pMute);
	WS_PRINTF("%s:%d", __FUNCTION__, __LINE__);
	WS_PRINTF("Mute nPos=%d, cnt=%d, count:%d", nPos , cnt, pMute ->count);
}

int * pull_config_httpcallback(char *responsedata)
{	
	ws_cJSON *data = NULL;
	ws_cJSON *root = NULL;
	ws_cJSON *codecs = NULL;

	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));	
    if(cmd_info == NULL)
    {
		WS_PRINTF("pull_config_httpcallback >> no memory!\n");
		Http_pull_config_timer_Create();
        return 0;
    }
    memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
    cmd_info->client = pme; 
	
	root = ws_cJSON_Parse(responsedata);
	codecs = ws_cJSON_GetObjectItem(root,"success");
	if(codecs!=NULL && NULL!= codecs->valuestring)
	{
		ws_printf("\n pull_config_httpcallback codecs=%s",codecs->valuestring);
		
		if(0 != strcmp(codecs->valuestring, "true"))
		{
		    Http_pull_config_timer_Create();
			free(cmd_info);
		    cmd_info = NULL;
			return 0;
		}
	}
	ws_printf("\n pull_config_httpcallback g_pull_config_type=%d",g_pull_config_type);

	if(g_pull_config_type == KAER_PULL_CONFIG_WHITENUM)
	{
		data = ws_cJSON_GetObjectItem(root, "whiteNumber");
		if(data !=NULL)
		{
			Http_ContactsProcess(pme, root, cmd_info);
			CWatchService_NofityClient(pme, cmd_info); 
		}
	}
	else if(g_pull_config_type == KAER_PULL_CONFIG_MUTE)
	{
		data = ws_cJSON_GetObjectItem(root, "timeList");
		if(data !=NULL)
		{
			cmd_info->evt = CMD_S2C_SETPARAM_SLIENT_PERIOD;
			Http_GetMuteList(pme, root, &cmd_info->cmd.mute_list);
			CWatchService_NofityClient(pme, cmd_info); 
		}
	}
	else if(g_pull_config_type == KAER_PULL_CONFIG_FAMILYNUM)
	{
		data = ws_cJSON_GetObjectItem(root, "familyNumber");
		if(data !=NULL)
		{
			Http_ContactsProcess(pme, root, cmd_info);
			CWatchService_NofityClient(pme, cmd_info); 
		}
	}
	else if(g_pull_config_type == KAER_PULL_CONFIG_SOS)
	{
		data = ws_cJSON_GetObjectItem(root, "sosNumber");
		if(data !=NULL)
		{
			Http_ContactsProcess(pme, root, cmd_info);
			CWatchService_NofityClient(pme, cmd_info); 
		}
	}
	
	if(cmd_info != NULL)
	{
		free(cmd_info);
		cmd_info = NULL;
	}

	Http_pull_config_timer_Create();
	
	return 200;
}

//uint8_t CWatchService_PullConfig(ws_client *pme, uint8_t	type)
uint8_t CWatchService_PullConfig(void)
{
    if(g_pull_config<KAER_PULL_CONFIG_WHITENUM || g_pull_config>KAER_PULL_CONFIG_SOS)
		return -1;
	
	char imei[20] = {0};
	char type_s[8] = {0};
	ws_cJSON *data =NULL;

	g_pull_config_type = g_pull_config;
	MMI_Modem_Query_Imei_Req(MMI_MODEM_SIM_1, imei);
	sprintf(type_s, "%d", g_pull_config_type);
	//ws_printf("CWatchService_Request_NewURL type=%d",type);
	data=ws_cJSON_CreateObject();

	ws_cJSON_AddStringToObject(data, "identity", imei);
	ws_cJSON_AddStringToObject(data, "type", type_s);
	
	char *data_js_to_string = ws_cJSON_PrintUnformatted(data);

	ws_printf("CWatchService_PullConfig send=%s",data_js_to_string);
	http_action_up(PULL_CONFIG_URL, data_js_to_string, pull_config_httpcallback); 

	ws_cJSON_Delete(data);
	free(data_js_to_string);

    if(g_pull_configTimer == NULL)
	{
	    WS_PRINTF("CWatchService_PullConfig create g_pull_configTimer");
		uos_timer_create(&g_pull_configTimer);
	}

	uos_timer_stop(g_pull_configTimer);
		
	uos_timer_start(g_pull_configTimer, 10*TICKES_IN_SECOND, 0, Http_pull_config_timer_Create, 0);
}

void Http_pull_config_timer_Stop()
{
	if(g_pull_configTimer!=NULL)
	{
		uos_timer_stop(g_pull_configTimer);
	}
}

void Http_pull_config_timer_update()
{
	Http_pull_config_timer_Stop();

	g_pull_config++;
	
	MMI_ModemAdp_Rpc_Req(CWatchService_PullConfig, 0, 0, 0);
}

void Http_pull_config_timer_Create()
{
    if(g_pull_configTimer == NULL)
	{
		uos_timer_create(&g_pull_configTimer);
	}
	
	uos_timer_stop(g_pull_configTimer);
	uos_timer_start(g_pull_configTimer, 1*TICKES_IN_SECOND, 0, Http_pull_config_timer_update, 0);
}

#endif
uint32_t CWatchService_GetUppos_Time()
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	return userData->m_base.ws_reg.upPosTime;
}

#if defined(__XF_PRO_DSHANG__)
uint8_t shutdown_delay_confirm_time()
{
	hal_rtc_t rtc_curr; 
	Hal_Rtc_Gettime(&rtc_curr);
	if(rtc_curr.tm_year >=2023 && (rtc_curr.tm_hour >= USE_LV_WATCH_WS_OFF_HOUR || rtc_curr.tm_hour <= USE_LV_WATCH_WS_ON_HOUR))
	{
		if(!MMI_ModemAdp_WS_Is_Online() && (WatchUtility_GetWsSwitch()->openCloseAlert) && (true == watch_modem_sim_present_check_req()))
			return 1;
	}
	return 0;
}
#endif
#if USE_LV_WATCH_KR_WEILIAO != 0

static void CWatchService_BodyBuild_School_JSONs(ws_client* pme,ws_cJSON  *root,unsigned long value)
{
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_cJSON *data = NULL;
	ws_cJSON *data_sub =NULL;
	ws_cJSON *req = NULL;
	char *base64 = NULL;
	int outlen = 0;
	
    char str_datetime[24] = "";
    // datetime
    hal_rtc_t time;
    Hal_Rtc_Gettime(&time);
    sprintf(str_datetime, "%04d-%02d-%02d %02d:%02d:%02d", time.tm_year, time.tm_mon, time.tm_mday, time.tm_hour, time.tm_min, time.tm_sec);
	
	WS_PRINTF("CWatchService_BodyBuild_School_JSON value:%d,userID:%s,jcom_token:%s", value,userData->userID,userData->jcom_token);

	userData->type = KAER_CMD_UP_WEILIAO;
	

	ws_cJSON_AddStringToObject(root,STRING_TYPE,STRING_TERMINAL);
	
	if(value==CMD_GET_USER_LOGININFO)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_GET_USER_LOGININFO);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);

	    ws_cJSON_AddStringToObject(req,"action",STR_GET_USER_LOGININFO);
		//ws_cJSON_AddStringToObject(req,"userID","");
	}
	else if(value==CMD_GET_CONTACT_LIST)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_GET_CONTACT_LIST);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action",STR_GET_CONTACT_LIST);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
		//ws_cJSON_AddStringToObject(req,"jcom_token",userData->jcom_token);
	}
	else if(value==CMD_GET_SESSION_LIST)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_GET_SESSION_LIST);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action",STR_GET_SESSION_LIST);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
		//ws_cJSON_AddStringToObject(req,"jcom_token",userData->jcom_token);
	}
	else if(value==CMD_GET_SESSION_CONTENT)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_GET_SESSION_CONTENT);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action",STR_GET_SESSION_CONTENT);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
	}
	else if(value==CMD_GET_FRIENDS_NEARBY)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_GET_FRIEND_NEARBY);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);

		ws_cJSON_AddStringToObject(req,"action",STR_GET_FRIEND_NEARBY);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
		//ws_cJSON_AddStringToObject(req,"jcom_token",userData->jcom_token);
	}
	else if(value==CMD_ADD_FRIENDS)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_ADD_FRIEND_REQ);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);

		ws_cJSON_AddStringToObject(req,"action",STR_ADD_FRIEND_REQ);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
	}
	else if(value==CMD_ADD_FRIENDS_RESULT)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,"result_add_friend");
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);

		ws_cJSON_AddStringToObject(req,"action","result_add_friend");
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
	}
	else if(value==CMD_GET_FRIENDS_FIND)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_GET_FRIEND_FIND);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action",STR_GET_FRIEND_FIND);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
		//ws_cJSON_AddStringToObject(req,"jcom_token",userData->jcom_token);
	}
	else if(value==CMD_REPORT_SESSION_ACTION_DEL_CONTACT)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,STR_DEL_FRIEND_REQ);
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action",STR_DEL_FRIEND_REQ);
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);
	}
	else if(value==CMD_REPORT_SESSION_ACTION_ADDCONTENT)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,"report_session_action_addContent");
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action","report_session_action_addContent");
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);

	}
	else if(value==CMD_GET_FRIENDS_DEVCODE)
	{
		ws_cJSON_AddStringToObject(root,STRING_COMMAND,"request_dev_code");
		req = ws_cJSON_CreateObject();
		ws_cJSON_AddItemToObject(root,STRING_REQ,req);
		ws_cJSON_AddStringToObject(req,"action","get_dev_code");
		ws_cJSON_AddStringToObject(req,"userID",userData->userID);

	}
	else if((value>CMD_REPORT_SESSION_ACTION_START)&&(value<CMD_REPORT_SESSION_ACTION_END))
	{
		ws_cJSON_AddStringToObject(root,"action",STR_REPORT_SESSION_ACTION);
		ws_cJSON_AddStringToObject(root,"userID",userData->userID);
		ws_cJSON_AddStringToObject(root,"jcom_token",userData->jcom_token);
	}

	/***data***/
	if(value==CMD_REPORT_SESSION_ACTION_DEL_CONTACT)
	{
		char data_str[64];
		ws_cJSON *m_item = NULL;
		ws_cJSON *m_array = NULL;
		m_array =  ws_cJSON_CreateArray();
		sprintf(data_str, "%s",userData->toDeluserID);
		m_item = ws_cJSON_CreateString(data_str);
		ws_cJSON_AddItemToArray(m_array, m_item);////"data":["1000000"]
		
		ws_cJSON_AddItemToObject(req, "data",m_array);

	}
	else if((value==CMD_GET_FRIENDS_NEARBY))//|| (value==CMD_GET_SESSION_LIST))
	{
		data =	ws_cJSON_CreateArray();
		ws_cJSON_AddItemToObject(req, "data",data);
	}
	else
	{

		data = ws_cJSON_CreateObject();
	    ws_cJSON_AddItemToObject(req,"data",data);
		//details
		if(value==CMD_GET_SESSION_CONTENT)
		{	
			uint8_t sbugs[10]={0};
			sprintf(sbugs,"%d",g_Session_content_page_num);
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
			ws_cJSON_AddStringToObject(data,"session_type",userData->sessionType);
			ws_cJSON_AddStringToObject(data,"pageNum",sbugs);
			memset(sbugs,0,sizeof(sbugs));
			sprintf(sbugs,"%d",SESSION_CONTENT_ONE_READ_COUNT);
			ws_cJSON_AddStringToObject(data,"pageSize",sbugs);
		}
		else if(value==CMD_GET_CONTACT_LIST)
		{
			uint8_t sbugs[10]={0};
			sprintf(sbugs,"%d",g_Contact_page_num);
			ws_cJSON_AddStringToObject(data,"pageNum",sbugs);
			
			memset(sbugs,0,sizeof(sbugs));
			sprintf(sbugs,"%d",CANTACT_ONE_READ_COUNT);
			ws_cJSON_AddStringToObject(data,"pageSize",sbugs);
		}
		else if(value==CMD_GET_SESSION_LIST)
		{
			uint8_t sbugs[10]={0};
			sprintf(sbugs,"%d",g_Session_page_num);
			ws_cJSON_AddStringToObject(data,"pageNum",sbugs);
			
			memset(sbugs,0,sizeof(sbugs));
			sprintf(sbugs,"%d",SESSION_LIST_ONE_READ_COUNT);
			ws_cJSON_AddStringToObject(data,"pageSize",sbugs);
		}
		else if(value==CMD_REPORT_SESSION_ACTION_DEL_SESSION)
		{//no use	
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
			ws_cJSON_AddStringToObject(data,"action",STR_ACTION_DELETE);
			//data_sub = ws_cJSON_CreateObject();
			data_sub =	ws_cJSON_CreateArray();
			ws_cJSON_AddItemToObject(data,"data",data_sub);
		}
		
		else if(value==CMD_REPORT_SESSION_ACTION_MESSAGE_READ)
		{	//no use
			char data_str[64];
			ws_cJSON *m_item = NULL;
			ws_cJSON *m_array = NULL;
			
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
			ws_cJSON_AddStringToObject(data,"action",STR_ACTION_MESSAGE_READ);
			data_sub = ws_cJSON_CreateObject();
			
			m_array = ws_cJSON_CreateArray();
            sprintf(data_str, "%s",userData->readID);
            m_item = ws_cJSON_CreateString(data_str);
            ws_cJSON_AddItemToArray(m_array, m_item);
			ws_cJSON_AddItemToObject(data_sub,"msg_id",m_array);
			
			ws_cJSON_AddItemToObject(data,"data",data_sub);

			
		}
		else if(value==CMD_REPORT_SESSION_ACTION_CMD_CLICK)
		{	//no use
			char data_str[64];
			ws_cJSON *m_item = NULL;
			ws_cJSON *m_array = NULL;
			/*
			{"code":0,"msg":"ok","action":"report_session_action","userID":"1000009","jcom_token":"e9f78220326a22e7c93ce12fd07b8a64",
			"data":[{"session_id":"s1000009_1000001","action":"clickCmd","data":{"msg_id":"fbc52cd5b828e6b364bcbc5f19bac204","cmd":"no"}}]} 
			*/
			m_item = ws_cJSON_CreateObject();
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
			ws_cJSON_AddStringToObject(data,"action",STR_ACTION_CMD_CLICK);
			data_sub = ws_cJSON_CreateObject();
			ws_cJSON_AddStringToObject(data_sub,"msg_id",userData->readID);
			ws_cJSON_AddStringToObject(data_sub,"cmd",userData->cmdValue);
			ws_cJSON_AddItemToObject(data,"data",data_sub);
		}

		else if(value==CMD_REPORT_SESSION_ACTION_ADDCONTENT)
		{	
			char time_str[5]={};
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
		    ws_cJSON_AddStringToObject(data,"session_type",userData->sessionType);

			if(userData->up_type == WATCH_VOICE_MSG_TYPE_EMOJI_LIST)
			{
				uint8_t sk = 0;
				int base64_len=0;
				uint8_t fflsag = 0;
				char base64_data[100]={0};
				memset(base64_data,0,sizeof(base64_data));
				ws_cJSON_AddStringToObject(data,"type","emo");
				for(sk=0;sk<sizeof(VoiceMsgEmoLIST)/sizeof(voice_msg_emoji_list);sk++)
				{
					if(userData->emo_id == VoiceMsgEmoLIST[sk].emoji_id)
					{
						fflsag = sk;
						break;
					}
				}
				//base64
				_base64_encode(VoiceMsgEmoLIST[fflsag].Bigfile,strlen(VoiceMsgEmoLIST[fflsag].Bigfile),&base64_len,base64_data);
				ws_printf("base64_data[%d]==sk=%s",base64_len,base64_data);
				
				ws_cJSON_AddStringToObject(data,"text",base64_data);
				ws_cJSON_AddStringToObject(data,"url","");
				ws_cJSON_AddStringToObject(data,"duration","");
			}
			else
			{
				ws_cJSON_AddStringToObject(data,"type","voice");
				ws_cJSON_AddStringToObject(data,"text","");
				ws_cJSON_AddStringToObject(data,"url",userData->up_voice_path);
				sprintf(time_str,"%d",userData->duration);
				ws_cJSON_AddStringToObject(data,"duration",time_str); 

			}
			ws_cJSON_AddStringToObject(data,"dateTime",str_datetime);
			
		
			
		}
		else if(value==CMD_REPORT_SESSION_ACTION_OPEN)
		{//no use	
			char time_str[5]={};
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
		    ws_cJSON_AddStringToObject(data,"action",STR_ACTION_OPEN);
			//data_sub = ws_cJSON_CreateObject();
			data_sub =	ws_cJSON_CreateArray();
			ws_cJSON_AddItemToObject(data,"data",data_sub);
		}
		else if(value==CMD_REPORT_SESSION_ACTION_CLOSE)
		{	//no use
			char time_str[5]={};
			ws_cJSON_AddStringToObject(data,"session_id",userData->sessionId);
		    ws_cJSON_AddStringToObject(data,"action",STR_ACTION_CLOSE);
			//data_sub = ws_cJSON_CreateObject();
			data_sub =	ws_cJSON_CreateArray();
			ws_cJSON_AddItemToObject(data,"data",data_sub);
		}
		else if(value==CMD_GET_FRIENDS_FIND)
		{	
			#if USE_LV_WATCH_DEV_CODE_ADD_FRIEND !=0
			ws_cJSON_AddStringToObject(data,"type","code");
			ws_cJSON_AddStringToObject(data,"value",userData->dev_code);
			#else
			ws_cJSON_AddStringToObject(data,"type","phone");
			ws_cJSON_AddStringToObject(data,"value",userData->phone);
			#endif
		}
		else if(value==CMD_ADD_FRIENDS)
		{	
			ws_cJSON_AddStringToObject(data,"userID",userData->add_userID);
			if(userData->is_nearby == 1)
				ws_cJSON_AddStringToObject(data,"fromType","nearby");
			else
				ws_cJSON_AddStringToObject(data,"fromType","find");
		}
		else if(value==CMD_ADD_FRIENDS_RESULT)
		{
			ws_cJSON_AddStringToObject(data,"userID",userData->add_userID);
			if(userData->is_nearby == 1)
				ws_cJSON_AddStringToObject(data,"fromType","nearby");
			else
				ws_cJSON_AddStringToObject(data,"fromType","find");
			if(userData->add_friend_ok == 1)
				ws_cJSON_AddStringToObject(data,"result","1");
			else
				ws_cJSON_AddStringToObject(data,"result","0");
		}
		
	}


}
void CWatchService_CmdSend(ws_client * pme, ws_evt_id_e cmd_id,unsigned long seq)
{
	ws_cJSON *root = NULL;
	root = ws_cJSON_CreateObject();
    if(NULL == root )
    {
        return;
    }
    CWatchService_BodyBuild_School_JSONs(pme,root,seq);

	CWatchService_SendJsonData(pme, root);
}

void CWatchService_school_GetUser_LoginInfo(ws_client *pme)
{
	/*get usernfo*/
	CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL,(unsigned long)CMD_GET_USER_LOGININFO);
}

void CWatchService_school_GetFriend_devcode()
{
	ws_client* pme = MMI_ModemAdp_WS_GetClient();
	CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL,(unsigned long)CMD_GET_FRIENDS_DEVCODE);
}
void CWatchService_Get_Upfile_A_Login()
{
	uos_sleep(50);
	ws_client* pme = MMI_ModemAdp_WS_GetClient();
	//CWatchService_CmdSend(pme,CMD_S2C_JGET_FILEUPLOAD_PATH,0);
	//uos_sleep(50);
	//login
	CWatchService_school_GetUser_LoginInfo(pme);
}

void CWathcService_Set_user_login(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	int slen=0;
	 ws_cJSON *Result = NULL;
	  ws_cJSON *errmsg = NULL,*datmsg = NULL,*uids = NULL,*sjocken = NULL,*usname=NULL;
	 ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	 pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);
		}
		return;
	}
	datmsg = ws_cJSON_GetObjectItem(pRsp, "data");
	if(datmsg==NULL)
		return;
	//userid
	uids = ws_cJSON_GetObjectItem(datmsg, "userID");
	if(uids==NULL)
		return;
	memset(userData->userID,0,sizeof(userData->userID));
	strcpy(userData->userID,uids->valuestring);
	ws_printf("CWathcService_Set_user_login~~~~ userid=%s",userData->userID);
	//joken
	sjocken = ws_cJSON_GetObjectItem(datmsg, "jcom_token");
	if(sjocken==NULL)
		return;
	memset(userData->jcom_token,0,sizeof(userData->jcom_token));
	strcpy(userData->jcom_token,sjocken->valuestring);
	//NAME
	usname = ws_cJSON_GetObjectItem(datmsg, "name");
	memset(userData->username,0,sizeof(userData->username));
	slen = strlen(usname->valuestring);
	if(slen >= sizeof(userData->username))
		slen = sizeof(userData->username)-3;
	
	ws_printf("CWathcService_Set_user_login~~~~ jcom_token=%s",userData->jcom_token);

	ws_cJSON *phosomsg  = ws_cJSON_GetObjectItem(datmsg, "photo");
	userData->own_id = WATCH_PORTRAIT_ID_OWNER_BOY;
	if(phosomsg && strlen(phosomsg->valuestring) > 0)
	{
		int Index[10] =  {0};
		slen = strlen(phosomsg->valuestring);
		char *Datar = phosomsg->valuestring+9;
		int Cnt = split_string(Datar, '/', NULL, Index, 10);
		if(Cnt < 1)
			Cnt = 1;
		char *Dat = Datar + Index[Cnt-1];
		char *sptrs = strstr(Dat,".");
		if(sptrs)
			*sptrs = 0;
		uint16_t svalues = atoi(Dat);
		userData->own_id =svalues;
		ws_printf("CWathcService_Set_user_login~~~~ phoeot=%d",userData->own_id);
	}
	else if(phosomsg)
	{
		//��ǰ�ĵ�¼��û��ͷ��
		userData->own_id = WATCH_PORTRAIT_ID_NO_INFO;
	}
	
}

uint8_t Get_Owner_HeadImage()
{
	 ws_client* pme = MMI_ModemAdp_WS_GetClient();
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	return userData->own_id;
}

void CWatchService_sGetContactList_Init()
{
	g_Contact_page_num = 1;
	g_Contact_total = 0;
	g_Contact_page_total = 0;
	g_Contact_one_num = 0;
	memset(gYnContacts,0,sizeof(gYnContacts));
}

void CWatchService_school_GetContactList(ws_client *pme)
{
	CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_GET_CONTACT_LIST);
}

void CwatchService_Get_Contactlist()
{
	ws_client* pme = MMI_ModemAdp_WS_GetClient();
	CWatchService_sGetContactList_Init();
	CWatchService_school_GetContactList(pme);
}

uint8_t CwatchService_Has_Contactlist()
{
	uint8_t i = 0;
	for(i=0;i<3;i++)
	{
		if(strlen(gYnContacts[i].sessionId) > 0)
			return 1;
	}
	return 0;
}

void CWathcService_parse_Get_contact_list(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	int slen=0,i=0,j=g_Contact_one_num;
	uint16_t totalcnt = 0;
	int content_len = 0;
	 ws_cJSON *Result = NULL;
	  ws_cJSON *errmsg = NULL,*datmsg = NULL,*pItem = NULL,*sjocken = NULL,*groupitem=NULL,*SERVitem=NULL;
	 ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	 pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);
		}
		return;
	}
	datmsg = ws_cJSON_GetObjectItem(pRsp, "data");
	if(datmsg==NULL)
		return;

	ws_cJSON *totalspItem = ws_cJSON_GetObjectItem(datmsg, "personCount");

	ws_printf("get_MQTT_Info_Callback  personCount=%d,g_Contact_page_total=%d", totalspItem->valueint,g_Contact_page_total);
	if(totalspItem)
	{
		if(totalspItem->valueint >= NV_WATCH_MAX_FRIENDS_NUM)
			g_Contact_total = NV_WATCH_MAX_FRIENDS_NUM;
		else
			g_Contact_total = totalspItem->valueint;
	}
	else
	{
		g_Contact_total = NV_WATCH_MAX_FRIENDS_NUM;
	}	
	

	if(g_Contact_page_total == 0)
	{
		if((g_Contact_total%CANTACT_ONE_READ_COUNT) == 0)
		{
			g_Contact_page_total = g_Contact_total/CANTACT_ONE_READ_COUNT;
		}
		else
		{
			g_Contact_page_total = (g_Contact_total/CANTACT_ONE_READ_COUNT) + 1;
		}
	}
	
	ws_printf("get_MQTT_Info_Callback  g_Contact_total=%d,g_Contact_page=%d,g_Contact_page_total=%d", g_Contact_total,g_Contact_page_num,g_Contact_page_total);
	pItem = ws_cJSON_GetObjectItem(datmsg, "person");
	if(pItem == NULL)
	{
		ws_printf("get_MQTT_Info_Callback person nulllllllllll ");
		voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_CONTACT_LIST,NULL,0,0,NULL,NULL,NULL,NULL);
		return;
	}
	else
	{
		 content_len = ws_cJSON_GetArraySize(pItem);
		ws_printf("get_MQTT_Info_Callback content_len=%d ,g_Contact_total=%d",content_len,g_Contact_total );
		if(content_len > NV_WATCH_MAX_FRIENDS_NUM)
			content_len = NV_WATCH_MAX_FRIENDS_NUM;
		
		for (i = 0; i < content_len; i++)
        {
         	sjocken = ws_cJSON_GetArrayItem(pItem, i);
			ws_cJSON *userids = ws_cJSON_GetObjectItem(sjocken, "userID");
			ws_cJSON *uNAMEs = ws_cJSON_GetObjectItem(sjocken, "name");
			ws_cJSON *usessions = ws_cJSON_GetObjectItem(sjocken, "session_id");
			ws_cJSON *phoeoions = ws_cJSON_GetObjectItem(sjocken, "photo");
			
			if(userids && uNAMEs && usessions)
			{
				gYnContacts[j].index = i;///index ��voice_msg_fill_chat_id(), <������ , WATCH_VOICE_MSG_SINGLE_CHAT  ��֮ ������(WATCH_VOICE_MSG_FAMILY_GROUP_CHAT 
				slen = strlen(uNAMEs->valuestring);
				if(slen >= sizeof(gYnContacts[j].name) )
					slen = sizeof(gYnContacts[j].name)-1;
				memcpy(gYnContacts[j].name,uNAMEs->valuestring,slen);
				
				slen = strlen(userids->valuestring);
				if(slen >= sizeof(gYnContacts[j].userId) )
					slen = sizeof(gYnContacts[j].userId)-1;
				memcpy(gYnContacts[j].userId,userids->valuestring,slen);
				
				slen = strlen(usessions->valuestring);
				if(slen >= sizeof(gYnContacts[j].sessionId) )
					slen = sizeof(gYnContacts[j].sessionId)-1;
				memcpy(gYnContacts[j].sessionId,usessions->valuestring,slen);
				memcpy(gYnContacts[j].imei,usessions->valuestring,slen);
				gYnContacts[j].type = 1;
				gYnContacts[j].portrait_id = 0;
				if(phoeoions && strlen(phoeoions->valuestring) > 0)
				{
					int Index[10] =  {0};
					char *Datar = phoeoions->valuestring+9;
					int Cnt = split_string(Datar, '/', NULL, Index, 10);
					if(Cnt < 1)
						Cnt = 1;
					char *Dat = Datar + Index[Cnt-1];
					char *sptrs = strstr(Dat,".");
					if(sptrs)
						*sptrs = 0;
					uint16_t svalues = atoi(Dat);
					ws_printf("get conttttttttttttcnt =%d,svalues=%d",Cnt,svalues);
					gYnContacts[j].portrait_id =svalues;
				}
				else if(phoeoions)
				{
					//����������ͷ��
					char tmp_pre[32] = {0};
 					for(int mr = 0;mr<sizeof(VoicemsgImageList)/sizeof(voice_msg_image_list);mr++)
					{
						memset(tmp_pre,0,sizeof(tmp_pre));
						HexToAscii(VoicemsgImageList[mr].file, tmp_pre); 
						if(strcmp(tmp_pre,uNAMEs->valuestring) == 0)
						{
							gYnContacts[j].portrait_id = VoicemsgImageList[mr].img_id;
							break;
						}
					}
 				}
				
				j++;
			}
        }
	}

//好友存nv
	if(g_need_Save_friend_nv == 1)
	{
		g_need_Save_friend_nv = 0;
		#if 0
		ws_friends_list sfriends_list;
		sfriends_list.cnt = j;
		if(j >0)
		{
			sfriends_list.friends = lv_mem_alloc(j * sizeof(ws_friend));
			memset(sfriends_list.friends, 0, (j * sizeof(ws_friend)));
			for(i = 0;i<j;i++)
			{
				memset(sfriends_list.friends[i].name,0,sizeof(sfriends_list.friends[i].name));
				slen = strlen(gYnContacts[i].name);
				if(slen >= sizeof(sfriends_list.friends[i].name) )
					slen = sizeof(sfriends_list.friends[i].name)-1;
				memcpy(sfriends_list.friends[i].name,gYnContacts[i].name,slen);

				slen = strlen(gYnContacts[i].userId);
				memset(sfriends_list.friends[i].imei,0,sizeof(sfriends_list.friends[i].imei));
				if(slen >= sizeof(sfriends_list.friends[i].imei) )
					slen = sizeof(sfriends_list.friends[i].imei)-1;
				memcpy(sfriends_list.friends[i].imei,gYnContacts[i].userId,slen);

				memcpy(sfriends_list.friends[i].image,gYnContacts[i].photoId,strlen(gYnContacts[i].photoId));
				
			}
			WatchUtility_SetFriendsList(&sfriends_list);

		}
		else
		{
			 uint32_t length = sizeof(nv_watch_friends_t);
    		 nv_watch_friends_t * nvm_contact = (nv_watch_friends_t *)lv_mem_alloc(length);

			memset(nvm_contact, 0, length);
	
		    voice_msg_write_pb_nvm(nvm_contact);
			lv_mem_free(nvm_contact);

		}
		#endif
	}
	WS_PRINTF("CWathcService_Get_contact_list 111=%d",j);
	groupitem = ws_cJSON_GetObjectItem(datmsg, "group");
	if(groupitem)
	{
		content_len = ws_cJSON_GetArraySize(groupitem);
		ws_printf("get_MQTT_Info_Callback 222 content_len=%d",content_len);
		if(content_len > NV_WATCH_MAX_VOICE_MSG_GROUP_NUM )
			content_len = NV_WATCH_MAX_VOICE_MSG_GROUP_NUM;///VOICE_MSG_FAMILY_GROUP and VOICE_MSG_FRIENDS_GROUP
		for (i = 0; i < content_len; i++)
        {
         	sjocken = ws_cJSON_GetArrayItem(groupitem, i);
			ws_cJSON *userids = ws_cJSON_GetObjectItem(sjocken, "groupID");
			ws_cJSON *uNAMEs = ws_cJSON_GetObjectItem(sjocken, "name");
			ws_cJSON *usessions = ws_cJSON_GetObjectItem(sjocken, "session_id");
			if(userids && uNAMEs && usessions)
			{
				gYnContacts[j].index = VOICE_MSG_FAMILY_GROUP;
				slen = strlen(uNAMEs->valuestring);
				if(slen >= sizeof(gYnContacts[j].name) )
					slen = sizeof(gYnContacts[j].name)-1;
				memcpy(gYnContacts[j].name,uNAMEs->valuestring,slen);
				
				slen = strlen(userids->valuestring);
				if(slen >= sizeof(gYnContacts[j].userId) )
					slen = sizeof(gYnContacts[j].userId)-1;
				memcpy(gYnContacts[j].userId,userids->valuestring,slen);
				
				slen = strlen(usessions->valuestring);
				if(slen >= sizeof(gYnContacts[j].sessionId) )
					slen = sizeof(gYnContacts[j].sessionId)-1;
				memcpy(gYnContacts[j].sessionId,usessions->valuestring,slen);
				memcpy(gYnContacts[j].imei,usessions->valuestring,slen);
				gYnContacts[j].portrait_id =VOICE_MSG_PORTRAIT_ID_FAMILY_GROUP;//group
				gYnContacts[j].type = 1;
				j++;
			}
        }
	}
	WS_PRINTF("CWathcService_Get_contact_list 222=%d",j);
	SERVitem = ws_cJSON_GetObjectItem(datmsg, "service");
	if(SERVitem)
	{
		content_len = ws_cJSON_GetArraySize(SERVitem);
		ws_printf("get_MQTT_Info_Callback 333 content_len=%d",content_len);
		if(content_len > 1)
			content_len = 1;
		for (i = 0; i < content_len; i++)
        {
         	sjocken = ws_cJSON_GetArrayItem(SERVitem, i);
			ws_cJSON *userids = ws_cJSON_GetObjectItem(sjocken, "serviceID");
			ws_cJSON *uNAMEs = ws_cJSON_GetObjectItem(sjocken, "name");
			ws_cJSON *usessions = ws_cJSON_GetObjectItem(sjocken, "session_id");
			if(userids && uNAMEs && usessions)
			{
				gYnContacts[j].index = VOICE_MSG_FAMILY_GROUP-1;
				slen = strlen(uNAMEs->valuestring);
				if(slen >= sizeof(gYnContacts[j].name) )
					slen = sizeof(gYnContacts[j].name)-1;
				memcpy(gYnContacts[j].name,uNAMEs->valuestring,slen);
				slen = strlen(userids->valuestring);
				if(slen >= sizeof(gYnContacts[j].userId) )
					slen = sizeof(gYnContacts[j].userId)-1;
				memcpy(gYnContacts[j].userId,userids->valuestring,slen);
				
				slen = strlen(usessions->valuestring);
				if(slen >= sizeof(gYnContacts[j].sessionId) )
					slen = sizeof(gYnContacts[j].sessionId)-1;
				memcpy(gYnContacts[j].sessionId,usessions->valuestring,slen);
				memcpy(gYnContacts[j].imei,usessions->valuestring,slen);
				gYnContacts[j].portrait_id =WATCH_PORTRAIT_ID_NO_INFO;//service
				gYnContacts[j].type = 1;
				j++;
			}
        }
	}

	g_Contact_one_num = j;
	WS_PRINTF("CWathcService_Get_contact_list 333=%d,g_Contact_page_num=%d",j,g_Contact_page_num);
	if(g_Contact_one_num >= g_Contact_total)
	{
		voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_CONTACT_LIST,NULL,0,0,NULL,NULL,NULL,NULL);
		return;
	}
	if(g_Contact_page_num < g_Contact_page_total)
	{
		g_Contact_page_num++;
		//��ȡ��һ��
		CWatchService_school_GetContactList(pme);
	}
	else
	{
		voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_CONTACT_LIST,NULL,0,0,NULL,NULL,NULL,NULL);
	}
		
}

void CWatchService_Get_SessionList_Init()
{
	g_Session_page_num = 1;
	g_Session_total = 0;
    g_Session_one_num= 0;
	g_Session_page_total= 0;
	if(gYnSessions != NULL)
	{
		free(gYnSessions);
		gYnSessions = NULL;
	}
}
void MMI_ModemAdp_WS_Chat_Get_SessionList()
{
	MMI_ModemAdp_WS_Yn_Chat_Get_SessionList();
}

uint16_t CWathcService_Get_session_list_total()
{
	return g_Session_total;
}
void CWatchServe_Set_OwnerSend_LastSession_content(char *s_sessonid,uint8_t stype)
{
	if( gYnSessions != NULL)
	{
		for(uint16_t i = 0; i < CWathcService_Get_session_list_total(); i++)
		{
			if(strlen(gYnSessions[i].sessionId) >0 && memcmp(gYnSessions[i].sessionId,s_sessonid,strlen(gYnSessions[i].sessionId)) == 0)
			{
			//owner send ,no unread msg
				gYnSessions[i].mark = 0;
				gYnSessions[i].last_type = stype;
				memset(gYnSessions[i].last_cont,0,sizeof(gYnSessions[i].last_cont));
				if(gYnSessions[i].last_type == WATCH_VOICE_MSG_TYPE_VOICE)
					sprintf(gYnSessions[i].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_CHAT_VOICE));
				else if(gYnSessions[i].last_type == WATCH_VOICE_MSG_TYPE_EMOJI_LIST)
					sprintf(gYnSessions[i].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_EXPRESSION));
				ws_printf("CWatchServe_Set_OwnerSend_LastSession_content[%s]=%d",gYnSessions[i].sessionId,gYnSessions[i].mark);
				break;

			}

		}

	}
}

void CWathcService_parse_Get_session_list(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	int slen=0,i=0,k=0;
	int j=g_Session_one_num;
	int content_len = 0;
	 ws_cJSON *Result = NULL;
	  ws_cJSON *errmsg = NULL,*datmsg = NULL,*pItem = NULL,*sjocken = NULL,*groupitem=NULL,*SERVitem=NULL;
	 ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	 unsigned char* res_base64 = NULL;
	int outlen = 0;
	
	 pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);
		}
		return;
	}
	datmsg = ws_cJSON_GetObjectItem(pRsp, "data");
	if(datmsg==NULL)
		return;

	ws_cJSON *totalmsg = ws_cJSON_GetObjectItem(pRsp, "friendCount");
	ws_printf("CWathcService_parse_Get_session_list  total=%d",totalmsg->valueint);
	if(totalmsg)
	{
		g_Session_total = totalmsg->valueint;
	}
	else
	{
		g_Session_total = (NV_WATCH_MAX_FRIENDS_NUM+NV_WATCH_MAX_VOICE_MSG_GROUP_NUM);
	}

	if(g_Session_page_total == 0)
	{
		if((g_Session_total%SESSION_LIST_ONE_READ_COUNT) == 0)
		{
			g_Session_page_total = g_Session_total/SESSION_LIST_ONE_READ_COUNT;
		}
		else
		{
			g_Session_page_total = (g_Session_total/SESSION_LIST_ONE_READ_COUNT) + 1;
		}
	}

	content_len = ws_cJSON_GetArraySize(datmsg);
	ws_printf("CWathcService_parse_Get_session_list content_len=%d,g_Session_total=%d,pageto=%d",content_len,g_Session_total,g_Session_page_total);
	if(datmsg)
	{
		if(content_len > (NV_WATCH_MAX_FRIENDS_NUM+NV_WATCH_MAX_VOICE_MSG_GROUP_NUM))
			content_len = (NV_WATCH_MAX_FRIENDS_NUM+NV_WATCH_MAX_VOICE_MSG_GROUP_NUM);////  2 group  

		
		if(content_len > 0)
		{
			if(gYnSessions == NULL)
			{
				ws_printf("gYnSessions requesttttttttttttttt malloc");
				gYnSessions = (voice_msg_contact_t *)malloc((g_Session_total) *sizeof(voice_msg_contact_t));
				if(gYnSessions != NULL)
				{
					for(i=0;i<g_Session_total;i++)
					{
						memset(&gYnSessions[i],0,sizeof(voice_msg_contact_t));
						memset(gYnSessions[i].sessionId,0,sizeof(gYnSessions[i].sessionId));
					}
				}
			}
		}
		for (i = 0; i < content_len; i++)
        {
         	sjocken = ws_cJSON_GetArrayItem(datmsg, i);
			ws_cJSON *userids = ws_cJSON_GetObjectItem(sjocken, "type");
			ws_cJSON *uNAMEs = ws_cJSON_GetObjectItem(sjocken, "title");
			ws_cJSON *usessions = ws_cJSON_GetObjectItem(sjocken, "session_id");
			ws_cJSON *photoions = ws_cJSON_GetObjectItem(sjocken, "photo");
			if(userids && uNAMEs && usessions)
			{
				//gYnSessions[j].index =j;
				slen = strlen(uNAMEs->valuestring);
				if(slen >= sizeof(gYnSessions[j].name) )
					slen = sizeof(gYnSessions[j].name)-1;
				memcpy(gYnSessions[j].name,uNAMEs->valuestring,slen);
				
				if(memcmp(userids->valuestring,"person",6)==0)
				{
					gYnSessions[j].type = 1;//person
					gYnSessions[j].index =i;
				}
				else if(memcmp(userids->valuestring,"group",5)==0)
				{
					gYnSessions[j].type = 2;//group
					gYnSessions[j].index =VOICE_MSG_FAMILY_GROUP;
				}
				else
				{
					gYnSessions[j].type = 3;//service
					gYnSessions[j].index =i;
				}	
				
				slen = strlen(usessions->valuestring);
				if(slen >= sizeof(gYnSessions[j].sessionId) )
					slen = sizeof(gYnSessions[j].sessionId)-1;
				memcpy(gYnSessions[j].sessionId,usessions->valuestring,slen);
				//strcpy(gYnSessions[j].userId,userData->userID );////////////////////////////login user  userid
				memcpy(gYnSessions[j].imei,usessions->valuestring,slen);
				gYnSessions[j].portrait_id =WATCH_PORTRAIT_ID_CUST; //session

				if(photoions && strlen(photoions->valuestring) > 0)
				{
					int Index[10] =  {0};
					char *Datar = photoions->valuestring+9;
					int Cnt = split_string(Datar, '/', NULL, Index, 10);
					if(Cnt < 1)
						Cnt = 1;
					char *Dat = Datar + Index[Cnt-1];
					char *sptrs = strstr(Dat,".");
					if(sptrs)
						*sptrs = 0;
					uint16_t svalues = atoi(Dat);
					ws_printf("get sess conttttttttttttcnt =%d,svalues=%d",Cnt,svalues);
					gYnSessions[j].portrait_id=svalues;
				}
				else if(photoions)
				{
					//����������ͷ��
					char tmp_pre[32] = {0};
 					for(int mr = 0;mr<sizeof(VoicemsgImageList)/sizeof(voice_msg_image_list);mr++)
					{
						memset(tmp_pre,0,sizeof(tmp_pre));
						HexToAscii(VoicemsgImageList[mr].file, tmp_pre); 
						if(strcmp(tmp_pre,uNAMEs->valuestring) == 0)
						{
							gYnSessions[j].portrait_id = VoicemsgImageList[mr].img_id;
							break;
						}
					}
 				}
				
				ws_cJSON *lasttype = ws_cJSON_GetObjectItem(sjocken, "lastType");
				ws_cJSON *lastext = ws_cJSON_GetObjectItem(sjocken, "lastText");
				ws_cJSON *lasturl = ws_cJSON_GetObjectItem(sjocken, "lastUrl");
				ws_cJSON *lastusrname= ws_cJSON_GetObjectItem(sjocken, "lastUserName");
				ws_cJSON *unreadd= ws_cJSON_GetObjectItem(sjocken, "unreadCount");

				if(unreadd)
					gYnSessions[j].mark = unreadd->valueint;

				gYnSessions[j].last_type =0;
				memset(gYnSessions[j].last_cont,0,sizeof(gYnSessions[j].last_cont));
				if(lasttype)         
				{
					if(strcmp(lasttype->valuestring ,"voice") == 0)
					{
						gYnSessions[j].last_type= WATCH_VOICE_MSG_TYPE_VOICE;
						sprintf(gYnSessions[j].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_CHAT_VOICE));
					}
					else if(strcmp(lasttype->valuestring ,"emo") == 0)
					{
						gYnSessions[j].last_type= WATCH_VOICE_MSG_TYPE_EMOJI_LIST;
						sprintf(gYnSessions[j].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_EXPRESSION));
						
					}
					else if(strcmp(lasttype->valuestring ,"text") == 0)
					{
						gYnSessions[j].last_type= WATCH_VOICE_MSG_TYPE_TEXT;
						res_base64 = _base64_decode(lastext->valuestring, strlen(lastext->valuestring), &outlen); 
						if(outlen >= sizeof(gYnSessions[j].last_cont))
							outlen = sizeof(gYnSessions[j].last_cont)-1;
						memset(gYnSessions[j].last_cont,0,sizeof(gYnSessions[j].last_cont));
						memcpy(gYnSessions[j].last_cont, res_base64,outlen);
						gYnSessions[j].last_cont[outlen] = 0;
						if(res_base64 != NULL)
			    			free(res_base64);
			    		res_base64 = NULL;

					}
					else if(strcmp(lasttype->valuestring ,"image") == 0)
					{
						gYnSessions[j].last_type= WATCH_VOICE_MSG_TYPE_PHOTO;
						sprintf(gYnSessions[j].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_PICTURE));
					}
			
				}

				
				ws_printf("sessionss[%d]=%s,make=%d",j,gYnSessions[j].sessionId,gYnSessions[j].mark);
				j++;
			}
        }
	}

	WS_PRINTF("CWathcService_Get_session_list 111=%d",j);
	g_Session_one_num = j;
	if(g_Session_one_num >= g_Session_total)
	{
		voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_SESSION_LIST,NULL,0,0,NULL,NULL,NULL,NULL);
		return;
	}
	if(g_Session_page_num < g_Session_page_total)
	{
		g_Session_page_num++;
		CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_GET_SESSION_LIST); 
	}
	else
	{
		voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_SESSION_LIST,NULL,0,0,NULL,NULL,NULL,NULL);
	}
	
}

uint8_t CWathcService_Set_Request_Add_friend(ws_client * pme,ws_friends_list *friends_list)
{
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	WS_PRINTF("CWathcService_Set_Request_Add_friend[%d]=%d",g_friend_index,friends_list->cnt);
	if(g_friend_index >= friends_list->cnt )
	{
		return 0;
	}
	memset(userData->add_userID,0,sizeof(userData->add_userID));
	memset(userData->add_username,0,sizeof(userData->add_username));
	strcpy(userData->add_username,friends_list->friends[g_friend_index].name);
	strcpy(userData->add_userID,friends_list->friends[g_friend_index].imei);//好友ID
	CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_ADD_FRIENDS);
	g_friend_index++;
	return 1;
}
void CWathcService_parse_Get_friend_nearby(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	int slen=0,i=0,j=0,m=0,k=0;
	int content_len = 0;
	 ws_cJSON *Result = NULL;
	  ws_cJSON *errmsg = NULL,*datmsg = NULL,*pItem = NULL,*sjocken = NULL,*groupitem=NULL,*SERVitem=NULL;
	  ws_friend temp_dat;
	 ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	  
	 pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);
		}
		return;
	}
	datmsg = ws_cJSON_GetObjectItem(pRsp, "data");
	if(datmsg==NULL)
		return;
	
	content_len = ws_cJSON_GetArraySize(datmsg);
	//if(datmsg)
	{
		ws_printf("CWathcService_parse_Get_friend_nearby content_len=%d",content_len);
		if(content_len > NV_WATCH_MAX_FRIENDS_NUM)
			content_len = NV_WATCH_MAX_FRIENDS_NUM;

		g_friend_index = 0;
		if(content_len == 0)
			return ;

		g_friends_list.cnt = content_len;
		if(g_friends_list.friends)
	 	{
	 		lv_mem_free(g_friends_list.friends); 
			g_friends_list.friends = NULL;
	 	}
		g_friends_list.friends = lv_mem_alloc(content_len * sizeof(ws_friend));
		memset(g_friends_list.friends, 0, (content_len * sizeof(ws_friend)));
		for (i = 0; i < content_len; i++)
        {
         	sjocken = ws_cJSON_GetArrayItem(datmsg, i);
			ws_cJSON *userids = ws_cJSON_GetObjectItem(sjocken, "userID");
			ws_cJSON *uNAMEs = ws_cJSON_GetObjectItem(sjocken, "name");
			SERVitem =  ws_cJSON_GetObjectItem(sjocken, "distance");
			if(userids && uNAMEs )
			{
				slen = strlen(uNAMEs->valuestring);
				if(slen >= sizeof(g_friends_list.friends[j].name) )
					slen = sizeof(g_friends_list.friends[j].name)-1;
				memcpy(g_friends_list.friends[j].name,uNAMEs->valuestring,slen);

				slen = strlen(userids->valuestring);
				if(slen >= sizeof(g_friends_list.friends[j].imei) )
					slen = sizeof(g_friends_list.friends[j].imei)-1;
				memcpy(g_friends_list.friends[j].imei,userids->valuestring,slen);

				memset(g_friends_list.friends[j].image,0,sizeof(g_friends_list.friends[j].image));

				if(SERVitem)
				{
					for(m=0;m<strlen(SERVitem->valuestring);m++)
					{
						if(SERVitem->valuestring[m] == '.')
							break;
						if(SERVitem->valuestring[m] >= 0x30 && SERVitem->valuestring[m] <=0x39)
						{
							g_friends_list.friends[j].image[k++] = SERVitem->valuestring[m];
						}
					}
				}
				else
				{
					g_friends_list.friends[j].image[k++]=0x30;
				}
				ws_printf("CWathcService_parse_Get_friend_nearby[%d]=%s,%s,%s",j,g_friends_list.friends[j].name,g_friends_list.friends[j].imei,g_friends_list.friends[j].image);
				j++;
			}
        }
		g_friends_list.cnt = j;
	}

	//����
	memset(&temp_dat,0,sizeof(temp_dat));
	if(k>0)
	{
		for(i=0; i<g_friends_list.cnt; i++)
		{
			for(j=0;j<g_friends_list.cnt-i-1;j++)
			{
				if(atoi(g_friends_list.friends[j].image) > atoi(g_friends_list.friends[j+1].image))
				{
					memset(&temp_dat,0,sizeof(ws_friend));
					memcpy(&temp_dat,&g_friends_list.friends[j],sizeof(ws_friend));
					memcpy(&g_friends_list.friends[j],&g_friends_list.friends[j+1],sizeof(ws_friend));
					memcpy(&g_friends_list.friends[j+1],&temp_dat,sizeof(ws_friend));
				}
			}
			
		}
	}
	
	WS_PRINTF("CWathcService_Get_friends 111=%d",g_friends_list.cnt);
	if(g_friends_list.cnt > 0)
	{
		userData->is_nearby =1;
	//�������Ӻ���nearby
		CWathcService_Set_Request_Add_friend(pme,&g_friends_list);

	}
 	
}

char *CWatchService_Get_friend_devcode()
{
	ws_client* pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	return userData->dev_code;
}
void HttpPost_resonse_err_win(void * param)
{
	 ws_client* pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	if(strlen(userData->login_failMsg) > 0)
		tip_content_create_by_text2(2,lv_watch_get_top_activity_obj(),userData->login_failMsg);
}

void CWathcService_parse_Request_addfriend_response(ws_client * pme, void *data)
{
 	ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_cJSON *pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	ws_cJSON *Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		ws_cJSON *errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			int slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);

			lv_task_t * testtask_once2 = lv_task_create(HttpPost_resonse_err_win, 50, LV_TASK_PRIO_HIGHEST, NULL);
			lv_task_once(testtask_once2);
		}
		return;
	}
}

void CWathcService_parse_Get_Dev_code(ws_client * pme, void *data)
{
	 ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	
	ws_cJSON *pRsp = NULL;
 	pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	 ws_cJSON *Result = ws_cJSON_GetObjectItem(pRsp, "data");
	if(Result==NULL)
		return;
	ws_cJSON *userids = ws_cJSON_GetObjectItem(Result, "code");
	if(userids)
	{
		memset(userData->dev_code,0,sizeof(userData->dev_code));
		int slen =strlen(userids->valuestring);
		if(slen >= sizeof(userData->dev_code))
			slen = sizeof(userData->dev_code)-1;
		memcpy(userData->dev_code,userids->valuestring,slen);
		ws_printf("CWathcService_parse_Get_Dev_code =%s",userData->dev_code);
	}
	#if 0
	if(strlen(userData->dev_code) > 0)
	{
		char *find=(char *)malloc(sizeof(userData->dev_code)+1);
		memset(find,0,sizeof(userData->dev_code));
		strcpy(find,userData->dev_code);
		MMI_ModemAdp_WS_Yn_Friend_Find_By_PhbNumber(find);
	}
	#endif
}

void CWathcService_parse_Get_friend_find(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	int slen=0,i=0,j=0;
	int content_len = 0;
	 ws_cJSON *Result = NULL;
	  ws_cJSON *errmsg = NULL,*datmsg = NULL,*pItem = NULL,*sjocken = NULL,*groupitem=NULL,*SERVitem=NULL;
	 ws_cJSON *root_parse = (ws_cJSON *)data;
	 ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	  
	 pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);
			lv_task_t * testtask_once2 = lv_task_create(HttpPost_resonse_err_win, 50, LV_TASK_PRIO_HIGHEST, NULL);
			lv_task_once(testtask_once2);
		}
		return;
	}
	datmsg = ws_cJSON_GetObjectItem(pRsp, "data");
	if(datmsg==NULL)
		return;
	
	g_friend_index = 0;
	ws_cJSON *userids = ws_cJSON_GetObjectItem(datmsg, "userID");
	ws_cJSON *uNAMEs = ws_cJSON_GetObjectItem(datmsg, "name");
	SERVitem = ws_cJSON_GetObjectItem(datmsg, "is_friend");
	if(SERVitem)
	{
		content_len = atoi(SERVitem->valuestring);
		if(content_len ==1)
		{
			tip_content_create(NULL, WATCH_TEXT_ID_FRIEND_ALREADY_EXSIT);
			return;
		}
	}
	if(userids && uNAMEs )
	{
		g_friends_list.cnt = 1;
		content_len = 1;
		if(g_friends_list.friends)
	 	{
	 		lv_mem_free(g_friends_list.friends); 
			g_friends_list.friends = NULL;
	 	}
		g_friends_list.friends = lv_mem_alloc(content_len * sizeof(ws_friend));
		memset(g_friends_list.friends, 0, (content_len * sizeof(ws_friend)));
		
		slen = strlen(uNAMEs->valuestring);
		memset(g_friends_list.friends[j].name,0,sizeof(g_friends_list.friends[j].name));
		if(slen >= sizeof(g_friends_list.friends[j].name) )
			slen = sizeof(g_friends_list.friends[j].name)-1;
		memcpy(g_friends_list.friends[j].name,uNAMEs->valuestring,slen);

		slen = strlen(userids->valuestring);
		memset(g_friends_list.friends[j].imei,0,sizeof(g_friends_list.friends[j].imei));
		if(slen >= sizeof(g_friends_list.friends[j].imei) )
			slen = sizeof(g_friends_list.friends[j].imei)-1;
		memcpy(g_friends_list.friends[j].imei,userids->valuestring,slen);
		ws_printf("CWathcService_parse_Get_friend_find[%d]=%s,%s",j,g_friends_list.friends[j].name,g_friends_list.friends[j].imei);
	}
	
	
//	if(j > 0)
	{
		userData->is_nearby =0;
	//�������Ӻ���
		CWathcService_Set_Request_Add_friend(pme,&g_friends_list);

	}
 	
}

void CWathcService_Open_Add_friend_window()
{
	ws_client* pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	voice_cmd_cfm_msg_create2(NULL,userData->add_username);
}

void CWatchService_Organize_owner_voice_info(ws_client * pme,uint8_t stype)
{
	int i = 0;
	hal_rtc_t times;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	app_adaptor_voice_msg_t *b_msgs = NULL;
	b_msgs = (app_adaptor_voice_msg_t *)lv_mem_alloc(1 *sizeof(app_adaptor_voice_msg_t));
	memset(b_msgs,0,1 *sizeof(app_adaptor_voice_msg_t));

	b_msgs->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT;

	b_msgs->addcontent = 0;

	uint32_t info_mallcen = sizeof(app_adaptor_voice_msg_info_t) * 1;
	b_msgs->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(info_mallcen);
	memset(b_msgs->msg,0,info_mallcen);

	b_msgs->msg[i].direction =WATCH_VOICE_MSG_FROM_UI;
	b_msgs->msg[i].read_flag = 1;

	Hal_Rtc_Gettime(&times);
    b_msgs->msg[i].time.year = times.tm_year;
	b_msgs->msg[i].time.month = times.tm_mon;
	b_msgs->msg[i].time.day = times.tm_mday;
	b_msgs->msg[i].time.hour = times.tm_hour;
	b_msgs->msg[i].time.min = times.tm_min;

	if(stype == WATCH_VOICE_MSG_TYPE_VOICE)
	{
	    b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_VOICE;

		b_msgs->msg[i].data_type = WATCH_VOICE_DATA_TYPE_FILE;
		b_msgs->msg[i].content.voice.index = i;
		b_msgs->msg[i].content.voice.voice_len = 0;
		b_msgs->msg[i].content.voice.duration =userData->duration;
		b_msgs->msg[i].content.voice.file = (uint8_t *)lv_mem_alloc(1+strlen(userData->up_voice_path));
		strcpy(b_msgs->msg[i].content.voice.file, userData->up_voice_path );

	}
ws_printf("CWatchService_Organize_owner_voice_info[%d]=%d,%d,%d,%d,%d, ",i,b_msgs->msg[i].time.year,b_msgs->msg[i].time.month,b_msgs->msg[i].time.day,b_msgs->msg[i].time.hour,b_msgs->msg[i].time.min);
	voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_SESSION_VOICE_UPLOAD,(uint8_t *)b_msgs,1,0,NULL,userData->sessionId,NULL,NULL);
}


void CWathcService_Get_session_handle(void * param)
{
	g_Session_content_page_num++;
	ws_client* pme = MMI_ModemAdp_WS_GetClient();
	ws_printf("CWathcService_Get_session_handle[%d]=",g_Session_content_page_num);
	CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_GET_SESSION_CONTENT);

}
void CWathcService_Get_session_time_callback()
{
	if(link_get_sessioncont_timer)
	{
		uos_timer_stop(link_get_sessioncont_timer);
	}
	lv_task_t * testtask_once4 = lv_task_create(CWathcService_Get_session_handle, 100 ,LV_TASK_PRIO_HIGHEST, NULL);
    lv_task_once(testtask_once4);
}

void Start_Get_session_content_timer(uint8_t stimes)
{
	if(link_get_sessioncont_timer == NULL)
	{
		uos_timer_create(&link_get_sessioncont_timer);
	}
	else
		uos_timer_stop(link_get_sessioncont_timer);
	WS_PRINTF("\n link_get_sessioncont_timer")	 ;
	uos_timer_start(link_get_sessioncont_timer, stimes*TICKES_IN_SECOND, 0, CWathcService_Get_session_time_callback, 0);
}


void CWathcService_Get_session_content_init()
{
	g_Session_content_page_num= 1;
	g_Session_content_one_num = 0;
	g_Session_content_total= 0;
	g_Session_content_page_total= 0;
}
void CWathcService_parse_Get_session_content(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	unsigned char* res_base64 = NULL;
	int outlen = 0;
	int slen=0,i=0,j=0,mk = 0;
	int flags = 0,content_len=0;
	ws_cJSON *Result = NULL;
	app_adaptor_voice_msg_t *b_msgs = NULL;
	ws_cJSON *errmsg = NULL,*datmsg = NULL,*pItem = NULL,*sjocken = NULL,*groupitem=NULL,*SERVitem=NULL;
	ws_cJSON *root_parse = (ws_cJSON *)data;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

	 pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_RSP);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	Result = ws_cJSON_GetObjectItem(pRsp, "code");
	if(Result==NULL)
		return;

	memset(userData->login_failMsg,0,sizeof(userData->login_failMsg));

	if(Result->valueint == 0)
		;
	else
	{
		errmsg= ws_cJSON_GetObjectItem(pRsp, "msg");
		if(errmsg)
		{
			slen = strlen(errmsg->valuestring);
			if(slen >= sizeof(userData->login_failMsg))
				slen = sizeof(userData->login_failMsg)-1;
			memcpy(userData->login_failMsg,errmsg->valuestring,slen);
		}
		return;
	}
	datmsg = ws_cJSON_GetObjectItem(pRsp, "data");
	if(datmsg==NULL)
		return;

	 
	//sessionid
	pItem = ws_cJSON_GetObjectItem(datmsg, "session_id");
	sjocken = ws_cJSON_GetObjectItem(datmsg, "type");
	if(!strcmp(sjocken->valuestring,"person") || !strcmp(sjocken->valuestring,"service"))
		flags = 1;

	groupitem = ws_cJSON_GetObjectItem(datmsg, "data");
	content_len = ws_cJSON_GetArraySize(groupitem);
	ws_printf("CWathcService_parse_Get_session_content cnt=%d",content_len);
	if(content_len == 0)
		return;

	ws_cJSON *totalsmgs = ws_cJSON_GetObjectItem(datmsg, "sessionCount");
	if(totalsmgs)
	{
		g_Session_content_total = totalsmgs->valueint;
	}

	ws_printf("!!!CWathcService_parse_Get_session_content tot=%d,pagtot=%d",g_Session_content_total,g_Session_content_page_total);

	if(g_Session_content_page_total == 0)
	{
		if((g_Session_content_total%SESSION_CONTENT_ONE_READ_COUNT) == 0)
		{
			g_Session_content_page_total = g_Session_content_total/SESSION_CONTENT_ONE_READ_COUNT;
		}
		else
		{
			g_Session_content_page_total = (g_Session_content_total/SESSION_CONTENT_ONE_READ_COUNT) + 1;
		}
		
		//���seesionID δ����Ϣ mark=0
		if(gYnSessions != NULL)
		{
			for(uint16_t zr = 0; zr < CWathcService_Get_session_list_total(); zr++)
			{
				if(strlen(gYnSessions[zr].sessionId) >0 && (memcmp(gYnSessions[zr].sessionId,pItem->valuestring,strlen(gYnSessions[zr].sessionId)) == 0) && (gYnSessions[zr].mark >0))
				{
					gYnSessions[zr].mark = 0;
					break;
				}
			}
		}
		
	}

	
	b_msgs = (app_adaptor_voice_msg_t *)lv_mem_alloc(1 *sizeof(app_adaptor_voice_msg_t));
	memset(b_msgs,0,1 *sizeof(app_adaptor_voice_msg_t));
	if(flags == 1)
		b_msgs->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT;
	else
		b_msgs->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT+1;

	b_msgs->addcontent = 0;

	uint32_t info_mallcen = sizeof(app_adaptor_voice_msg_info_t) * content_len;
	b_msgs->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(info_mallcen);
	memset(b_msgs->msg,0,info_mallcen);

	for(i;i<content_len;i++)
	{
		SERVitem =  ws_cJSON_GetArrayItem(groupitem, i);
		ws_cJSON *type_s =ws_cJSON_GetObjectItem(SERVitem, "type");
		ws_cJSON *texst_s =ws_cJSON_GetObjectItem(SERVitem, "text");
		ws_cJSON *turl_s =ws_cJSON_GetObjectItem(SERVitem, "url");
		ws_cJSON *durs_s =ws_cJSON_GetObjectItem(SERVitem, "duration");

		if(type_s && !strcmp(type_s->valuestring,"text"))
		{
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_TEXT;
			if(0)
			{
				b_msgs->msg[i].content.text = (uint8_t *)lv_mem_alloc(strlen(texst_s->valuestring)+1);
				strcpy(b_msgs->msg[i].content.text, texst_s->valuestring);
			}
			else
			{
			//base64 jiemi
				res_base64 = _base64_decode(texst_s->valuestring, strlen(texst_s->valuestring), &outlen); 
				ws_printf("get dsetetet text len=%d,%d,",outlen,strlen(texst_s->valuestring));
				b_msgs->msg[i].content.text = (char *)lv_mem_alloc(sizeof(char)*(outlen+1));
				memset(b_msgs->msg[i].content.text,0,sizeof(char)*(outlen+1));
				memcpy(b_msgs->msg[i].content.text, res_base64,outlen);
				b_msgs->msg[i].content.text[outlen] = 0;
				//ke_log_bin_print(b_msgs->msg[i].content.text, strlen(b_msgs->msg[i].content.text));
				
				if(res_base64 != NULL)
	    			free(res_base64);
	    		res_base64 = NULL;

			}
		}
		else if(type_s && !strcmp(type_s->valuestring,"image"))
		{
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_PHOTO;
			b_msgs->msg[i].content.img.data = (uint8_t *)lv_mem_alloc(1+strlen(turl_s->valuestring));
			strcpy(b_msgs->msg[i].content.img.data, turl_s->valuestring);
			b_msgs->msg[i].content.img.data_size=strlen(turl_s->valuestring)+1;
		}
		else if(type_s && !strcmp(type_s->valuestring,"voice"))
		{
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_VOICE;
			b_msgs->msg[i].data_type = WATCH_VOICE_DATA_TYPE_FILE;
			b_msgs->msg[i].content.voice.index = i;
			b_msgs->msg[i].content.voice.voice_len = 0;
			b_msgs->msg[i].content.voice.duration =atoi(durs_s->valuestring);
			b_msgs->msg[i].content.voice.file = (uint8_t *)lv_mem_alloc(1+strlen(turl_s->valuestring));
			strcpy(b_msgs->msg[i].content.voice.file, turl_s->valuestring );
		}
		else if(type_s && !strcmp(type_s->valuestring,"emo"))
		{
		//base64 jiemi
			int sids = 0; 
			uint32_t svalue = WECHAT_MSG_EMOJI_ID_IDX101;
			if(texst_s && strlen(texst_s->valuestring)>0)
			{
			    res_base64 = _base64_decode(texst_s->valuestring, strlen(texst_s->valuestring), &outlen); 
				//ws_printf("get dsetetet emo len=%d,%d,%d,%s,%s",outlen,strlen(texst_s->valuestring),strlen(VoiceMsgEmoLIST[sids].file),texst_s->valuestring,res_base64);
				//ke_log_bin_print(res_base64,outlen);
				for(sids = 0;sids<sizeof(VoiceMsgEmoLIST)/sizeof(voice_msg_emoji_list);sids++)
				{
					if((memcmp(res_base64,VoiceMsgEmoLIST[sids].file,outlen) == 0) || (memcmp(res_base64,VoiceMsgEmoLIST[sids].Bigfile,outlen) == 0))
					{
						svalue = VoiceMsgEmoLIST[sids].emoji_id;
					//	ws_printf("get dsetetet emo value=%d",svalue);
						break;
					}
				}

			}
			
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_EMOJI_LIST;
			b_msgs->msg[i].content.emoji_list.cnt  = 1;
			b_msgs->msg[i].content.emoji_list.list = (uint32_t *)lv_mem_alloc(1*sizeof(uint32_t));
   			b_msgs->msg[i].content.emoji_list.list[0]= svalue;
			if(res_base64 != NULL)
	    		free(res_base64);
	    	res_base64 = NULL;
		}
		ws_cJSON *fromnaem_s =ws_cJSON_GetObjectItem(SERVitem, "fromName");
		b_msgs->msg[i].direction = WATCH_VOICE_MSG_FROM_OTHER_DEVICE;
		if(fromnaem_s)
		{
			//ke_log_bin_print(fromnaem_s->valuestring,strlen(fromnaem_s->valuestring));
			if(strlen(fromnaem_s->valuestring) >0 && strcmp(fromnaem_s->valuestring,userData->username)==0)
				b_msgs->msg[i].direction =WATCH_VOICE_MSG_FROM_UI;//owner
			b_msgs->msg[i].name=(char*)lv_mem_alloc(sizeof(char) * strlen(fromnaem_s->valuestring));
			memset(b_msgs->msg[i].name,0,(sizeof(char) * strlen(fromnaem_s->valuestring)));
			strcpy(b_msgs->msg[i].name,fromnaem_s->valuestring);
		}
		ws_cJSON *fromusrid_s =ws_cJSON_GetObjectItem(SERVitem, "fromUserID");
		if(fromusrid_s)
		{
			ws_printf("fromidddd[%d]=%s,%s",i,fromusrid_s->valuestring,userData->userID);
			if(strlen(fromusrid_s->valuestring) >0 && strcmp(fromusrid_s->valuestring,userData->userID)==0)
				b_msgs->msg[i].direction =WATCH_VOICE_MSG_FROM_UI;//owner
		}
		ws_cJSON *datat_s =ws_cJSON_GetObjectItem(SERVitem, "dateTime");
		if(datat_s)
		{
			b_msgs->msg[i].time.year = (datat_s->valuestring[0]-'0')*1000+(datat_s->valuestring[1]-'0')*100+(datat_s->valuestring[2]-'0')*10+(datat_s->valuestring[3]-'0');
			b_msgs->msg[i].time.month = (datat_s->valuestring[5]-'0')*10+(datat_s->valuestring[6]-'0');
			b_msgs->msg[i].time.day= (datat_s->valuestring[8]-'0')*10+(datat_s->valuestring[9]-'0');
			b_msgs->msg[i].time.hour =(datat_s->valuestring[11]-'0')*10+(datat_s->valuestring[12]-'0');
			b_msgs->msg[i].time.min=(datat_s->valuestring[14]-'0')*10+(datat_s->valuestring[15]-'0');
		}

		ws_cJSON *msgid_s =ws_cJSON_GetObjectItem(SERVitem, "msg_id"); 
		if(msgid_s)
			strcpy(b_msgs->msg[i].msg_id, msgid_s->valuestring);
		ws_printf("SESStime[%d]=%d,%d,%d,%d,%d,diret=%d,%s ",i,b_msgs->msg[i].time.year,b_msgs->msg[i].time.month,b_msgs->msg[i].time.day,b_msgs->msg[i].time.hour,b_msgs->msg[i].time.min,b_msgs->msg[i].direction,b_msgs->msg[i].msg_id);

		//deafult read
		b_msgs->msg[i].read_flag = 1;
	}

	voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_SESSION_CONTENT,(uint8_t *)b_msgs,content_len,0,NULL,pItem->valuestring,NULL,NULL);
	ws_printf("================== session contentpage=%d,total=%d",g_Session_content_page_num,g_Session_content_page_total);
	return ;//ֻ��һ�����������µļ�¼

	if(g_Session_content_page_num >= g_Session_content_page_total)
	{
		return;
	}
	if(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT))
		Start_Get_session_content_timer(1);
}

void CWathcService_parse_Plat_Download(ws_client * pme, void *data)
{
	ws_cJSON *pRsp = NULL;
	int slen=0,i=0,j=0;
	int content_len = 0,flags=0;
	app_adaptor_voice_msg_t *b_msgs = NULL;
	ws_cJSON *Result = NULL;
	ws_cJSON *errmsg = NULL,*datmsg = NULL,*pItem = NULL,*sjocken = NULL,*groupitem=NULL,*SERVitem=NULL;
	ws_cJSON *root_parse = (ws_cJSON *)data;
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	unsigned char* res_base64 = NULL;
	int outlen = 0;
	uint8_t new_msg_types = 0;
	  
	pRsp = ws_cJSON_GetObjectItem(root_parse, STRING_REQ);
    if(pRsp==NULL)
    {
    	WS_PRINTF("CWathcService_Set_user_login(): STRING_RSP error\n");
		return;
    }
	errmsg = ws_cJSON_GetObjectItem(pRsp, "action");
	if(errmsg == NULL)
		return ;
	if(strcmp( errmsg->valuestring, "request_add_friend")==0)
	{
	#if 0
	//对方收到平台下发�?x1082 -request_add_friend
	//本设备也会收�?x1082 -request_add_friend ，其中data下的userid是本设备的userID
		datmsg= ws_cJSON_GetObjectItem(pRsp, "data");
		if(datmsg==NULL)
			return;
		pItem = ws_cJSON_GetObjectItem(datmsg, "userID");
		groupitem = ws_cJSON_GetObjectItem(datmsg, "userName");
		SERVitem = ws_cJSON_GetObjectItem(datmsg, "fromType");
		ws_printf("~~~~CWathcService_parse_Plat_Download 11=%s,%s,nerbay=%d",pItem->valuestring,userData->add_userID,userData->is_nearby);
		if( memcmp(SERVitem->valuestring,"nearby",strlen("nearby")) == 0)
			userData->is_nearby = 1;
		else
		{
			userData->is_nearby = 0;
			//对方收到平台下发，userid可能是空�?
			if(strlen(userData->add_userID)<2)
			{
				strcpy(userData->add_userID,pItem->valuestring);
				memset(userData->add_username,0,sizeof(userData->add_username));
				slen = strlen(groupitem->valuestring);
				if(slen >= sizeof(userData->add_username) )
					slen = sizeof(userData->add_username)-3;
				memcpy(userData->add_username,groupitem->valuestring,slen);
			}

		}
		
		userData->add_friend_ok = 0;
		if(memcmp(pItem->valuestring,userData->add_userID,strlen(userData->add_userID)) == 0  )
		{
			if(userData->is_nearby)
			{
				userData->add_friend_ok =1;
				CWatchService_SendSimpleCmd(pme,KAER_CMD_DOWN_WEILIAO,1);
				//碰碰交友，对方同意，发送添加好友结�?
				CWatchService_CmdSend(pme, CMD_S2C_JCOM_HOME_SCHOOL, (unsigned long)CMD_ADD_FRIENDS_RESULT);//result_add_friend
			}
			else
			{
				CWatchService_SendSimpleCmd(pme,KAER_CMD_DOWN_WEILIAO,1);
				if(!watch_get_lcd_status())
				{
					Wakeup_GuiTask(true);
					watch_set_lcd_status(true);
			        watch_wakeup_time_reset();
				}

				MMI_ModemAdp_Rpc_Req(CWathcService_Open_Add_friend_window, 0, 0, 0);
			}
		}
		#endif
	}
	else if(strcmp( errmsg->valuestring, "result_add_friend")==0)
	{
		//ƽ̨�·����Ӻ��ѽ��
		//uint8_t ret = CWathcService_Set_Request_Add_friend(pme,&g_friends_list);
		datmsg= ws_cJSON_GetObjectItem(pRsp, "data");
		if(datmsg==NULL)
			return;
		pItem = ws_cJSON_GetObjectItem(datmsg, "userID");
		groupitem = ws_cJSON_GetObjectItem(datmsg, "userName");
		SERVitem = ws_cJSON_GetObjectItem(datmsg, "result");
		ws_printf("~~~~CWathcService_parse_Plat_Download result_add_friend =%s,%s,nerbay=%d,%s",pItem->valuestring,userData->add_userID,userData->is_nearby,SERVitem->valuestring);
		memset(userData->add_userID,0,sizeof(userData->add_userID));
		strcpy(userData->add_userID,pItem->valuestring);
		CWatchService_SendSimpleCmd(pme,KAER_CMD_DOWN_WEILIAO,1);
		if(g_friends_list.friends)
	 	{
	 		lv_mem_free(g_friends_list.friends); 
			g_friends_list.friends = NULL;
	 	}
		//�رս���
		if(lv_watch_get_activity_obj(ACT_ID_VOICE_FRIEND_ADD_BY_PHONE))
		{
			voice_msg_del_interface(ACT_ID_VOICE_FRIEND_ADD_BY_PHONE);
		}
		if(SERVitem && atoi(SERVitem->valuestring) == 0)
		{
			//refuse
			//���ӽ����ʾ����
			#if USE_LV_WATCH_DEV_CODE_ADD_FRIEND !=0
			make_friends_Result_create(NULL,0);
			#else
			app_adapter_make_friends_rsp(MAKE_FIRIENDS_FAILED);
			#endif
			return;
		}
		//���ӽ����ʾ����
		#if USE_LV_WATCH_DEV_CODE_ADD_FRIEND !=0
		make_friends_Result_create(NULL,1);
		#else
		app_adapter_make_friends_rsp(MAKE_FIRIENDS_SUCCEED);
		#endif

		g_need_Save_friend_nv = 1;
		
		//��ȡ������ϵ��
		CwatchService_Get_Contactlist();
	}
	else if(strcmp( errmsg->valuestring, "report_session_action_addContent")==0)
	{
		datmsg= ws_cJSON_GetObjectItem(pRsp, "data");
		if(datmsg==NULL)
			return;
		pItem = ws_cJSON_GetObjectItem(datmsg, "session_id");
		sjocken = ws_cJSON_GetObjectItem(datmsg, "session_type");
		if(!strcmp(sjocken->valuestring,"person") || !strcmp(sjocken->valuestring,"service"))
			flags = 1;
		b_msgs = (app_adaptor_voice_msg_t *)lv_mem_alloc(1 *sizeof(app_adaptor_voice_msg_t));
		memset(b_msgs,0,1 *sizeof(app_adaptor_voice_msg_t));
		if(flags == 1)
			b_msgs->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT;
		else
			b_msgs->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT+1;

		b_msgs->addcontent = 1;
		content_len = 1;
		
		uint32_t info_mallcen = sizeof(app_adaptor_voice_msg_info_t) * content_len;
		b_msgs->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(info_mallcen);
		memset(b_msgs->msg,0,info_mallcen);

		ws_cJSON *type_s =ws_cJSON_GetObjectItem(datmsg, "type");
		ws_cJSON *texst_s =ws_cJSON_GetObjectItem(datmsg, "text");
		ws_cJSON *turl_s =ws_cJSON_GetObjectItem(datmsg, "url");
		ws_cJSON *durs_s =ws_cJSON_GetObjectItem(datmsg, "duration");

		i = 0;
		if(type_s && !strcmp(type_s->valuestring,"text"))
		{
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_TEXT;
			res_base64 = _base64_decode(texst_s->valuestring, strlen(texst_s->valuestring), &outlen); 
			ws_printf("receive textttttttttt=%d,out=%d",strlen(texst_s->valuestring),outlen);
			b_msgs->msg[i].content.text = (char *)lv_mem_alloc(sizeof(char)*(outlen+1));
			memset(b_msgs->msg[i].content.text,0,sizeof(char)*(outlen+1));
			memcpy(b_msgs->msg[i].content.text, res_base64,outlen);
			b_msgs->msg[i].content.text[outlen] = 0;
			//ke_log_bin_print(b_msgs->msg[i].content.text, strlen(b_msgs->msg[i].content.text));
			
			if(res_base64 != NULL)
    			free(res_base64);
    		res_base64 = NULL;
		}
		else if(type_s && !strcmp(type_s->valuestring,"image"))
		{
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_PHOTO;
			b_msgs->msg[i].content.img.data = (uint8_t *)lv_mem_alloc(1+strlen(turl_s->valuestring));
			strcpy(b_msgs->msg[i].content.img.data, turl_s->valuestring);
			b_msgs->msg[i].content.img.data_size=strlen(turl_s->valuestring)+1;
		}
		else if(type_s && !strcmp(type_s->valuestring,"voice"))
		{
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_VOICE;
			b_msgs->msg[i].data_type = WATCH_VOICE_DATA_TYPE_FILE;
			b_msgs->msg[i].content.voice.index = i;
			b_msgs->msg[i].content.voice.voice_len = 0;
			b_msgs->msg[i].content.voice.duration =atoi(durs_s->valuestring);
			b_msgs->msg[i].content.voice.file = (uint8_t *)lv_mem_alloc(1+strlen(turl_s->valuestring));
			strcpy(b_msgs->msg[i].content.voice.file, turl_s->valuestring );
		}
		else if(type_s && !strcmp(type_s->valuestring,"emo"))
		{
		//base64 jiemi
			int sids = 0; 
			uint32_t svalue = WECHAT_MSG_EMOJI_ID_IDX101;
			if(texst_s && strlen(texst_s->valuestring)>0)
			{
			    res_base64 = _base64_decode(texst_s->valuestring, strlen(texst_s->valuestring), &outlen); 
				ws_printf("get dsetetet emo32 len=%d,%d,%d,%s,%d",outlen,strlen(texst_s->valuestring),strlen(VoiceMsgEmoLIST[sids].file),texst_s->valuestring,sizeof(VoiceMsgEmoLIST)/sizeof(voice_msg_emoji_list));
				//ke_log_bin_print(res_base64,outlen);
				for(sids = 0;sids<sizeof(VoiceMsgEmoLIST)/sizeof(voice_msg_emoji_list);sids++)
				{
					if((memcmp(res_base64,VoiceMsgEmoLIST[sids].file,outlen) == 0) || (memcmp(res_base64,VoiceMsgEmoLIST[sids].Bigfile,outlen) == 0))
					{
						svalue = VoiceMsgEmoLIST[sids].emoji_id;
						ws_printf("get dsetetet emo2 value=%d",svalue);
						break;
					}
				}

			}
			
			b_msgs->msg[i].type = WATCH_VOICE_MSG_TYPE_EMOJI_LIST;
			b_msgs->msg[i].content.emoji_list.cnt  = 1;
			b_msgs->msg[i].content.emoji_list.list = (uint32_t *)lv_mem_alloc(1*sizeof(uint32_t));
   			b_msgs->msg[i].content.emoji_list.list[0]= svalue;
			if(res_base64 != NULL)
	    		free(res_base64);
	    	res_base64 = NULL;
		}
		
		// if(   !strcmp(type_s->valuestring,"text")				/*老人手环KXS52版本只保留语音消息，其余屏蔽掉*/
		// 	||!strcmp(type_s->valuestring,"image")
		// 	||!strcmp(type_s->valuestring,"emo"))
		// {
		// 	voice_msg_free_msgs(b_msgs);
		// 	WS_PRINTF("--------------FFFFFFFFFFFRRRRRRRRRRR--------------------");
		// 	return;
		// }
			
		new_msg_types = b_msgs->msg[i].type;

		ws_cJSON *fromnaem_s =ws_cJSON_GetObjectItem(datmsg, "userName");
		b_msgs->msg[i].direction = WATCH_VOICE_MSG_FROM_OTHER_DEVICE;
		if(fromnaem_s)
		{
			//ke_log_bin_print(fromnaem_s->valuestring,strlen(fromnaem_s->valuestring));
			if(strcmp(fromnaem_s->valuestring,userData->username)==0)
				b_msgs->msg[i].direction =WATCH_VOICE_MSG_FROM_UI;//owner
			b_msgs->msg[i].name=(char*)lv_mem_alloc(sizeof(char) * strlen(fromnaem_s->valuestring));
			memset(b_msgs->msg[i].name,0,(sizeof(char) * strlen(fromnaem_s->valuestring)));
			strcpy(b_msgs->msg[i].name,fromnaem_s->valuestring);
		}
		ws_cJSON *fromusrid_s =ws_cJSON_GetObjectItem(datmsg, "userID");
		if(fromusrid_s)
		{
			ws_printf("plat downloadddd fromidddd[%d]=%s,%s",i,fromusrid_s->valuestring,userData->userID);
			if(strcmp(fromusrid_s->valuestring,userData->userID)==0)
				b_msgs->msg[i].direction =WATCH_VOICE_MSG_FROM_UI;//owner
			if(fromnaem_s==NULL)
			{
				b_msgs->msg[i].name=(char*)lv_mem_alloc(sizeof(char) * strlen(fromusrid_s->valuestring));
				memset(b_msgs->msg[i].name,0,(sizeof(char) * strlen(fromusrid_s->valuestring)));
				strcpy(b_msgs->msg[i].name,fromusrid_s->valuestring);
			}
		}
		ws_cJSON *datat_s =ws_cJSON_GetObjectItem(datmsg, "dateTime");
		if(datat_s)
		{
			b_msgs->msg[i].time.year = (datat_s->valuestring[0]-'0')*1000+(datat_s->valuestring[1]-'0')*100+(datat_s->valuestring[2]-'0')*10+(datat_s->valuestring[3]-'0');
			b_msgs->msg[i].time.month = (datat_s->valuestring[5]-'0')*10+(datat_s->valuestring[6]-'0');
			b_msgs->msg[i].time.day= (datat_s->valuestring[8]-'0')*10+(datat_s->valuestring[9]-'0');
			b_msgs->msg[i].time.hour =(datat_s->valuestring[11]-'0')*10+(datat_s->valuestring[12]-'0');
			b_msgs->msg[i].time.min=(datat_s->valuestring[14]-'0')*10+(datat_s->valuestring[15]-'0');
		}

		ws_printf("platdownload time[%d]=%d,%d,%d,%d,%d, ",i,b_msgs->msg[i].time.year,b_msgs->msg[i].time.month,b_msgs->msg[i].time.day,b_msgs->msg[i].time.hour,b_msgs->msg[i].time.min);
		ws_cJSON *msgid_s =ws_cJSON_GetObjectItem(datmsg, "msg_id");
		if(msgid_s)
			strcpy(b_msgs->msg[i].msg_id, msgid_s->valuestring);
		//deafult read
		b_msgs->msg[i].read_flag = 1;

		if(!watch_get_lcd_status())
		{
			Wakeup_GuiTask(true);
		}
		
		//�Ự�б���������
		lv_obj_t *sess_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_SESSION);
		lv_obj_t *chat_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT);
		lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
		ws_printf("new msg makr schat_obj=%d,chat_obj=%d,chat_ext=%d",sess_obj,chat_obj,chat_ext);
		if( gYnSessions != NULL)
		{
			for( i = 0; i < CWathcService_Get_session_list_total(); i++)
			{
				ws_printf("+++++ frome new msg makr[%s]=%d",gYnSessions[i].sessionId,gYnSessions[i].mark);
				if(strlen(gYnSessions[i].sessionId) >0 && memcmp(gYnSessions[i].sessionId,pItem->valuestring,strlen(gYnSessions[i].sessionId)) == 0)
				{
					if(sess_obj !=	NULL && chat_obj == NULL)
						gYnSessions[i].mark++;//session list win
					else if(sess_obj !=	NULL && chat_obj != NULL)
					{
						//��ǰ�������ݽ����ǵ�ǰsessinID���յ���mark=0
						ws_printf("----frome ssid=%s,%s",chat_ext->cur_contact->sessionId,chat_ext->cur_contact->imei);
						if(memcmp(chat_ext->cur_contact->sessionId,pItem->valuestring,strlen(gYnSessions[i].sessionId)) == 0)
							gYnSessions[i].mark = 0;// chat win
						else
							gYnSessions[i].mark++;
					}
					gYnSessions[i].last_type = new_msg_types;
					memset(gYnSessions[i].last_cont,0,sizeof(gYnSessions[i].last_cont));
					if(gYnSessions[i].last_type == WATCH_VOICE_MSG_TYPE_VOICE)
						sprintf(gYnSessions[i].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_CHAT_VOICE));
					else if(gYnSessions[i].last_type == WATCH_VOICE_MSG_TYPE_EMOJI_LIST)
						sprintf(gYnSessions[i].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_EXPRESSION));
					else if(gYnSessions[i].last_type == WATCH_VOICE_MSG_TYPE_TEXT)
					{
						if(outlen >= sizeof(gYnSessions[i].last_cont))
							outlen = sizeof(gYnSessions[i].last_cont)-1;
						memset(gYnSessions[i].last_cont,0,sizeof(gYnSessions[i].last_cont));
						memcpy(gYnSessions[i].last_cont,b_msgs->msg[0].content.text,outlen);
						gYnSessions[i].last_cont[outlen] = 0;
					}
					else
					{
						sprintf(gYnSessions[i].last_cont,"[%s]",(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_PICTURE));	
					}
					ws_printf("______________ new msg makr[%s]=%d,%s",gYnSessions[i].sessionId,gYnSessions[i].mark,gYnSessions[i].last_cont);
					break;

				}

			}

		}
		voice_msg_new_msg_ind(WATCH_VOICE_MSG_SET_SESSION_CONTENT_ADD,(uint8_t *)b_msgs,content_len,0,NULL,pItem->valuestring,NULL,NULL);

		CWatchService_SendSimpleCmd(pme,KAER_CMD_DOWN_WEILIAO,1);

	//����������˳�����Ҫ���»Ự�б� lastest������ʾ
		if(sess_obj !=	NULL && chat_obj != NULL)
		{
			g_need_update_seesionlist = 1;
			if(chat_ext && memcmp(chat_ext->cur_contact->sessionId,pItem->valuestring,strlen(pItem->valuestring)) == 0)
			{
				//当前看的是sessionDI一致的消息，清未读数量
				//发送获取会话，但不处理协议解析
				g_clear_cur_seesion_unread = 1;
				uint8_t *sfind = malloc(100);
				memset(sfind,0,100);
				if(chat_ext->cur_contact->index >= VOICE_MSG_FAMILY_GROUP)
					sprintf(sfind,"%s,group",chat_ext->cur_contact->sessionId);
				else
					sprintf(sfind,"%s,person",chat_ext->cur_contact->sessionId);
				g_Session_content_page_num= 1;
				MMI_ModemAdp_WS_Yn_Chat_Get_SessionContent(sfind);
			}
		}
	//�����б������£��򿪺����е��������	

		if(gYnSessions == NULL && sess_obj==NULL && chat_obj != NULL && chat_ext != NULL)
		{
			ws_printf("@@@@@@@@@@@@ curlistid=%s",chat_ext->cur_contact->sessionId);
			if(memcmp(chat_ext->cur_contact->sessionId,pItem->valuestring,strlen(pItem->valuestring)) == 0)
			{
				//��ǰ������sessionDIһ�µ���Ϣ����δ������
				//���ͻ�ȡ�Ự����������Э�����
				g_clear_cur_seesion_unread = 1;
				uint8_t *sfind = malloc(100);
				memset(sfind,0,100);
				if(chat_ext->cur_contact->index >= VOICE_MSG_FAMILY_GROUP)
					sprintf(sfind,"%s,group",chat_ext->cur_contact->sessionId);
				else
					sprintf(sfind,"%s,person",chat_ext->cur_contact->sessionId);
				g_Session_content_page_num= 1;
				ws_printf("-------------------------- need clear unreaddddd");
				MMI_ModemAdp_WS_Yn_Chat_Get_SessionContent(sfind);
			}
		}

	}

}


#endif

#endif

