Checking whether the ASM compiler is GNU using "--version" did not match "(GNU assembler)|(GCC)|(Free Software Foundation)":
Fatal error: C3900U: Unrecognized option '--version'.
Checking whether the ASM compiler is Clang using "--version" did not match "(clang version)":
Fatal error: C3900U: Unrecognized option '--version'.
Checking whether the ASM compiler is AppleClang using "--version" did not match "(Apple LLVM version)":
Fatal error: C3900U: Unrecognized option '--version'.
Checking whether the ASM compiler is HP using "-V" did not match "HP C":
Fatal error: C3900U: Unrecognized option '-V'.
Checking whether the ASM compiler is Intel using "--version" did not match "(ICC)":
Fatal error: C3900U: Unrecognized option '--version'.
Checking whether the ASM compiler is SunPro using "-V" did not match "Sun C":
Fatal error: C3900U: Unrecognized option '-V'.
Checking whether the ASM compiler is XL using "-qversion" did not match "XL C":
Fatal error: C3900U: Unrecognized option '-qversion'.
Checking whether the ASM compiler is MSVC using "-?" did not match "Microsoft":
Fatal error: C3900U: Unrecognized option '-?'.
Checking whether the ASM compiler is TI using "-h" did not match "Texas Instruments":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armcc [4d3604]
For Educational purposes only
Software supplied by: ARM Limited

Usage:         armcc [options] file1 file2 ... filen
Main options:
        
--arm          Generate ARM code
--thumb        Generate Thumb code
--c90          Switch to C mode (default for .c files)
--cpp          Switch to C++ mode (default for .cpp files)
-O0            Minimum optimization
-O1            Restricted optimization for debugging
-O2            High optimization
-O3            Maximum optimization
-Ospace        Optimize for codesize
-Otime         Optimize for maximum performance
--cpu <cpu>    Select CPU to generate code for
--cpu list     Output a list of all the selectable CPUs
-o <file>      Name the final output file of the compilation
-c             Compile only, do not link
--asm          Output assembly code as well as object code
-S             Output assembly code instead of object code
--interleave   Interleave source with disassembly (use with --asm or -S)
-E             Preprocess the C source code only
-D<symbol>     Define <symbol> on entry to the compiler
-g             Generate tables for high-level debugging
-I<directory>  Include <directory> on the #include search path
Checking whether the ASM compiler is IAR using "" did not match "IAR Assembler":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armcc [4d3604]
For Educational purposes only
Software supplied by: ARM Limited

Usage:         armcc [options] file1 file2 ... filen
Main options:
        
--arm          Generate ARM code
--thumb        Generate Thumb code
--c90          Switch to C mode (default for .c files)
--cpp          Switch to C++ mode (default for .cpp files)
-O0            Minimum optimization
-O1            Restricted optimization for debugging
-O2            High optimization
-O3            Maximum optimization
-Ospace        Optimize for codesize
-Otime         Optimize for maximum performance
--cpu <cpu>    Select CPU to generate code for
--cpu list     Output a list of all the selectable CPUs
-o <file>      Name the final output file of the compilation
-c             Compile only, do not link
--asm          Output assembly code as well as object code
-S             Output assembly code instead of object code
--interleave   Interleave source with disassembly (use with --asm or -S)
-E             Preprocess the C source code only
-D<symbol>     Define <symbol> on entry to the compiler
-g             Generate tables for high-level debugging
-I<directory>  Include <directory> on the #include search path
