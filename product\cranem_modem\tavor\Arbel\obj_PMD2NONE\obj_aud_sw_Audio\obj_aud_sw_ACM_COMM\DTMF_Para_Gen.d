\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/DTMF_Para_Gen.o : \aud_sw\ACM_COMM\src\dtmf_gen\DTMF_Para_Gen.c
\aud_sw\ACM_COMM\src\dtmf_gen\DTMF_Para_Gen.c:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/DTMF_Para_Gen.o : \aud_sw\AuC\inc\basic_op_audio.h
\aud_sw\AuC\inc\basic_op_audio.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/DTMF_Para_Gen.o : \aud_sw\AuC\inc\dspfns_copy.h
\aud_sw\AuC\inc\dspfns_copy.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/DTMF_Para_Gen.o : \tavor\Arbel\obj_PMD2NONE\inc\typedefs_audio.h
\tavor\Arbel\obj_PMD2NONE\inc\typedefs_audio.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/DTMF_Para_Gen.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/DTMF_Para_Gen.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
