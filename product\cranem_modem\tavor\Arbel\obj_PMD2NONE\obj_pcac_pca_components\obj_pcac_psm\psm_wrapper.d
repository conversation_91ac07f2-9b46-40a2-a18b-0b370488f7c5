\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \pcac\psm\src\psm_wrapper.c
\pcac\psm\src\psm_wrapper.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \tavor\Arbel\obj_PMD2NONE\inc\psm_wrapper.h
\tavor\Arbel\obj_PMD2NONE\inc\psm_wrapper.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \tavor\Arbel\obj_PMD2NONE\inc\psm.h
\tavor\Arbel\obj_PMD2NONE\inc\psm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hal\UART\inc\UART.h
\hal\UART\inc\UART.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\pmu\inc\pmu.h
\hop\pmu\inc\pmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \pcac\duster\inc\psm_module_def.h
\pcac\duster\inc\psm_module_def.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \pcac\duster\inc\duster_applets.h
\pcac\duster\inc\duster_applets.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_thread.h
\os\threadx\inc\tx_thread.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_port.h
\os\threadx\inc\tx_port.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa_old_api.h
\os\osa\inc\osa_old_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_hisr.h
\os\threadx\inc\tx_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa_tx.h
\os\osa\inc\osa_tx.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_thread.h
\os\threadx\inc\tx_thread.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_port.h
\os\threadx\inc\tx_port.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_hisr.h
\os\threadx\inc\tx_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_timer.h
\os\threadx\inc\tx_timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_initialize.h
\os\threadx\inc\tx_initialize.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\bsp_hisr.h
\csw\BSP\inc\bsp_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa_utils.h
\os\osa\inc\osa_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\nu_xscale\inc\nucleus.h
\os\nu_xscale\inc\nucleus.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa_internals.h
\os\osa\inc\osa_internals.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_hisr.h
\os\threadx\inc\tx_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\threadx\inc\tx_initialize.h
\os\threadx\inc\tx_initialize.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa_um_extr.h
\os\osa\inc\osa_um_extr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \os\osa\inc\osa_um_defs.h
\os\osa\inc\osa_um_defs.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \pcac\duster\inc\duster.h
\pcac\duster\inc\duster.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\INCLUDE\FDI_TYPE.h
\softutil\FDI\src\INCLUDE\FDI_TYPE.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\fdi_cfg.h
\csw\platform\inc\fdi_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\mmap.h
\csw\platform\inc\mmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hal\MMU\inc\mmu.h
\hal\MMU\inc\mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\loadTable.h
\csw\BSP\inc\loadTable.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\bsp_config.h
\csw\BSP\inc\bsp_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \csw\BSP\inc\ptable.h
\csw\BSP\inc\ptable.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\INCLUDE\FDI_CUST.h
\softutil\FDI\src\INCLUDE\FDI_CUST.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\FDI_ADD\runvars.h
\softutil\FDI\src\FDI_ADD\runvars.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\INCLUDE\FDI_ERR.h
\softutil\FDI\src\INCLUDE\FDI_ERR.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\FDI_ADD\FDI_OS.h
\softutil\FDI\src\FDI_ADD\FDI_OS.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\INCLUDE\FDI_TYPE.h
\softutil\FDI\src\INCLUDE\FDI_TYPE.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \softutil\FDI\src\FM_INC\FDI_FILE.h
\softutil\FDI\src\FM_INC\FDI_FILE.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \tavor\Arbel\obj_PMD2NONE\inc\fatwk_psm.h
\tavor\Arbel\obj_PMD2NONE\inc\fatwk_psm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/psm_wrapper.o : \tavor\Arbel\inc\platform.h
\tavor\Arbel\inc\platform.h:
