# 1 "\\aud_sw\\ACM_COMM\\src\\dtmf_gen\\Utils.c"
# 1 "\\aud_sw\\AuC\\inc\\basic_op_audio.h"
#pragma once
# 1 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
/* dspfns.h
 *
 * Copyright 2001 ARM Limited. All rights reserved.
 *
 * RCS $Revision: 149794 $
 * Checkin $Date: 2009-11-26 17:03:19 +0000 (Thu, 26 Nov 2009) $
 * Revising $Author: agrant $
 */

/* ----------------------------------------------------------------------
 * This header file provides a set of DSP-type primitive
 * operations, such as 16-bit and 32-bit saturating arithmetic. The
 * operations it provides are similar to the ones used by the ITU
 * for publishing specifications of DSP algorithms.
 */




# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 26 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"
/* assert.h: ANSI 'C' (X3J11 Oct 88) library header section 4.2 */
/* Copyright (C) Codemist Ltd., 1988-1993                       */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */

/*
 * The assert macro puts diagnostics into programs. When it is executed,
 * if its argument expression is false, it writes information about the
 * call that failed (including the text of the argument, the name of the
 * source file, and the source line number - the latter are respectively
 * the values of the preprocessing macros __FILE__ and __LINE__) on the
 * standard error stream. It then calls the abort function.
 * If its argument expression is true, the assert macro returns no value.
 */

/*
 * Note that <assert.h> may be included more that once in a program with
 * different setting of NDEBUG. Hence the slightly unusual first-time
 * only flag.
 */

# 43 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"
    extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
    extern __declspec(__nothrow) __declspec(__noreturn) void __aeabi_assert(const char *, const char *, int) __attribute__((__nonnull__(1,2)));
# 53 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"

# 77 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"





/* end of assert.h */

# 27 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

# 34 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

# 44 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

# 54 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

/* Define this to 1 if you do not need add() etc. to set the saturation flag */




/* Define this to 1 if you believe all shift counts are in the range [-255,255] */










#pragma recognize_itu_functions /* enable vectorization of ITU functions */



typedef union {
  struct {
    int _dnm:27;
    int Q:1;
    int V:1;
    int C:1;
    int Z:1;
    int N:1;
  } b;
  unsigned int word;
} _ARM_PSR;

# 102 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

register _ARM_PSR _apsr_for_q __asm("apsr");


# 185 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

static __forceinline int *_arm_global_carry(void) {
    static int c;
    return &c;
}



/*
 * Convert a 32-bit signed integer into a 16-bit signed integer by
 * saturation.
 */
static __forceinline int16_t saturate(int32_t x)
{

    return (int16_t)__ssat(x, 16);
# 207 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
}

/*
 * Add two 16-bit signed integers with saturation.
 */
static __forceinline int16_t add(int16_t x, int16_t y)
{

    return (int16_t)__qadd16(x, y);



}

/*
 * Subtract one 16-bit signed integer from another with saturation.
 */
static __forceinline int16_t sub(int16_t x, int16_t y)
{

    return (int16_t)__qsub16(x, y);



}

/*
 * Absolute value of a 16-bit signed integer. Saturating, so
 * abs(-0x8000) becomes +0x7FFF.
 */
static __forceinline int16_t abs_s(int16_t x)
{
    if (x >= 0)
        return x;

    return (int16_t)__qsub16(0, x);
# 249 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
}

/*
 * Shift a 16-bit signed integer left (or right, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int16_t shl(int16_t x, int16_t shift)
{
    if (shift <= 0 || x == 0) {



        return (int16_t) (x >> (-shift));
    }
    if (shift > 15)
        shift = 16;
    return saturate(x << shift);
}

/*
 * Shift a 16-bit signed integer right (or left, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int16_t shr(int16_t x, int16_t shift)
{
    if (shift >= 0 || x == 0) {



        return (int16_t) (x >> shift);
    }
    if (shift < -15)
        shift = -16;
    return saturate(x << (-shift));
}

/*
 * Multiply two 16-bit signed integers, shift the result right by
 * 15 and saturate it. (Saturation is only necessary if both inputs
 * were -0x8000, in which case the result "should" be 0x8000 and is
 * saturated to 0x7FFF.)
 */
static __forceinline int16_t mult(int16_t x, int16_t y)
{
    return (int16_t)(__qdbl(x*y) >> 16);
}

/*
 * Multiply two 16-bit signed integers to give a 32-bit signed
 * integer. Shift left by one, and saturate the result. (Saturation
 * is only necessary if both inputs were -0x8000, in which case the
 * result "should" be 0x40000000 << 1 = +0x80000000, and is
 * saturated to +0x7FFFFFFF.)
 */
static __forceinline int32_t L_mult(int16_t x, int16_t y)
{
    return __qdbl(x*y);
}

/*
 * Negate a 16-bit signed integer, with saturation. (Saturation is
 * only necessary when the input is -0x8000.)
 */
static __forceinline int16_t negate(int16_t x)
{

    return (int16_t)__qsub16(0, x);





}

/*
 * Return the top 16 bits of a 32-bit signed integer.
 */
static __forceinline int16_t extract_h(int32_t x)
{
    return (int16_t) (x >> 16);
}

/*
 * Return the bottom 16 bits of a 32-bit signed integer, with no
 * saturation, just coerced into a two's complement 16 bit
 * representation.
 */
static __forceinline int16_t extract_l(int32_t x)
{
    return (int16_t) x;
}

/*
 * Divide a 32-bit signed integer by 2^16, rounding to the nearest
 * integer (round up on a tie). Equivalent to adding 0x8000 with
 * saturation, then shifting right by 16.
 */
static __forceinline int16_t round(int32_t x)
{
    return extract_h(__qadd(x, 0x8000));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and add to
 * another 32-bit integer with saturation.
 * 
 * Note the intermediate saturation operation in the definition:
 * 
 *    L_mac(-1, -0x8000, -0x8000)
 * 
 * will give 0x7FFFFFFE and not 0x7FFFFFFF:
 *    the unshifted product is:   0x40000000
 *    shift left with saturation: 0x7FFFFFFF
 *    add to -1 with saturation:  0x7FFFFFFE
 */
static __forceinline int32_t L_mac(int32_t accumulator, int16_t x, int16_t y)
{
    return __qadd(accumulator, __qdbl(x*y));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and subtract
 * from another 32-bit integer with saturation.
 * 
 * Note the intermediate saturation operation in the definition:
 * 
 *    L_msu(1, -0x8000, -0x8000)
 * 
 * will give 0x80000002 and not 0x80000001:
 *    the unshifted product is:         0x40000000
 *    shift left with saturation:       0x7FFFFFFF
 *    subtract from 1 with saturation:  0x80000002
 */
static __forceinline int32_t L_msu(int32_t accumulator, int16_t x, int16_t y)
{
    return __qsub(accumulator, __qdbl(x*y));
}

/*
 * Add two 32-bit signed integers with saturation.
 */
static __forceinline int32_t L_add(int32_t x, int32_t y)
{
    return __qadd(x, y);
}

/*
 * Subtract one 32-bit signed integer from another with saturation.
 */
static __forceinline int32_t L_sub(int32_t x, int32_t y)
{
    return __qsub(x, y);
}


/*
 * Negate a 32-bit signed integer, with saturation. (Saturation is
 * only necessary when the input is -0x80000000.)
 */
static __forceinline int32_t L_negate(int32_t x)
{
    return __qsub(0, x);
}

/*
 * Multiply two 16-bit signed integers, shift the result right by
 * 15 with rounding, and saturate it. (Saturation is only necessary
 * if both inputs were -0x8000, in which case the result "should"
 * be 0x8000 and is saturated to 0x7FFF.)
 */
static __forceinline int16_t mult_r(int16_t x, int16_t y)
{
    return (int16_t)(__qdbl(x*y + 0x4000) >> 16);
}

/*
 * Return the number of bits of left shift needed to arrange for a
 * 16-bit signed integer to have value >= 0x4000 or <= -0x4001.
 * 
 * Returns 0 if x is zero (following C reference implementation).
 */
static __forceinline int16_t norm_s(int16_t x)
{
    return __clz(x ^ ((int32_t)x << 17)) & 15;
}

/*
 * Return the number of bits of left shift needed to arrange for a
 * 32-bit signed integer to have value >= 0x40000000 (if +ve)
 * or <= -0x40000001 (if -ve).
 * 
 * Returns 0 if x is zero (following C reference implementation).
 */
static __forceinline int16_t norm_l(int32_t x)
{
    return __clz(x ^ (x << 1)) & 31;
}

/*
 * Shift a 32-bit signed integer left (or right, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int32_t L_shl(int32_t x, int16_t shift)
{
    if (shift <= 0) {



        return x >> (-shift);
    }
    if (shift <= norm_l(x) || x == 0)
        return x << shift;
    return __qdbl((x < 0) ? (~0x7fffffff) : 2147483647);
}

/*
 * Shift a 32-bit signed integer right (or left, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int32_t L_shr(int32_t x, int16_t shift)
{
    if (shift >= 0) {



        return x >> shift;
    }
    if ((-shift) <= norm_l(x) || x == 0)
        return x << (-shift);
    return __qdbl((x < 0) ? (~0x7fffffff) : 2147483647);
}

/*
 * Shift a 16-bit signed integer right, with rounding. Shift left
 * with saturation if the shift count is negative.
 */
static __forceinline int16_t shr_r(int16_t x, int16_t shift)
{
    if (shift == 0 || x == 0)
        return (int16_t)x;
    if (shift > 0) {



        return (int16_t) (((x >> (shift-1)) + 1) >> 1);
    }
    if (shift < -15)
        shift = -16;
    return saturate(x << (-shift));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and add to
 * another 32-bit integer with saturation (like L_mac). Then shift
 * the result right by 15 bits with rounding (like round).
 */
static __forceinline int16_t mac_r(int32_t accumulator, int16_t x, int16_t y)
{
    return round(L_mac(accumulator, x, y));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and subtract
 * from another 32-bit integer with saturation (like L_msu). Then
 * shift the result right by 15 bits with rounding (like round).
 */
static __forceinline int16_t msu_r(int32_t accumulator, int16_t x, int16_t y)
{
    return round(L_msu(accumulator, x, y));
}

/*
 * Shift a 16-bit signed integer left by 16 bits to generate a
 * 32-bit signed integer. The bottom 16 bits are zeroed.
 */
static __forceinline int32_t L_deposit_h(int16_t x)
{
    return ((int32_t)x) << 16;
}

/*
 * Sign-extend a 16-bit signed integer by 16 bits to generate a
 * 32-bit signed integer.
 */
static __forceinline int32_t L_deposit_l(int16_t x)
{
    return (int32_t)x;
}

/*
 * Shift a 32-bit signed integer right, with rounding. Shift left
 * with saturation if the shift count is negative.
 */
static __forceinline int32_t L_shr_r(int32_t x, int16_t shift)
{
    if (shift == 0 || x == 0)
        return x;
    if (shift > 0) {



        int32_t x2 = x >> (shift-1);

        return (x2 >> 1) + (x2 & 1);
    }
    if (-shift <= norm_l(x) || x == 0)
        return x << (-shift);
    return __qdbl((x < 0) ? (~0x7fffffff) : 2147483647);
}

/*
 * Absolute value of a 32-bit signed integer. Saturating, so
 * abs(-0x80000000) becomes +0x7FFFFFFF.
 */
static __forceinline int32_t L_abs(int32_t x)
{
    if (x >= 0)
        return x;
    else
        return __qsub(0, x);
}

/*
 * Return a saturated value appropriate to the most recent carry-
 * affecting operation (L_add_c, L_macNs, L_sub_c, L_msuNs).
 * 
 * In other words: return the argument if the Q flag is clear.
 * Otherwise, return -0x80000000 or +0x7FFFFFFF depending on
 * whether the Carry flag is set or clear (respectively).
 */
static __forceinline int32_t L_sat(int32_t x)
{
    if (_apsr_for_q . b . Q) {
        _apsr_for_q . b . Q = 0;
        x = (int32_t)((uint32_t)2147483647 + (*_arm_global_carry()));
        (*_arm_global_carry()) = 0;
    }
    return x;
}





# 6 "\\aud_sw\\AuC\\inc\\basic_op_audio.h"
# 2 "\\aud_sw\\ACM_COMM\\src\\dtmf_gen\\Utils.c"
# 1 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\typedefs_audio.h"
/*
  ===========================================================================
   File: TYPEDEFS.H                                      v.2.3 - 30.Nov.2009
  ===========================================================================

            ITU-T   STL   BASIC  OPERATORS

            NEW TYPE DEFINITION PROTOTYPES

   History:
   03 Nov 04   v2.0     Incorporation of new 32-bit / 40-bit / control
                        operators for the ITU-T Standard Tool Library as 
                        described in Geneva, 20-30 January 2004 WP 3/16 Q10/16
                        TD 11 document and subsequent discussions on the
                        <EMAIL> email reflector.

                        Editor comment :
                        This file is not yet used or validated since
                        ORIGINAL_TYPEDEF_H compilation flag is defined in
                        typedef.h. This file is incorporated for future
                        reference / usage.

  ============================================================================
*/


/******************************************************************************
 *
 *      File             : typedefs.h
 *      Description      : Definition of platform independent data
 *                         types and constants
 *
 *
 *      The following platform independent data types and corresponding
 *      preprocessor (#define) constants are defined:
 *
 *        defined type  meaning           corresponding constants
 *        ----------------------------------------------------------
 *        Char          character         (none)
 *        Bool          boolean           true, false
 *        Word8         8-bit signed      minWord8,   maxWord8
 *        UWord8        8-bit unsigned    minUWord8,  maxUWord8
 *        Word16        16-bit signed     minWord16,  maxWord16
 *        UWord16       16-bit unsigned   minUWord16, maxUWord16
 *        Word32        32-bit signed     minWord32,  maxWord32
 *        UWord32       32-bit unsigned   minUWord32, maxUWord32
 *        Float         floating point    minFloat,   maxFloat
 *
 *
 *      The following compile switches are #defined:
 *
 *        PLATFORM      string indicating platform progam is compiled on
 *                      possible values: "OSF", "PC", "SUN"
 *
 *        OSF           only defined if the current platform is an Alpha
 *        PC            only defined if the current platform is a PC
 *        SUN           only defined if the current platform is a Sun
 *        
 *        LSBFIRST      is defined if the byte order on this platform is
 *                      "least significant byte first" -> defined on DEC Alpha
 *                      and PC, undefined on Sun
 *
 *****************************************************************************/





/*****************************************************************************
 *                        INCLUDE FILES
 *****************************************************************************/
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\float.h"
/* float.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd, 1988                             */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.01 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */










# 28 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\float.h"

/* IEEE version: the following values are taken from the above ANSI draft.  */
/* The ACORN FPE (v17) is known not to precisely implement IEEE arithmetic. */


    /* radix of exponent representation */
# 40 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\float.h"
    /*
     * The rounding mode for floating-point addition is characterised by the
     * value of FLT_ROUNDS:
     *  -1 : indeterminable.
     *   0 : towards zero.
     *   1 : to nearest.
     *   2 : towards positive infinity.
     *   3 : towards negative infinity.
     *   ? : any other is implementation-defined.
     */




    /* number of base-FLT_RADIX digits in the floating point mantissa */


    /* number of decimal digits that the widest floating point type
       can be rounded to and back again without changing the value */





    /*
     * The use of evaluation formats is characterized by the value of
     * FLT_EVAL_METHOD:
     *  -1 : indeterminable.
     *   0 : evaluate all operations and constants just to the range
     *       and precision of the type.
     *   1 : evaluate operations and constants of type float and
     *       double to the range and precision of the double type,
     *       evaluate long double operations and constants to the
     *       range and precision of the long double type.
     *   2 : evaluate all opertations and constants to the range and
     *       precision of the long double type.
     */


/* The values that follow are not achieved under Acorn's FPE version 17  */
/* but they should be correct in due course!                             */




    /* number of decimal digits of precision */




    /* minimum negative integer such that FLT_RADIX raised to that power */
    /* minus 1 is a normalised floating-point number. */




    /* minimum negative integer such that 10 raised to that power is in the */
    /* range of normalised floating-point numbers. */




    /* maximum integer such that FLT_RADIX raised to that power minus 1 is a */



    /* maximum integer such that 10 raised to that power is in the range of */
    /* representable finite floating-point numbers. */




    /* maximum representable finite floating-point number. */




    /* minimum positive floating point number x such that 1.0 + x != 1.0 */




    /* minimum normalised positive floating-point number. */

/*
 * The Microsoft <float.h> extensions.
 */



unsigned _controlfp(unsigned, unsigned);
unsigned _clearfp(void);
unsigned _statusfp(void);





/*
 * Because the _EM_ constants are shared between _controlfp (masks)
 * and _statusfp (sticky bits), we adopt the convention that
 * _controlfp will shift its arguments left by 8 bits before using
 * them.
 */

# 151 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\float.h"







/*
 * _FPE_ constants passed as the hidden second argument to SIGFPE
 * handlers.
 */





















/* end of float.h */

# 73 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\typedefs_audio.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 74 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\typedefs_audio.h"



/*****************************************************************************
 *                        DEFINITION OF CONSTANTS 
 *****************************************************************************/
/*
 ********* define char type
 */
typedef char Char;

typedef unsigned short int UNS_Word16;  /* 16 bit "register"  (sw*) */ 




/*
 ********* define 8 bit signed/unsigned types & constants
 */

typedef signed char Word8;



typedef unsigned char UWord8;







/*
 ********* define 16 bit signed/unsigned types & constants
 */
# 117 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\typedefs_audio.h"
typedef short Word16;


typedef unsigned short UWord16;






/* Definition of Word64 */




/* Definition of Word40 was missing  10/06/2013 */


/*
 ********* define 32 bit signed/unsigned types & constants
 */

typedef int Word32;


typedef unsigned int UWord32;
# 155 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\typedefs_audio.h"

/*
 ********* define floating point type & constants
 */
/* use "if 0" below if Float should be double;
   use "if 1" below if Float should be float
 */





typedef double Float;




/*
 ********* define complex type
 */
typedef struct {
  Float r;                      /* real part */
  Float i;                      /* imaginary part */
} CPX;

/*
 ********* Check current platform
 */
# 200 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\typedefs_audio.h"
/*#error "can't determine architecture; adapt typedefs.h to your platform"*/
/* for MSVC 2008 10/06/2013 */













typedef int Longword;             /* 32 bit "accumulator" (L_*) */
typedef short Shortword;           /* 16 bit "register"  (sw*) */
typedef short ShortwordRom;        /* 16 bit ROM data    (sr*) */
typedef int LongwordRom;          /* 32 bit ROM data    (L_r*)  */




/* end of file */
# 3 "\\aud_sw\\ACM_COMM\\src\\dtmf_gen\\Utils.c"

Longword L_mpy_ll(Longword L_var1, Longword L_var2);
int fixed_sin_x(int x)
{
		int sin,x_2,x_3,x_5;
		int a3 = 0x15555555;
		int a5 = 0x1111111;
		x_2 =  L_mpy_ll(x,x);
		x_3 =  L_mpy_ll(x,x_2);
		x_5 =  L_mpy_ll(x_2,x_3);
		x_3 = L_mpy_ll(x_3,a3);
		x_5 = L_mpy_ll(x_5,a5);
		sin = L_add(x,x_5);
		sin = L_sub(sin,x_3);
		return sin;
}


int fixed_cos_x(int x)
{
		int cos,x_2,x_4;
		int a1 = 0x7fffffff;
		int a2 = 0x5555555;
		x_2 = L_mpy_ll(x,x);
		x_4 = L_mpy_ll(x_2,x_2);
		x_2 = L_shr(x_2,1);
		x_4 = L_mpy_ll(x_4,a2);
		cos = L_sub(a1,x_2);//auto sat
		cos = L_add(cos,x_4);
		return cos;
}


Longword L_mpy_ll(Longword L_var1, Longword L_var2)
{
  Shortword swLow1,
         swLow2,
         swHigh1,
         swHigh2;
  Longword L_varOut,
         L_low,
         L_mid1,
         L_mid2,
         L_mid;


  swLow1 = shr(extract_l(L_var1), 1);
  swLow1 = (short)0x7fff & swLow1;

  swLow2 = shr(extract_l(L_var2), 1);
  swLow2 = (short)0x7fff & swLow2;
  swHigh1 = extract_h(L_var1);
  swHigh2 = extract_h(L_var2);

  L_low = L_mult(swLow1, swLow2);
  L_low = L_shr(L_low, 16);

  L_mid1 = L_mult(swHigh1, swLow2);
  L_mid1 = L_shr(L_mid1, 1);
  L_mid = L_add(L_mid1, L_low);

  L_mid2 = L_mult(swHigh2, swLow1);
  L_mid2 = L_shr(L_mid2, 1);
  L_mid = L_add(L_mid, L_mid2);

  L_mid = L_shr(L_mid, 14);
  L_varOut = L_mac(L_mid, swHigh1, swHigh2);

  return (L_varOut);
}
