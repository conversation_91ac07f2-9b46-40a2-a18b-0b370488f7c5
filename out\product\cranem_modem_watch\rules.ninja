# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.14

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: crane_modem
# Configuration: RelWithDebInfo
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__crane_modem
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__crane_modem
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armlink.exe  $LINK_FLAGS  --via=$RSP_FILE -o $TARGET_FILE --list $TARGET_FILE.map && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__zlib
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__zlib
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__libpng
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__libpng
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__libjpeg
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__libjpeg
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__libqrencode
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__libqrencode
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__code128
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__code128
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__freetype
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__freetype
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lvgl_hal
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lvgl_hal
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lvgl
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lvgl
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lvgl_ram
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lvgl_ram
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lv_drivers
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lv_drivers
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lv_drivers_ram
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lv_drivers_ram
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__xf_drv
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__xf_drv
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lv_watch_ext
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lv_watch_ext
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lv_watch
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lv_watch
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__zbar
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__zbar
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__ril
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe   --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out   -c $in
  description = Building C object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__ril
  depfile = $DEP_FILE
  deps = gcc
  command = C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armcc.exe    --via=$RSP_FILE --depend=$DEP_FILE --depend_single_line --no_depend_system_headers -o $out -c $in
  description = Building CXX object $out
  rspfile = $RSP_FILE
  rspfile_content =  $DEFINES $INCLUDES $FLAGS


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__ril
  command = cmd.exe /C "$PRE_LINK && C:\PROGRA~1\DS-5V5~1.2\sw\ARMCOM~1.06U\bin\armar.exe --create -cr $TARGET_FILE $LINK_FLAGS --via=$RSP_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\Project_area\SVN\7_kxs52_spain\prebuilts\cmake\windows-x86\bin\cmake.exe -SD:\Project_area\SVN\7_kxs52_spain\product\cranem_modem -BD:\Project_area\SVN\7_kxs52_spain\out\product\cranem_modem_watch
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\Project_area\SVN\7_kxs52_spain\prebuilts\misc\windows-x86\ninja.exe -t clean
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\Project_area\SVN\7_kxs52_spain\prebuilts\misc\windows-x86\ninja.exe -t targets
  description = All primary targets available:

