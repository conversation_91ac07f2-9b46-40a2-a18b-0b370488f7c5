# This is the CMakeCache file.
# For build in directory: d:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch
# It was generated by CMake: D:/Project_area/SVN/7_kxs52_spain/prebuilts/cmake/windows-x86/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
ARELEASE:FILEPATH=D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/arelease.exe

//The ARMCC archiver
CMAKE_AR:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armar.exe

//Path to a program.
CMAKE_ARMCC_AR:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armar.exe

//Path to a program.
CMAKE_ARMCC_LINKER:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armlink.exe

//ASM compiler
CMAKE_ASM_COMPILER:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe

//Flags used by the ASM compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the ASM compiler during DEBUG builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=

//Flags used by the ASM compiler during MINSIZEREL builds.
CMAKE_ASM_FLAGS_MINSIZEREL:STRING=

//Flags used by the ASM compiler during RELEASE builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=

//Flags used by the ASM compiler during RELWITHDEBINFO builds.
CMAKE_ASM_FLAGS_RELWITHDEBINFO:STRING=

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Path to a program.
CMAKE_CXX_COMPILER:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=--cpu=Cortex-R5 --fpu=softvfp --littleend --thumb --apcs=/interwork --cpp11 --gnu --no-exceptions --no-rtti

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Ospace -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-Otime -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g

//Path to a program.
CMAKE_C_COMPILER:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=--cpu=Cortex-R5 --fpu=softvfp --littleend --thumb --apcs=/interwork --c99 --gnu

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Ospace -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-Otime -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/crane_modem

//The ARMCC linker
CMAKE_LINKER:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armlink.exe

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=CMAKE_NM-NOTFOUND

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/fromelf.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=CMAKE_OBJDUMP-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=crane_modem

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=CMAKE_STRIP-NOTFOUND

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=D:/Project_area/SVN/7_kxs52_spain/build/toolchain_cranem_modem.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//No help, variable specified on the command line.
CRANE_PLATFORM_VERSION:UNINITIALIZED=crane-2020.02.14

//No help, variable specified on the command line.
VARIANT:UNINITIALIZED=watch

//Value Computed by CMake
code128_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/code128

//Value Computed by CMake
code128_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/code128

//Value Computed by CMake
crane_modem_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch

//Value Computed by CMake
crane_modem_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/product/cranem_modem

//Value Computed by CMake
freetype_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/freetype

//Dependencies for the target
freetype_LIB_DEPENDS:STATIC=general;etlib;

//Value Computed by CMake
freetype_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/third-party-libs/freetype

//Value Computed by CMake
juphoon_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/juphoon

//Value Computed by CMake
juphoon_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/third-party-libs/juphoon

//Value Computed by CMake
libjpeg_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/libjpeg

//Value Computed by CMake
libjpeg_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/libjpeg-turbo

//Dependencies for the target
libpng_LIB_DEPENDS:STATIC=general;zlib;

//Value Computed by CMake
libqrencode_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/libqrencode

//Value Computed by CMake
libqrencode_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/libqrencode

//Value Computed by CMake
linpng_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/libpng

//Value Computed by CMake
linpng_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/libpng

//Value Computed by CMake
lv_drivers_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/lv_drivers

//Dependencies for the target
lv_drivers_LIB_DEPENDS:STATIC=general;D:/Project_area/SVN/7_kxs52_spain/gui/libs/libbpm_xufu_20211104.a;general;D:/Project_area/SVN/7_kxs52_spain/gui/libs/pah800x_cortexR5_v303009.a;general;D:/Project_area/SVN/7_kxs52_spain/gui/libs/pah800x_v579_5.a;general;D:/Project_area/SVN/7_kxs52_spain/gui/libs/libbd_166x_0507A.a;general;D:/Project_area/SVN/7_kxs52_spain/gui/libs/arm7_20190819_V975_22d_hglbp.a;general;D:/Project_area/SVN/7_kxs52_spain/gui/libs/Gcc_slib_R5_3918_hrs_spo2_hrv_20240914_v02.2a.a;

//Value Computed by CMake
lv_drivers_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/gui/lv_drivers

//Value Computed by CMake
lv_watch_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/lv_watch

//Dependencies for the target
lv_watch_LIB_DEPENDS:STATIC=general;lv_libctccdm;general;lv_libFalldetec;general;lvgl;general;zbar;general;lv_watch_ext;general;lvgl;

//Value Computed by CMake
lv_watch_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/gui/lv_watch

//Dependencies for the target
lv_watch_ext_LIB_DEPENDS:STATIC=general;lvgl;

//Value Computed by CMake
lvgl_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/lvgl

//Dependencies for the target
lvgl_LIB_DEPENDS:STATIC=general;libpng;general;libqrencode;general;code128;general;freetype;general;libjpeg;

//Value Computed by CMake
lvgl_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/gui/lvgl

//Value Computed by CMake
ril_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/ril

//Value Computed by CMake
ril_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/product/cranem_modem/ril

//Value Computed by CMake
xf_drv_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/xf_drv

//Value Computed by CMake
xf_drv_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/gui/lv_drivers/xf_drv

//Value Computed by CMake
zbar_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/lv_watch/zbar

//Value Computed by CMake
zbar_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/third-party-libs/zbar

//Value Computed by CMake
zlib_BINARY_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch/zlib

//Value Computed by CMake
zlib_SOURCE_DIR:STATIC=D:/Project_area/SVN/7_kxs52_spain/external/zlib


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ARMCC_AR
CMAKE_ARMCC_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ARMCC_LINKER
CMAKE_ARMCC_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER
CMAKE_ASM_COMPILER-ADVANCED:INTERNAL=1
CMAKE_ASM_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS
CMAKE_ASM_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_DEBUG
CMAKE_ASM_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_MINSIZEREL
CMAKE_ASM_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELEASE
CMAKE_ASM_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELWITHDEBINFO
CMAKE_ASM_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/Project_area/SVN/7_kxs52_spain/out/product/cranem_modem_watch
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=14
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=D:/Project_area/SVN/7_kxs52_spain/prebuilts/cmake/windows-x86/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=D:/Project_area/SVN/7_kxs52_spain/prebuilts/cmake/windows-x86/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=D:/Project_area/SVN/7_kxs52_spain/prebuilts/cmake/windows-x86/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/Project_area/SVN/7_kxs52_spain/product/cranem_modem
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
CMAKE_NINJA_FORCE_RESPONSE_FILE:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=14
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=D:/Project_area/SVN/7_kxs52_spain/prebuilts/cmake/windows-x86/share/cmake-3.14
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1

