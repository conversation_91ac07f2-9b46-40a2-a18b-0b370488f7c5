# 1 "\\pcac\\mbedTLS\\mbedTLS_2_1_8\\library\\md4.c"
/*
 *  RFC 1186/1320 compliant MD4 implementation
 *
 *  Copyright (C) 2006-2015, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */
/*
 *  The MD4 algorithm was designed by <PERSON> in 1990.
 *
 *  http://www.ietf.org/rfc/rfc1186.txt
 *  http://www.ietf.org/rfc/rfc1320.txt
 */

# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/config.h"
/**
 * \file config.h
 *
 * \brief Configuration options (set of defines)
 *
 *  This set of compile-time options may be used to enable
 *  or disable features selectively, and reduce the global
 *  memory footprint.
 */
/*
 *  Copyright (C) 2006-2018, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */

# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/build_info.h"
/**
 * \file build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
 /*
  *  Copyright The Mbed TLS Contributors
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  */




# 37 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/build_info.h"

# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config.h"
/**
 * \file asros_mbedtls_config.h
 *
 * \brief Minimal configuration of features that do not require an entropy source
 */
/*
 *  Copyright (C) 2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */
/*
 * Minimal configuration of features that do not require an entropy source
 * Distinguishing reatures:
 * - no entropy module
 * - no TLS protocol implementation available due to absence of an entropy
 *   source
 *
 * See README.txt for usage instructions.
 */




# 47 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config.h"








//#define MBEDTLS_CONFIG_TLS_DEBUG

# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config_dongle.h"
/**
 * \file asros_mbedtls_config.h
 *
 * \brief Minimal configuration of features that do not require an entropy source
 */
/*
 *  Copyright (C) 2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */
/*
 * Minimal configuration of features that do not require an entropy source
 * Distinguishing reatures:
 * - no entropy module
 * - no TLS protocol implementation available due to absence of an entropy
 *   source
 *
 * See README.txt for usage instructions.
 */




//this is Minimal configuration of features for data module 
/*mbedtls macro sync define begin:*/
/*mbedtls macro sync define begin:*/
/*mbedtls macro sync define begin:*/

/* System support */






//#define MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED


/* mbed TLS feature support */




//#define MBEDTLS_ECP_DP_SECP384R1_ENABLED
//#define MBEDTLS_ECP_DP_CURVE25519_ENABLED
//#define MBEDTLS_ECP_NIST_OPTIM
//#define MBEDTLS_ECDSA_DETERMINISTIC // closed for reduce code size



//#define MBEDTLS_SELF_TEST // closed for reduce code size
//#define MBEDTLS_VERSION_FEATURES //closed for reduce code size
//#define MBEDTLS_X509_CHECK_KEY_USAGE
//#define MBEDTLS_X509_CHECK_EXTENDED_KEY_USAGE

/* mbed TLS modules */


//#define MBEDTLS_ASN1_WRITE_C // closed for reduce code size











//#define MBEDTLS_ERROR_C //closed for reduce code size

//#define MBEDTLS_HMAC_DRBG_C // closed for reduce code size
# 98 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config_dongle.h"
//#define MBEDTLS_VERSION_C // closed for reduce code size
# 111 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config_dongle.h"
//#define MBEDTLS_SSL_SRV_C
//#define MBEDTLS_CERTS_C // need open?
//#define MBEDTLS_SSL_SESSION_TICKETS











//#define MBEDTLS_CAMELLIA_C

//#define MBEDTLS_ARC4_C

/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




# 154 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config_dongle.h"

/*mbedtls macro sync define end:*/
/*mbedtls macro sync define end:*/
/*mbedtls macro sync define end:*/

# 59 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config.h"
# 208 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config.h"

# 220 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config.h"

/* Miscellaneous options */
//#define MBEDTLS_AES_ROM_TABLES

# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform_alt.h"
/*
  *  platform_alt.h
  *
  *  Copyright (C) 2018, Arm Limited, All Rights Reserved
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  *
  */




# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform.h"
/**
 *  Copyright (C) 2006-2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */





# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/build_info.h"
/**
 * \file build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
 /*
  *  Copyright The Mbed TLS Contributors
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  */

# 25 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform.h"

# 43 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform.h"









# 60 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform.h"

/*
 * MBEDTLS_ERR_PLATFORM_HW_FAILED is deprecated and should not be used.
 */




typedef struct mbedtls_timing_delay_context
{
    unsigned int timer; // ticks
    unsigned int int_ms;
    unsigned int fin_ms;
} mbedtls_timing_delay_context;

int mbedtls_timing_get_delay(void *data);
void mbedtls_timing_set_delay(void *data, unsigned int int_ms, unsigned int fin_ms);


# 25 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform_alt.h"

# 68 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_platform_alt.h"

# 225 "/pcac/mbedTLS/mbedTLS_2_1_8/asros/asros_mbedtls_config.h"

# 42 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/build_info.h"


/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */








# 1 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 */
/*
 *  Copyright (C) 2006-2018, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */

/*
 * It is recommended to include this file from your config.h
 * in order to catch dependency issues early.
 */




/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 38 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"




# 59 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"













































# 110 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"






# 127 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"










# 151 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"





# 173 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"

# 183 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"









































































































# 295 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"



















































































































































































































































































































































































# 672 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"

# 679 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"















# 700 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"

# 707 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/check_config.h"























































/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

# 58 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/build_info.h"

# 30 "/pcac/mbedTLS/mbedTLS_2_1_8/include/mbedtls/config.h"

# 30 "\\pcac\\mbedTLS\\mbedTLS_2_1_8\\library\\md4.c"




