//PPC Version : V2.1.9.30
//PPL Source File Name : \tavor\Arbel\obj_PMD2NONE\prepass_results\audio_bind.ppp
//PPL Source File Name : \\aud_sw\\Audio\\src\\audio_bind.c
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef unsigned int ACM_AudioConfirmID ;
typedef unsigned int ACM_MSAGain ;
typedef unsigned int ACM_AudioMISC ;
typedef signed char ACM_DigitalGain ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_MUTE_OFF = 0 ,	 
 ACM_MUTE_ON = 1 ,	 
	 
 ACM_AUDIO_MUTE_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioMute;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0 = 0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 , GPIO_PIN_33 , GPIO_PIN_34 , GPIO_PIN_35 , GPIO_PIN_36 , GPIO_PIN_37 , GPIO_PIN_38 , GPIO_PIN_39 ,	 
	 
 GPIO_PIN_40 , GPIO_PIN_41 , GPIO_PIN_42 , GPIO_PIN_43 , GPIO_PIN_44 , GPIO_PIN_45 , GPIO_PIN_46 , GPIO_PIN_47 ,	 
 GPIO_PIN_48 , GPIO_PIN_49 , GPIO_PIN_50 , GPIO_PIN_51 , GPIO_PIN_52 , GPIO_PIN_53 , GPIO_PIN_54 , GPIO_PIN_55 ,	 
 GPIO_PIN_56 , GPIO_PIN_57 , GPIO_PIN_58 , GPIO_PIN_59 , GPIO_PIN_60 , GPIO_PIN_61 , GPIO_PIN_62 , GPIO_PIN_63 ,	 
	 
 GPIO_PIN_64 , GPIO_PIN_65 , GPIO_PIN_66 , GPIO_PIN_67 , GPIO_PIN_68 , GPIO_PIN_69 , GPIO_PIN_70 , GPIO_PIN_71 ,	 
 GPIO_PIN_72 , GPIO_PIN_73 , GPIO_PIN_74 , GPIO_PIN_75 , GPIO_PIN_76 , GPIO_PIN_77 , GPIO_PIN_78 , GPIO_PIN_79 ,	 
 GPIO_PIN_80 , GPIO_PIN_81 , GPIO_PIN_82 , GPIO_PIN_83 , GPIO_PIN_84 , GPIO_PIN_85 , GPIO_PIN_86 , GPIO_PIN_87 ,	 
 GPIO_PIN_88 , GPIO_PIN_89 , GPIO_PIN_90 , GPIO_PIN_91 , GPIO_PIN_92 , GPIO_PIN_93 , GPIO_PIN_94 , GPIO_PIN_95 ,	 
	 
 GPIO_PIN_96 , GPIO_PIN_97 , GPIO_PIN_98 , GPIO_PIN_99 , GPIO_PIN_100 , GPIO_PIN_101 , GPIO_PIN_102 , GPIO_PIN_103 ,	 
 GPIO_PIN_104 , GPIO_PIN_105 , GPIO_PIN_106 , GPIO_PIN_107 , GPIO_PIN_108 , GPIO_PIN_109 , GPIO_PIN_110 , GPIO_PIN_111 ,	 
 GPIO_PIN_112 , GPIO_PIN_113 , GPIO_PIN_114 , GPIO_PIN_115 , GPIO_PIN_116 , GPIO_PIN_117 , GPIO_PIN_118 , GPIO_PIN_119 ,	 
 GPIO_PIN_120 , GPIO_PIN_121 , GPIO_PIN_122 , GPIO_PIN_123 , GPIO_PIN_124 , GPIO_PIN_125 , GPIO_PIN_126 , GPIO_PIN_127 ,	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ATC_DEFAULT = 0 ,	 
 ATC_HANDSET ,	 
 ATC_HEADSET ,	 
 ATC_HANDSFREE ,	 
 ATC_BLUETOOTH ,	 
 ATC_STEREO_BT ,	 
 ATC_SPEAKERPHONE ,	 
 ATC_HEADPHONE ,	 
 ATC_BT_NREC_OFF ,	 
 ATC_BT_WB ,	 
 ATC_BT_NREC_OFF_WB ,	 
 ATC_HANDSET_DUALMIC ,	 
 ATC_HEADSET_DUALMIC ,	 
 ATC_HANDSFREE_DUALMIC ,	 
 ATC_HANDSET_EXTRAVOL_ON ,	 
 ATC_HANDSFREE_EXTRAVOL_ON ,	 
 ATC_HANDSET_DUALMIC_EXTRAVOL_ON ,	 
 ATC_HANDSFREE_DUALMIC_EXTRAVOL_ON ,	 
	 
 ATC_TTY ,	 
 ATC_TTY_HCO ,	 
 ATC_TTY_VCO ,	 
 ATC_TTY_VCO_DUALMIC ,	 
	 
 ATC_HANDSET_LOOP ,	 
 ATC_HEADSET_LOOP ,	 
 ATC_HANDSFREE_LOOP ,	 
 ATC_HEADPHONE_LOOP ,	 
 ATC_STEREO_BT_LOOP ,	 
	 
 ATC_HANDSET_ENH_OFF ,	 
 ATC_HEADSET_ENH_OFF ,	 
 ATC_HANDSFREE_ENH_OFF ,	 
 ATC_HEADPHONE_ENH_OFF ,	 
 ATC_STEREO_BT_ENH_OFF ,	 
	 
 ATC_FM ,	 
	 
 ATC_PATH_NUM	 
 } ATCPath;

typedef ATCHandshakeExecMsg ATCHandshakeCfmMsg ;
typedef ATCPCMRecStrmIndiMsg ATCPCMPlayStrmRspMsg ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_RC_OK = 1 ,	 
 ACM_RC_DEVICE_ALREADY_ENABLED ,	 
 ACM_RC_DEVICE_ALREADY_DISABLED ,	 
 ACM_RC_NO_MUTE_CHANGE_NEEDED ,	 
 ACM_RC_INVALID_VOLUME_CHANGE ,	 
 ACM_RC_DEVICE_NOT_FOUND ,	 
 ACM_RC_BUFFER_GET_FUNC_INVALID ,	 
 ACM_RC_STREAM_OUT_NOT_PERFORMED ,	 
 ACM_RC_STREAM_IN_NOT_PERFORMED ,	 
 ACM_RC_STREAM_OUT_TO_BE_STOPPED_NOT_ACTIVE ,	 
 ACM_RC_STREAM_IN_TO_BE_STOPPED_NOT_ACTIVE ,	 
 ACM_RC_I2S_INVALID_DATA_POINTER ,	 
 ACM_RC_I2S_INVALID_DATA_SIZE ,	 
 ACM_RC_I2S_INVALID_NOTIFICATION_THRESHOLD ,	 
 ACM_RC_I2S_MESSAGE_QUEUE_IS_FULL ,	 
 ACM_RC_MEMORY_ALREADY_INITIALISED ,	 
	 
 //// Jackie add	 
 ACM_RC_NEED_DISABLE_PATH , // need APPS to disable codec path	 
 ACM_RC_MALLOC_ERROR , // memory error	 
 ACM_RC_OUTOF_RANGE ,	 
	 
 /* Must be at the end */	 
 ACM_RC_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_ReturnCode;

typedef UINT32 PM_TimeIn32KHzUnitsT ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 PM_EXT_DBG_EVENT_EMPTY = 0 ,	 
 PM_EXT_DBG_EVENT_GENERAL_PURPOSE = 99 , // for debug purposes , general event to	 
 // track something while debug ( not to be left in code permanently ) !	 
 PM_EXT_DBG_EVENT_D2_EXIT =100 , // 100	 
 PM_EXT_DBG_EVENT_C1_EXIT , // 101	 
 PM_EXT_DBG_EVENT_C1_GATED_EXIT , // 102	 
 PM_EXT_DBG_EVENT_TM_GET_NEAREST , // 103	 
 PM_EXT_DBG_EVENT_TM_SUSPEND , // 104	 
 PM_EXT_DBG_EVENT_TM_SYNCH_AFTER , // 105	 
 PM_EXT_DBG_EVENT_TM_NU_TICK , // 106	 
 PM_EXT_DBG_EVENT_TM_EXT_TICK , // 107	 
 PM_EXT_DBG_EVENT_TM_SKIP_OS_TICK , // 108	 
 PM_EXT_DBG_EVENT_TM_SUSPEND_ENABLE , // 109	 
 PM_EXT_DBG_EVENT_TM_SUSPEND_DISABLE , // 110	 
 PM_EXT_DBG_EVENT_TM_TRIGGER_ERROR , // 111	 
 PM_EXT_DBG_EVENT_TICK_FROM_SYNCH , // 112	 
 PM_EXT_DBG_EVENT_TICK_FROM_TRIGGER , // 113	 
 PM_EXT_DBG_EVENT_TM_HW_TIMER_SET , // 114	 
 PM_EXT_DBG_EVENT_OS_TIMER_EXPIRE , // 115	 
 PM_EXT_DBG_EVENT_TM_TICK_SUSPENDED , // 116	 
 PM_EXT_DBG_EVENT_ACTIVATE_NU_HISR , // 117	 
 PM_EXT_DBG_EVENT_ACTIVATE_GKI_HISR , // 118	 
 PM_EXT_DBG_EVENT_TIMER_DEACTIVATE , // 119	 
 PM_EXT_DBG_EVENT_TIMER_CONFIGURE , // 120	 
 PM_EXT_DBG_EVENT_TIMER_ACTIVATE , // 121	 
 PM_EXT_DBG_EVENT_TIMER_STATUS_CLEAR , // 122	 
 PM_EXT_DBG_EVENT_TIMER_TCMR_SET , // 123	 
 PM_EXT_DBG_EVENT_TIMER_STATUS_READ , // 124	 
 PM_EXT_DBG_EVENT_TIMER_TIER_CLEAR , // 125	 
 PM_EXT_DBG_EVENT_TIMER_TMR_SET , // 126	 
 PM_EXT_DBG_EVENT_TIMER_TCCR_SET , // 127	 
 PM_EXT_DBG_EVENT_TIMER_TIER_SET , // 128	 
 PM_EXT_DBG_EVENT_RM_PREVENT_D2 , // 129	 
 PM_EXT_DBG_EVENT_AAM_PREVENT_D2 , // 130	 
 PM_EXT_DBG_EVENT_GP_FLAG_1 , // 131	 
 PM_EXT_DBG_EVENT_AAM_D2_TIMER_WAKEUP , // 132	 
 PM_EXT_DBG_EVENT_AAM_D2_OWN_WAKEUP , // 133	 
 PM_EXT_DBG_EVENT_AAM_MANAGE_BUSY , // 134	 
 PM_EXT_DBG_EVENT_AAM_MANAGE_FREE , // 135	 
 PM_EXT_DBG_EVENT_AAM_ALLOW_D2 , // 136	 
 PM_EXT_DBG_EVENT_AAM_AA_FORBID_D2 , // 137	 
 PM_EXT_DBG_EVENT_AAM_TM_FORBID_D2 , // 138	 
 PM_EXT_DBG_EVENT_AAM_APP_TM_D2 , // 139	 
 PM_EXT_DBG_EVENT_AAM_OST_TM_D2 , // 140	 
 PM_EXT_DBG_EVENT_RM_TCU_ALLOC , // 141	 
 PM_EXT_DBG_EVENT_RM_TCU_FREE , // 142	 
 PM_EXT_DBG_EVENT_RM_SCK_ALLOC , // 143	 
 PM_EXT_DBG_EVENT_RM_SCK_FREE , // 144	 
 PM_EXT_DBG_EVENT_RM_ALLOW_D2 , // 145	 
 PM_EXT_DBG_EVENT_RM_FORBID_D2 , // 146	 
 PM_EXT_DBG_EVENT_RM_ALLOW_C1_GATED , // 147	 
 PM_EXT_DBG_EVENT_TCU_D2_PREPARE , // 148	 
 PM_EXT_DBG_EVENT_TCU_D2_RECOVER , // 149	 
 PM_EXT_DBG_EVENT_CPA_D2_PREPARE , // 150	 
 PM_EXT_DBG_EVENT_CPA_D2_RECOVER , // 151	 
 PM_EXT_DBG_EVENT_CPA_D2_WAKEUP , // 152	 
 PM_EXT_DBG_EVENT_D2_WAKEUP_TIMER , // 153	 
 PM_EXT_DBG_EVENT_GSM_WAKEUP_SWI , // 154	 
 PM_EXT_DBG_EVENT_GSM_SLEEP_SWI , // 155	 
 ////////////////////////////////////////////// DDR	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_HIGH_FREQ_ACK_WHILE_RELINQUISH_HIGH_IS_PENDING ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_REQUEST_ACK ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_SYSTEM_IN_REG_RUNNING_MODE_AND_SEND_REQ ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_REQUEST_ACK_WHILE_HIGH_IS_PENDING ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_HIGH_FREQ_ACK ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_HIGH_FREQ_ACK_AND_SEND_REQ ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_SYSTEM_IN_REG_RUNNING_MODE ,	 
 PM_EXT_DBG_EVENT_AC_IPC_INTERRUPT_HANDLER ,	 
 PM_EXT_DBG_EVENT_260_REL_ACK ,	 
 PM_EXT_DBG_EVENT_CHANGED_SYSTEM_IN_HIGH_FREQ_MODE ,	 
 PM_EXT_DBG_EVENT_DDR_REG_REQ ,	 
 PM_EXT_DBG_EVENT_DDR_REG_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_DDR_REG_REQ_AND_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_DDR_HF_REQ ,	 
 PM_EXT_DBG_EVENT_DDR_HF_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_DDR_HF_REQ_AND_RELINQUISH ,	 
	 
 PM_EXT_DBG_EVENT_DDR_STATUS_FORBID_D2 ,	 
 ////////////////////////////////////////////// DDR	 
	 
 PM_EXT_DBG_EVENT_RM_ALLOC ,	 
 PM_EXT_DBG_EVENT_RM_FREE ,	 
	 
 PM_EXT_DBG_EVENT_D2_ENTRY ,	 
 PM_EXT_DBG_EVENT_C1_ENTRY ,	 
 PM_EXT_DBG_EVENT_C1_GATED_ENTRY ,	 
 PM_EXT_DBG_EVENT_D0CS_ENTRY ,	 
 PM_EXT_DBG_EVENT_D0CS_EXIT ,	 
 // BRN	 
 PM_EXT_DBG_EVENT_VCTCXO_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_VCTCXO_REQUEST ,	 
 PM_EXT_DBG_EVENT_DDR_LPM_DONE ,	 
 PM_EXT_DBG_EVENT_POUT_DISABLE ,	 
 PM_EXT_DBG_EVENT_POUT_ENABLE ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_HIGH ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_LOW ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_USER ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_START ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_DONE ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_GET_FREQ ,	 
 PM_EXT_DBG_EVENT_DVFM_TABLE_UPDATE ,	 
 PM_EXT_DBG_EVENT_LPM_DECISION ,	 
 PM_EXT_DBG_EVENT_SRAM_MEMORY_ERRORS_COUNT ,	 
 PM_EXT_DBG_WAKEUP_SRC ,	 
 PM_EXT_DBG_WAKEUP_SRC_NOTREGISTER ,	 
 PM_EXT_DBG_EVENT_NO_DATA = 1500 , /* indicates that no data is send with the event	 
 ( and forces the enum to be treated as UINT32 ) */	 
 PM_EXT_DBG_DATA_FAKE_D2 =0x2000000 , //	 
 PM_EXT_DBG_DATA_REAL_D2 =0x4000000 // we add to this bit hte wakeup event register	 
 // - relevant bits are 0 -19	 
 } PM_EventTypeE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 PM_TimeIn32KHzUnitsT timeStamp ;	 
 PM_EventTypeE event ;	 
 UINT32 data ;	 
 } PM_TimeStampLogEnteryS;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 nextEntryIndex ;	 
 PM_TimeStampLogEnteryS eventLog [ 256 ] ;	 
 BOOL logEnabled ;	 
 BOOL cyclic ;	 
 } PM_EventLogS;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 COMMPM_PP_NOTSET ,	 
 COMMPM_PP_1 ,	 
 COMMPM_PP_2 ,	 
 COMMPM_PP_3 ,	 
 COMMPM_PP_4 ,	 
 COMMPM_NUMBER_OF_PP = 4	 
 } CommPM_PPE;

typedef void ( *COMMPM_DDRAckNotificationT ) ( CommPM_DDRAckE ackType ) ;
typedef void ( *COMMPM_DDRDVFMNotificationT ) ( CommPM_PPE PPType ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 Service_UART = 0x01 ,	 
 Enable_TCU_clock= 0x02 ,	 
 Service_HSPDA = 0x04 ,	 
 Service_ICAT = 0x08 ,	 
 Service_client5 = 0x10 ,	 
 Service_client6 = 0x20 ,	 
 Service_client7 = 0x40 ,	 
 Service_client8 = 0x80	 
	 
 } CommPM_servicesD1E;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 COMMPM_MODEM_ENABLE = 0 ,	 
 COMMPM_MODEM_DISABLE ,	 
 COMMPM_INVALID_STATE	 
	 
 } PM_Modem_StateE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 D2mode ; // 0 - no D2 , 1 - D2 alike , 2 - full D2	 
 UINT32 D2Variation ; // 0 Stay in D0 , 1 , go to C1 , 2 Full D2	 
 UINT32 LPTsleepTicks ; // how many ticks the LPT should sleep	 
 // temp solution for L1 Standalone.	 
 UINT32 LPTdebugBusyWait ; // to enable time to enter commands when	 
 // waking up ( since currently ( Sep 6 ) at C1 / D2	 
 // we can not submit commands )	 
 UINT32 DDRFunctionalityIsOn ; // 0 -regular work without DDR / 1 -DDR functionality is open for debug / 2 -DDR full functionality	 
 UINT32 endlessLoopAfterD2nExit ;	 
 UINT32 endlessLoopAfterDDRgrantnTimes ;	 
 UINT32 kickWDTonD2Exit ;	 
 UINT16 ServiceD1control ; // word that indicates service controlling D1 , if bit is set , D1 should be prevented	 
 // ( the bit position is set by the enum CommPM_servicesD1E )	 
 UINT8 AppsCommSyncActivated ;	 
 BOOL allowC1 ; // when set real C1 from HW ( if D2 set , also C1 ? )	 
 BOOL notifyMSA ; // if set - we work with L1 , and should notify them	 
 // otherwise we are in ' D2 standalone ' - only us	 
 BOOL LPTidle ; // LPT does nothing	 
 BOOL LPTdecisionOnly ; // do only decision no setting to HW , no prepare	 
 // ( means no real D2 , no C1 ) ( not relevant in LPTidle )	 
 // if not set - we have full LPT functionality	 
 BOOL L1isRegister ; // to synchronize D2 decisions on L1 registration	 
 BOOL PmDebugTaskEnalbe ; // to enable / disable comm pm debug task	 
 } CommPM_workModeS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 DdrHfRequestTS ; // current client request TS ( maybe DDR in HF already )	 
 UINT32 DdrHfRequestFirstClientTS ; // signifies the first client request ( the one we wait for its ack )	 
 UINT32 DdrRegRequestTS ;	 
 UINT32 DdrRegMaxResponseTime ;	 
 UINT32 DdrHfClientTabResponseTime [ 30 ] ;	 
 UINT32 DdrRegClientTabResponseTime [ 30 ] ;	 
 UINT32 DdrHfMaxResponseTime ;	 
 } CommPM_DDRtimingS;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 CommPM_Active ,	 
 CommPM_Idle ,	 
 COMMPM_NUMBER_OF_CPU_STATES ,	 
 } CommPM_CPUStateE;

typedef int ( *MMIC1PrepareFunc ) ( void ) ;
typedef int ( *MMIC1RecoverFunc ) ( void ) ;
typedef int ( *MMID2PrepareFunc ) ( void ) ;
typedef int ( *MMID2RecoverFunc ) ( BOOL ExitFromD2 ) ;
typedef int ( *MMIStatusFunc ) ( void ) ;
typedef UINT32 AAM_AppsStatusT ;
typedef UINT32 AAM_HandleT ;
typedef void ( AAM_CallbackFuncT ) ( void ) ;
typedef void ( AAM_CallbackFuncPrepareT ) ( PM_PowerStatesE statetoprepare ) ;
typedef void ( AAM_CallbackFuncRecoverT ) ( PM_PowerStatesE stateexited , BOOL b_DDR_ready , BOOL b_RegsRetainedState ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /******************************************************************	 
 These ProcIDs are audio stub related	 
 *******************************************************************/	 
 // AUDIO_STUB_START_ID ,	 
 // AUDIO_STUB_CONFIRM ,	 
 // AUDIO_STUB_STOP_ID ,	 
	 
 /******************************************************************	 
 These ProcIDs are ACM_COMM related	 
 *******************************************************************/	 
 AUDIO_ACM_START_ID=100 , // ACM_COMM procID sarts from 100	 
 AUDIO_REGCPNT_ID , // 101	 
 AUDIO_REGCPNT_CONFIRM , // 102	 
	 
 AUDIO_ENABLEHWCPNT_ID , // 103	 
 AUDIO_ENABLEHWCPNT_CONFIRM , // 104	 
	 
 AUDIO_DISABLEHWCPNT_ID , // 105	 
 AUDIO_DISABLEHWCPNT_CONFIRM , // 106	 
	 
 AUDIO_SETCPNTVOL_ID , // 107	 
 AUDIO_SETCPNTVOL_CONFIRM , // 108	 
	 
 AUDIO_HWCPNTMUTE_ID , // 109	 
 AUDIO_HWCPNTMUTE_CONFIRM , // 110	 
	 
 AUDIO_STREAMINDICATE_ID , // 111 , From COMM to APPS	 
 AUDIO_STREAMRESPONSE , // 112 , From APPS to COMM	 
	 
 AUDIO_ENABLEOUTSTREAM_ID , // 113	 
 AUDIO_ENABLEOUTSTREAM_CONFIRM , // 114	 
	 
 AUDIO_DISABLEOUTSTREAM_ID , // 115	 
 AUDIO_DISABLEOUTSTREAM_CONFIRM , // 116	 
	 
 AUDIO_ENABLEINSTREAM_ID , // 117	 
 AUDIO_ENABLEINSTREAM_CONFIRM , // 118	 
	 
 AUDIO_DISABLEINSTREAM_ID , // 119	 
 AUDIO_DISABLEINSTREAM_CONFIRM , // 120	 
	 
 /* for enabling / disabling the audio at the begining / ending of a call */	 
 AUDIO_CONVERSATIONSTART_ID , // 121 , From COMM to APPS	 
 AUDIO_CONVERSATIONSTOP_ID , // 122 , From COMM to APPS	 
	 
 /*	 
 *Jackie , 2010 -0518	 
 * APPS audio send AUDIO_SETMSASETTING_ID to control MSA	 
 * COMM audio send AUDIO_GETMSASETTING_ID to APPS audio , APPS can refresh its audio calibration UI	 
 */	 
 AUDIO_SETMSASETTING_ID , // 123	 
 AUDIO_GETMSASETTING_ID , // 124	 
	 
 /*	 
 *Jackie , 2010 -0817	 
 * APPS audio send AUDIO_SYNC_CODEC_ID to sync gain information of codec chip	 
 */	 
 AUDIO_SYNC_CODEC_ID , // 125	 
	 
 /*	 
 *Jackie , 2010 -1207	 
 * MSA detect DTMF tone from far-end.	 
 */	 
 AUDIO_DTMFDETECT_ID , // 126 , From AP to CP: enable / disable	 
 AUDIO_DTMFDETECTED_ID , // 127 , From CP to AP:Forward detected DTMF code to AP audio	 
	 
 /*	 
 *Jackie , 2011 -0212	 
 * Speech logging feature	 
 */	 
 AUDIO_ENABLESPEECHLOGGING_ID , // 128 , From APPS to COMM: start speech logging	 
 AUDIO_DISABLESPEECHLOGGING_ID , // 129 , From APPS to COMM: stop speech logging	 
 AUDIO_TXSPEECHDATA_ID , // 130 , From COMM to APPS: tx speech data	 
 AUDIO_RXSPEECHDATA_ID , // 131 , From COMM to APPS: rx speech data	 
	 
 AUDIO_SETMSAGAIN_ID , // 132 , From APPS to COMM: Set MSA gain	 
 AUDIO_SETMSAGAIN_CONFIRM , // 133 , From COMM to APPS: Confirm	 
	 
 AUDIO_SETEQSETTING_ID , // 134 , From APPS to COMM: Set USER-EQ	 
 AUDIO_SETEQSETTING_CONFIRM , // 135 , From COMM to APPS: Confirm	 
	 
	 
 } ACM_EXTENT_ID;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_MSA_PCM = 0 , /* first format must be ' 0 ' - used by ' for ' loops */	 
 ACM_XSCALE_PCM ,	 
 ACM_I2S ,	 
 ACM_AUDIO_DATA , /* For DAI */	 
 ACM_AUX_FM ,	 
 ACM_AUX_HW_MIDI ,	 
 ACM_AUX_APP ,	 
	 
 /* Must be at the end */	 
 ACM_NO_FORMAT ,	 
 ACM_NUM_OF_AUDIO_FORMATS = ACM_NO_FORMAT ,	 
	 
	 
 ACM_AUDIO_FORMAT_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioFormat;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* first device must be ' 0 ' - used by ' for ' loops */	 
 ACM_MAIN_SPEAKER = 0 // 0 , handset speaker	 
 , ACM_AUX_SPEAKER // 1 , handsfree speaker	 
 , ACM_HEADSET_SPEAKER // 2 , headset speaker	 
 , ACM_MONO_LEFT_SPEAKER // 3	 
 , ACM_MONO_RIGHT_SPEAKER // 4	 
 , ACM_BUZZER // 5	 
 , ACM_MIC // 6 , handset mic	 
 , ACM_MIC_DIFF // 7 ,	 
 , ACM_AUX_MIC // 8 , handsfree mic	 
 , ACM_AUX_MIC_DIFF // 9	 
 , ACM_BLUETOOTH_SPEAKER // 10 , bluetooth speaker	 
 , ACM_BLUETOOTH_MIC // 11 , bluetooth mic	 
 , ACM_DAI_OUT // 12	 
 , ACM_DAI_IN // 13	 
 , ACM_CAR_KIT_SPEAKER // 14	 
 , ACM_CAR_KIT_MIC // 15	 
 , ACM_INPUT_DEVICE_8 // 16	 
 , ACM_HEADSET_MIC // 17 , headset mic	 
 , ACM_MIC_EC // 18	 
 , ACM_AUX_MIC_EC // 19	 
 , ACM_HEADSET_MIC_EC // 20	 
 , ACM_MIC_LOOP_SPEAKER // 21	 
 , ACM_MIC_LOOP_EARPIECE // 22	 
 , ACM_HEADSET_MIC_LOOP // 23	 
	 
 , ACM_TTY_IN_45	 
 , ACM_INPUT_DEVICE_10 = ACM_TTY_IN_45 // 24	 
	 
 , ACM_TTY_IN_50	 
 , ACM_INPUT_DEVICE_11 = ACM_TTY_IN_50 // 25	 
	 
 , ACM_TTY_IN_HCO // 26	 
 , ACM_TTY_VCO_MIC // 27	 
 , ACM_TTY_VCO_MIC_DUALMIC // 28	 
 , ACM_INPUT_DEVICE_15 // 29	 
 , ACM_INPUT_DEVICE_16 // 30	 
 , ACM_INPUT_DEVICE_17 // 31	 
 , ACM_INPUT_DEVICE_18 // 32	 
 , ACM_INPUT_DEVICE_19 // 33	 
 , ACM_INPUT_DEVICE_20 // 34	 
 , ACM_INPUT_TEST_DEVICE = ACM_INPUT_DEVICE_20	 
 , ACM_INPUT_DEVICE_21 // 35	 
 , ACM_LINE_OUT = ACM_INPUT_DEVICE_21	 
	 
 , ACM_INPUT_DEVICE_22 // 36	 
 , ACM_INPUT_DEVICE_23 // 37	 
 , ACM_INPUT_DEVICE_24 // 38	 
 , ACM_INPUT_DEVICE_25 // 39	 
 , ACM_INPUT_DEVICE_26 // 40	 
 , ACM_INPUT_DEVICE_27 // 41	 
	 
 , ACM_TTY_OUT_45	 
 , ACM_OUTPUT_DEVICE_10 = ACM_TTY_OUT_45 // 42	 
	 
 , ACM_TTY_OUT_50	 
 , ACM_OUTPUT_DEVICE_11 = ACM_TTY_OUT_50 // 43	 
	 
 , ACM_TTY_HCO_SPEAKER // 44	 
 , ACM_TTY_OUT_VCO // 45	 
 , ACM_TTY_OUT_VCO_DUALMIC // 46	 
 , ACM_OUTPUT_DEVICE_15 // 47	 
 , ACM_OUTPUT_DEVICE_16 // 48	 
 , ACM_OUTPUT_DEVICE_17 // 49	 
 , ACM_OUTPUT_DEVICE_18 // 50	 
 , ACM_OUTPUT_DEVICE_19 // 51	 
 , ACM_OUTPUT_DEVICE_20 // 52	 
 , ACM_OUTPUT_TEST_DEVICE = ACM_OUTPUT_DEVICE_20	 
 , ACM_OUTPUT_DEVICE_21 // 53	 
 , ACM_OUTPUT_DEVICE_22 // 54	 
 , ACM_OUTPUT_DEVICE_23 // 55	 
 , ACM_OUTPUT_DEVICE_24 // 56	 
 , ACM_OUTPUT_DEVICE_25 // 57	 
 , ACM_OUTPUT_DEVICE_26 // 58	 
 , ACM_OUTPUT_DEVICE_27 // 59	 
 , ACM_OUTPUT_DEVICE_28 // 60	 
 , ACM_OUTPUT_DEVICE_29 // 61	 
	 
 , ACM_WB_BLUETOOTH_SPEAKER // 62 , WB BLUETOOTH speaker	 
 , ACM_WB_BLUETOOTH_MIC // 63 , WB BLUETOOTH mic	 
 , ACM_WB_BLUETOOTH_NREC_SPEAKER // 64 , WB BLUETOOTH NREC speaker	 
 , ACM_WB_BLUETOOTH_NREC_MIC // 65 , WB BLUETOOTH NREC mic	 
 , ACM_HEADPHONE_SPEAKER // 66 , HEADSET3POLE speaker	 
 , ACM_HEADPHONE_MIC // 67 , HEADSET3POLE mic	 
 , ACM_EXTRA_VOLUME_MAIN_SPEAKER // 68 , Handset speaker with extra volume on	 
 , ACM_EXTRA_VOLUME_MIC // 69 , Handset mic with extra volume on	 
 , ACM_EXTRA_VOLUME_AUX_SPEAKER // 70 , Aux speaker with extra volume on	 
 , ACM_EXTRA_VOLUME_AUX_MIC // 71 , Aux mic with extra volume on	 
 , ACM_BLUETOOTH6_SPEAKER // 72 , BLUETOOTH6 speaker	 
 , ACM_BLUETOOTH6_MIC // 73 , BLUETOOTH6 mic	 
 , ACM_BLUETOOTH7_SPEAKER // 74 , BLUETOOTH7 speaker	 
 , ACM_BLUETOOTH7_MIC // 75 , BLUETOOTH7 mic	 
 , ACM_BLUETOOTH8_SPEAKER // 76 , BLUETOOTH8 speaker	 
 , ACM_BLUETOOTH8_MIC // 77 , BLUETOOTH8 mic	 
 , ACM_BLUETOOTH_NREC_SPEAKER // 78 , BLUETOOTH-NREC speaker	 
 , ACM_BLUETOOTH_NREC_MIC // 79 , BLUETOOTH-NREC mic	 
	 
 // Jackie , 2011 -0222	 
 // Loop include codec and MSA	 
 , ACM_MAIN_SPEAKER__LOOP // 80 , handset speaker for loopback test	 
 , ACM_AUX_SPEAKER__LOOP // 81 , handsfree speaker for loopback test	 
 , ACM_HEADSET_SPEAKER__LOOP // 82 , headset speaker for loopback test	 
 , ACM_MIC__LOOP // 83 , handset mic for loopback test	 
 , ACM_AUX_MIC__LOOP // 84 , handsfree mic for loopback test	 
 , ACM_HEADSET_MIC__LOOP // 85 , headset mic for loopback test	 
 , ACM_HEADPHONE_SPEAKER__LOOP // 86 , bluetooth speaker for loopback test	 
 , ACM_HEADPHONE_MIC__LOOP // 87 , bluetooth mic for loopback test	 
	 
 // Jackie , 2011 -0603	 
 // Dual mic devices	 
 , ACM_MAIN_SPEAKER_DUALMIC // 88 , handset speaker for dual mic solution	 
 , ACM_AUX_SPEAKER_DUALMIC // 89 , handsfree speaker for dual mic solution	 
 , ACM_HEADSET_SPEAKER_DUALMIC // 90 , headset speaker for dual mic solution	 
 , ACM_BLUETOOTH_SPEAKER_DUALMIC // 91 , bluetooth speaker for dual mic solution	 
 , ACM_BLUETOOTH_NREC_SPEAKER_DUALMIC // 92 , bluetooth NREC speaker for dual mic solution	 
	 
 , ACM_MIC_DUALMIC // 93 , handset mic for dual mic solution	 
 , ACM_AUX_MIC_DUALMIC // 94 , handsfree mic for dual mic solution	 
 , ACM_HEADSET_MIC_DUALMIC // 95 , headset mic for dual mic solution	 
 , ACM_BLUETOOTH_MIC_DUALMIC // 96 , bluetooth mic for dual mic solution	 
 , ACM_BLUETOOTH_NREC_MIC_DUALMIC // 97 , bluetooth NREC mic for dual mic solution	 
	 
	 
 // Jackie , 2011 -0915	 
 // VT devices	 
 , ACM_MAIN_SPEAKER_VT // 98 , handset speaker for VT	 
 , ACM_AUX_SPEAKER_VT // 99 , handsfree speaker for VT	 
 , ACM_HEADSET_SPEAKER_VT // 100 , headset speaker for VT	 
 , ACM_BLUETOOTH_SPEAKER_VT // 101 , bluetooth speaker for VT	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VT // 102 , bluetooth NREC speaker for VT	 
	 
 , ACM_MIC_VT // 103 , handset mic for VT	 
 , ACM_AUX_MIC_VT // 104 , handsfree mic for VT	 
 , ACM_HEADSET_MIC_VT // 105 , headset mic for VT	 
 , ACM_BLUETOOTH_MIC_VT // 106 , bluetooth mic for VT	 
 , ACM_BLUETOOTH_NREC_MIC_VT // 107 , bluetooth NREC mic for VT	 
	 
 // VT_DUALMIC devices	 
 , ACM_MAIN_SPEAKER_VT_DUALMIC // 108 , handset speaker for VT_DUALMIC	 
 , ACM_AUX_SPEAKER_VT_DUALMIC // 109 , handsfree speaker for VT_DUALMIC	 
 , ACM_HEADSET_SPEAKER_VT_DUALMIC // 110 , headset speaker for VT_DUALMIC	 
 , ACM_BLUETOOTH_SPEAKER_VT_DUALMIC // 111 , bluetooth speaker for VT_DUALMIC	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VT_DUALMIC // 112 , bluetooth NREC speaker for VT_DUALMIC	 
	 
 , ACM_MIC_VT_DUALMIC // 113 , handset mic for VT_DUALMIC	 
 , ACM_AUX_MIC_VT_DUALMIC // 114 , handsfree mic for VT_DUALMIC	 
 , ACM_HEADSET_MIC_VT_DUALMIC // 115 , headset mic for VT_DUALMIC	 
 , ACM_BLUETOOTH_MIC_VT_DUALMIC // 116 , bluetooth mic for VT_DUALMIC	 
 , ACM_BLUETOOTH_NREC_MIC_VT_DUALMIC // 117 , bluetooth NREC mic for VT_DUALMIC	 
	 
 ////////////// Wu Bo , 2012 -0525 , support ' VOIP over modem ' ///////////////	 
 // VOIP devices	 
 , ACM_MAIN_SPEAKER_VOIP // 118 , handset speaker for VOIP	 
 , ACM_AUX_SPEAKER_VOIP // 119 , handfree speaker for VOIP	 
 , ACM_HEADSET_SPEAKER_VOIP // 120 , headset speaker for VOIP	 
 , ACM_BLUETOOTH_SPEAKER_VOIP // 121 , bluetooth speaker for VOIP	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VOIP // 122 , bluetooth NREC speaker for VOIP	 
 , ACM_BLUETOOTH_SPEAKER_VOIP_WB // 123 , bluetooth speaker for VOIP WB	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VOIP_WB // 124 , bluetooth NREC speaker for VOIP WB	 
	 
 , ACM_MIC_VOIP // 125 , handset mic for VOIP	 
 , ACM_AUX_MIC_VOIP // 126 , handfree mic for VOIP	 
 , ACM_HEADSET_MIC_VOIP // 127 , headset mic for VOIP	 
 , ACM_BLUETOOTH_MIC_VOIP // 128 , bluetooth mic for VOIP , NB	 
 , ACM_BLUETOOTH_NREC_MIC_VOIP // 129 , bluetooth NREC mic for VOIP , NB	 
 , ACM_BLUETOOTH_MIC_VOIP_WB // 130 , bluetooth mic for VOIP , WB	 
 , ACM_BLUETOOTH_NREC_MIC_VOIP_WB // 131 , bluetooth NREC mic for VOIP , WB	 
	 
 // VOIP_DUALMIC devices	 
 , ACM_MAIN_SPEAKER_VOIP_DUALMIC // 132 , handset speaker for VOIP_DUALMIC	 
 , ACM_AUX_SPEAKER_VOIP_DUALMIC // 133 , handfree speaker for VOIP_DUALMIC	 
 , ACM_HEADSET_SPEAKER_VOIP_DUALMIC // 134 , headset speaker for VOIP_DUALMIC	 
	 
 , ACM_MIC_VOIP_DUALMIC // 135 , handset mic for VOIP_DUALMIC ,	 
 , ACM_AUX_MIC_VOIP_DUALMIC // 136 , handfree mic for VOIP_DUALMIC ,	 
 , ACM_HEADSET_MIC_VOIP_DUALMIC // 137 , headset mic for VOIP_DUALMIC ,	 
	 
 , ACM_MAIN_SPEAKER__LOOP2 // 138 , handset speaker for loopback test for factory test	 
 , ACM_AUX_SPEAKER__LOOP2 // 139 , handsfree speaker for loopback test for factory test	 
 , ACM_HEADSET_SPEAKER__LOOP2 // 140 , headset speaker for loopback test for factory test	 
 , ACM_MIC__LOOP2 // 141 , handset mic for loopback test for factory test	 
 , ACM_AUX_MIC__LOOP2 // 142 , handsfree mic for loopback test for factory test	 
 , ACM_HEADSET_MIC__LOOP2 // 143 , headset mic for loopback test for factory test	 
 , ACM_HEADPHONE_SPEAKER__LOOP2 // 144 , bluetooth speaker for loopback test for factory test	 
 , ACM_HEADPHONE_MIC__LOOP2 // 145 , bluetooth mic for loopback test for factory test	 
	 
 /* Must be at the end */	 
 , ACM_NUM_OF_AUDIO_DEVICES	 
	 
 // Tag for search end of audio device table	 
 , ACM_NOT_CONNECTED = 0x7fffffff	 
	 
 , ACM_AUDIO_DEVICE_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioDevice;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_FORMAT_NOT_SUPPORTED = 0 ,	 
 ACM_FORMAT_SUPPORTED = 1 ,	 
	 
 ACM_FORMAT_SUPPORTED_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_FormatSupported;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_PATH_IN = 0 , // Tx	 
 ACM_PATH_OUT = 1 , // Rx	 
	 
 /* Must be at the end */	 
 ACM_NUM_OF_PATHS ,	 
	 
 ACM_PATH_DIRECTION_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_PathDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_SCENARIO_AUDIO = 0 , // audio	 
 ACM_SCENARIO_VOICE = 1 , // voice	 
	 
 /* Must be at the end */	 
 ACM_SCENARIO_CNT ,	 
	 
 ACM_SCENARIO_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_SCENARIOT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short Configure ; // bit 0 :CP send confirmation to AP ; bit 1 : reuse speaker as receiver ; bit 2 : bypass PM813 PA	 
 unsigned short Always_Print_PCM ; // default:1 ( Print PCM in call ) ; If in production phase , set this to 0 to save traffic	 
 unsigned short Disable_All_Modules ; // default:0	 
	 
 unsigned char description [ 28 ] ;	 
 } ACM_Configuration;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short shift ;	 
 unsigned short SSCR0_HIGH ;	 
 unsigned short SSCR0_LOW ;	 
 unsigned short SSCR1_HIGH ;	 
 unsigned short SSCR1_LOW ;	 
 unsigned short SSTSA_LOW ;	 
 unsigned short SSRSA_LOW ;	 
 unsigned short SSPSP_HIGH ;	 
 unsigned short SSPSP_LOW ;	 
	 
 // Tavor only:	 
 unsigned short SSACD_LOW ;	 
 unsigned short SSACDD_HIGH ;	 
 unsigned short SSACDD_LOW ;	 
 } GSSP_Configuration;

typedef void ( *ACM_ResetStatusInd ) ( void ) ;
typedef ACMAudioStreamOutStartRsp ACMAudioStreamInStartRsp ;
typedef ACMAudioStreamOutStartRsp ACMAudioStreamOutStopRsp ;
typedef ACMAudioStreamOutStartRsp ACMAudioStreamInStopRsp ;
//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_SET {	 
 unsigned long command ;	 
 unsigned long op ;	 
 unsigned long param1 ;	 
 unsigned char data [ ( 140 ) ] ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_SET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_GET {	 
 unsigned long command ;	 
 unsigned long op ;	 
 unsigned long param1 ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_GET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_SET {	 
 unsigned long command ;	 
 unsigned long cmd_id ;	 
 unsigned long res_id ;	 
 unsigned long param2 ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_SET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_GET {	 
 unsigned long command ;	 
 unsigned long cmd_id ;	 
 unsigned long res_id ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_GET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_IND {	 
 unsigned long command ;	 
 unsigned long urc_id ;	 
 unsigned long data ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_IND;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_GET_CNF {	 
 unsigned long command ;	 
 unsigned long op ;	 
 unsigned long param1 ;	 
 unsigned long value1 ;	 
 unsigned long value2 ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_GET_CNF;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_IND {	 
 unsigned long command ;	 
 unsigned long res_id ;	 
 unsigned long res_state ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_IND;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_GET_CNF {	 
 unsigned long command ;	 
 unsigned long cmd_id ;	 
 unsigned long res_id ;	 
 unsigned long param2 ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_GET_CNF;

typedef void ( *AUDIO_ECALL_CNF_CB ) ( const ACIPC_AUDIO_VCM_ECALL_VOICE_GET_CNF* ecall_voice , const ACIPC_AUDIO_VCM_ECALL_DATA_GET_CNF* ecall_data ) ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 data [ 256 ] ;	 
 } ACMAudioDSPSettings;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 length ;	 
 UINT8 data [ 2048 ] ;	 
 } ACMAudioSpeechData;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 VC_HANDSET = 0 ,	 
 VC_HANDSFREE ,	 
 VC_HEADSET , // with mic	 
 VC_HEADPHONE , // without mic	 
 VC_BT ,	 
 VC_LOOPBACK ,	 
 VC_MAXCOUNT ,	 
	 
 AC_HANDSET = 64 ,	 
 AC_HANDSFREE ,	 
 AC_HEADSET , // with mic	 
 AC_HEADPHONE , // without mic	 
 AC_BT ,	 
 AC_KWS ,	 
	 
 AC_FM ,	 
 AC_MAXCOUNT ,	 
	 
 ACM_PROFILE_ID_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACM_PROFILE_ID;

typedef void ( *SetDownLinkStream ) ( DOWNLINKSTREAM_REQUEST *streamReq ) ;
typedef void ( *SetUpLinkStream ) ( const UINT8* buf , UINT32 size ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIO_GSM_RAT ,	 
 AUDIO_WCDMA_RAT ,	 
 AUDIO_LTE_RAT ,	 
 AUDIO_NULL_RAT ,	 
 AUDIO_ALL_RAT ,	 
 AUDIO_UNKNOWN_RAT ,	 
 } audioRat_te;

typedef audioRat_te ( *audioGetRAT_t ) ( void ) ;
typedef unsigned char ( *audioDoDataSwapBytes_t ) ( void ) ;
typedef void ( *audioGsmStartVoicePath_t ) ( void ) ;
typedef void ( *audioGsmStopVoicePath_t ) ( void ) ;
typedef void ( *audioGsmGetVocoderTypeRate_t ) ( unsigned short *vocoderType , unsigned short *vocoderRate ) ;
typedef BOOL ( *audioGsmGetDtxSupport_t ) ( void ) ;
typedef void* ( *audioGsmGetTxBuffer_t ) ( void ) ;
typedef void ( *audioGsmSetAmrToc_t ) ( unsigned short ) ;
typedef void ( *audioGsmSetAmrSidTypeInd_t ) ( unsigned short ) ;
typedef void ( *audioGsmSetEncoderFlags_t ) ( unsigned short ) ;
typedef void ( *audioGsmSetDecoderSidInd_t ) ( unsigned short ) ;
typedef void ( *POCTxPCMHandler_t ) ( short *pData , unsigned short Length ) ;
typedef void ( *POCRxPCMHandler_t ) ( short *pData , unsigned short Length ) ;
typedef int ( *POCTxAMRHandler_t ) ( void const * frame , unsigned short Length ) ;
typedef int ( *POCRxAMRHandler_t ) ( void* frame , unsigned short Length ) ;
typedef void ( *DTMFDetectionHandler_t ) ( unsigned short DTMFCode ) ;
typedef void ( *OEMAudio_GetVoiceDataCallback_t ) ( short *rx , short *tx , short *mixed , short length , unsigned int count ) ;
typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 OSA_TASK_READY ,	 
 OSA_TASK_COMPLETED ,	 
 OSA_TASK_TERMINATED ,	 
 OSA_TASK_SUSPENDED ,	 
 OSA_TASK_SLEEP ,	 
 OSA_TASK_QUEUE_SUSP ,	 
 OSA_TASK_SEMAPHORE_SUSP ,	 
 OSA_TASK_EVENT_FLAG ,	 
 OSA_TASK_BLOCK_MEMORY ,	 
 OSA_TASK_MUTEX_SUSP ,	 
 OSA_TASK_STATE_UNKNOWN ,	 
 } OSA_TASK_STATE;

//ICAT EXPORTED STRUCT 
 typedef struct OSA_TASK_STRUCT 
 {	 
 char *task_name ; /* Pointer to thread ' s name */	 
 unsigned int task_priority ; /* Priority of thread ( 0 -255 ) */	 
 unsigned long task_stack_def_val ; /* default vaule of thread */	 
 OSA_TASK_STATE task_state ; /* Thread ' s execution state */	 
 unsigned long task_stack_ptr ; /* Thread ' s stack pointer */	 
 unsigned long task_stack_start ; /* Stack starting address */	 
 unsigned long task_stack_end ; /* Stack ending address */	 
 unsigned long task_stack_size ; /* Stack size */	 
 unsigned long task_run_count ; /* Thread ' s run counter */	 
	 
 } OSA_TASK;

typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPartitionPoolRef ;
typedef UINT8 OS_STATUS ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 aplpQDepth ;	 
 UINT32 aplpStackSize ;	 
 UINT32 aplpTaskPriority ;	 
 UINT32 aplpHighQDepth ;	 
 UINT32 aplpHighStackSize ;	 
 UINT32 aplpHighTaskPriority ;	 
 } aplpConfigParams_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 DISABLE_L1C_FEATURE = 0 ,	 
 ENABLE_L1C_FEATURE = 1	 
 } l1FeatureEnable_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 l1FeatureEnable_ts enableSleep ;	 
 l1FeatureEnable_ts SleepAlike ;	 
 l1FeatureEnable_ts enableExtendedTracing ;	 
 l1FeatureEnable_ts periodicDetected ;	 
 } featuresParams_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 l1FeatureEnable_ts RESERVED1 ;	 
 l1FeatureEnable_ts RESERVED2 ;	 
 l1FeatureEnable_ts AnritsuSetup ;	 
 l1FeatureEnable_ts RESERVED4 ;	 
 l1FeatureEnable_ts frameToDiag ;	 
 l1FeatureEnable_ts RESERVED6 ;	 
 l1FeatureEnable_ts RESERVED7 ;	 
 l1FeatureEnable_ts RESERVED8 ;	 
 } featuresUnderDevelopment_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 long dpchMaxInitPower [ 5 ] ;	 
 long dpchMinInitPower ;	 
 long dpchMaxPowerLevel [ 5 ] ;	 
 long prachMaxInitPower [ 5 ] ;	 
 long prachMinInitPower ;	 
 long prachMaxPowerLevel [ 5 ] ;	 
 UINT32 numFramesToWakePccpchSkip ;	 
 } MS_configParams_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 Qin ;	 
 UINT16 Qout ;	 
 } dataPkg_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 maxPccpchShifts ;	 
 UINT8 maxTmUpdtB4DummyTracker ;	 
 UINT8 tccInterrupt0Offset ;	 
 UINT8 tccInterrupt1Offset ;	 
 } tccTmPkg_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 NORMAL_MODE = 0 , // handle COM_WARNING as warnings , COM_ERROR and DSP_EXCEPTION as errors	 
 ALL_ERRORS = 1 , // handle all as errors	 
 ALL_WARNINGS = 2 , // handle all as warnings	 
 LISTEN_TO_DSP= 3 , // handle according to DSP message ( bit indicating treatment way )	 
 TRACE_N_CONTINUE = 4 // send trace to ICAT and do nothing	 
 } L1ErrHandleMode_e;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 L1ErrHandleMode_e plpErrHandleMode ;	 
 L1ErrHandleMode_e aplpErrHandleMode ;	 
 l1FeatureEnable_ts enablePlwErrIndToPs ;	 
 UINT8 pad ;	 
 } L1_ErrorHandlerConfig_ts;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 aplpConfigParams_ts aplpConfigParams ;	 
 featuresParams_ts featuresParams ;	 
 MS_configParams_ts MS_configParams ;	 
 dataPkg_ts dataPackageParams ;	 
 tccTmPkg_ts tccTmPkgParams ;	 
 featuresUnderDevelopment_ts developmentSwitch ;	 
 L1_ErrorHandlerConfig_ts l1ErrHandler ;	 
 } l1cNvm_ts;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 NULL_RAT =0 ,	 
 WCDMA_RAT ,	 
 TDSCDMA_RAT ,	 
 GSM_RAT ,	 
 LTE_RAT ,	 
 ALL_RAT ,	 
 NR_RAT // ********** add	 
 } initialRat_te;

//ICAT EXPORTED ENUM 
 typedef enum RatSetCauseTag 
 {	 
 RAT_CAUSE_NULL ,	 
 POWER_ON_ON_GSM ,	 
 POWER_ON_ON_TD ,	 
 DRAT_HO_TD_TO_GSM ,	 
 DRAT_HO_TD_TO_GSM_FAIL , // 4	 
 DRAT_HO_GSM_TO_TD ,	 
 DRAT_HO_GSM_TO_TD_FAIL ,	 
 DRAT_RESEL_GSM_TO_TD ,	 
 DRAT_RESEL_GSM_TO_TD_FAIL ,	 
 DRAT_CCO_TD_DCH_TO_GSM , // 9	 
 DRAT_CCO_TD_DCH_TO_GSM_FAIL ,	 
 DRAT_RESEL_TD_IDLE_TO_GSM ,	 
 DRAT_RESEL_TD_IDLE_TO_GSM_FAIL ,	 
 DRAT_RESEL_TD_FACH_TO_GSM ,	 
 DRAT_RESEL_TD_FACH_TO_GSM_FAIL , // 14	 
 DRAT_SWITCH_TD_TO_GSM ,	 
 DRAT_SWITCH_GSM_TO_TD ,	 
 PLMN_SEARCH_IN_TD_GSM_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_TD_BACK_TO_TD ,	 
 PLMN_SEARCH_IN_GSM_TD_BCH_DECODE , // 19	 
 PLMN_SEARCH_IN_GSM_BACK_TO_GSM ,	 
 SWITCH_TO_TD_ATTER_GSM_TERMINATE ,	 
 DRAT_SET_FROM_GPLC ,	 
 POWER_ON_LTE , // 23	 
 /* ********** - Update IRAT feature - begin */	 
	 
 IRAT_HO_LTE_TO_TD , // 24	 
 IRAT_HO_LTE_TO_TD_FAIL ,	 
 IRAT_HO_TD_TO_LTE ,	 
 IRAT_HO_TD_TO_LTE_FAIL ,	 
	 
 IRAT_RESEL_LTE_TO_TD ,	 
 IRAT_RESEL_LTE_TO_TD_FAIL , // 29	 
 IRAT_RESEL_TD_TO_LTE ,	 
 IRAT_RESEL_TD_TO_LTE_FAIL ,	 
	 
 PLMN_SEARCH_IN_LTE_TD_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_TD_LTE_BCCH_DECODE ,	 
	 
 DRAT_RESEL_GSM_TO_LTE , // 34	 
 DRAT_RESEL_GSM_TO_LTE_FAIL ,	 
	 
 DRAT_RESEL_LTE_TO_GSM ,	 
 DRAT_RESEL_LTE_TO_GSM_FAIL ,	 
	 
 PLMN_SEARCH_IN_TD_LTE_BACK_TO_TD ,	 
 PLMN_SEARCH_IN_GSM_LTE_BCCH_DECODE , // 39	 
 PLMN_SEARCH_IN_GSM_LTE_BACK_TO_GSM , // 4 40	 
 PLMN_SEARCH_IN_LTE_GSM_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_LTE_GSM_BACK_TO_LTE ,	 
 PLMN_SEARCH_IN_LTE_TD_BACK_TO_LTE ,	 
	 
 /*Add by qhli begin*/	 
 IRAT_HO_LTE_TO_WB = 44 ,	 
 IRAT_HO_LTE_TO_WB_FAIL ,	 
 IRAT_HO_WB_TO_LTE ,	 
 IRAT_HO_WB_TO_LTE_FAIL ,	 
	 
 IRAT_RESEL_LTE_TO_WB , // 48	 
 IRAT_RESEL_LTE_TO_WB_FAIL ,	 
 IRAT_RESEL_WB_TO_LTE , // 4 50	 
 IRAT_RESEL_WB_TO_LTE_FAIL ,	 
 IRAT_REDIR_WB_TO_LTE_FAIL , // CQ65927 for WB Redir To Lte Fail issue	 
	 
 PLMN_SEARCH_IN_LTE_WB_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_WB_LTE_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_LTE_WB_BACK_TO_LTE ,	 
 PLMN_SEARCH_IN_WB_LTE_BACK_TO_WB ,	 
 /*Add by qhli end*/	 
	 
 // IRAT_RESEL_WB_TO_LTE ,	 
 // PLMN_SEARCH_IN_WB_LTE_BCCH_DECODE ,	 
 // PLMN_SEARCH_IN_WB_LTE_BACK_TO_WB ,	 
 // IRAT_RESEL_LTE_TO_WB_FAIL ,	 
	 
 /* ********** - Update IRAT feature - end */	 
	 
 // CQ00035825 , Add IRAT CGI cause , start	 
 IRAT_WB_CGI_IN_LTE ,	 
 IRAT_WB_CGI_BACK_TO_LTE ,	 
 IRAT_GSM_CGI_IN_LTE ,	 
 IRAT_GSM_CGI_BACK_TO_LTE , // 4 60	 
 IRAT_TD_CGI_IN_LTE ,	 
 IRAT_TD_CGI_BACK_TO_LTE ,	 
 // CQ00035825 , Add IRAT CGI cause , end	 
	 
 // CQ35801 w / g iRat Casue , start	 
 PLMN_SEARCH_IN_WB_GSM_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_WB_GSM_BACK_TO_WB ,	 
 PLMN_SEARCH_IN_GSM_WB_BCCH_DECODE ,	 
 PLMN_SEARCH_IN_GSM_WB_BACK_TO_GSM ,	 
 IRAT_HO_WB_TO_GSM ,	 
 IRAT_HO_WB_TO_GSM_FAIL ,	 
 IRAT_HO_GSM_TO_WB ,	 
 IRAT_HO_GSM_TO_WB_FAIL , // 4 70	 
 IRAT_RESEL_GSM_TO_WB ,	 
 IRAT_RESEL_GSM_TO_WB_FAIL ,	 
 IRAT_RESEL_WB_TO_GSM ,	 
 IRAT_RESEL_WB_TO_GSM_FAIL ,	 
 IRAT_SWITCH_WB_TO_GSM ,	 
 IRAT_SWITCH_GSM_TO_WB ,	 
 // CQ35801 w / g iRat Casue , end	 
 POWER_ON_WB , // CQ42646 for CQ42639	 
 /*Modify for CQ00054259 by qhli begin*/	 
 IRAT_SWITCH_WB_TO_LTE ,	 
 IRAT_SWITCH_LTE_TO_WB ,	 
 /*Modify for CQ00054259 by qhli end*/	 
	 
 /* Add for LTE / GSM handover IRAT case */	 
 IRAT_HO_LTE_TO_GSM , // 4 80	 
 IRAT_HO_LTE_TO_GSM_FAIL ,	 
 IRAT_HO_GSM_TO_LTE ,	 
 IRAT_HO_GSM_TO_LTE_FAIL ,	 
	 
 LTE_IPC_RECEIVED_IN_NON_LTE_MODE ,	 
 LTE_CSFB_GSM , /*CQ00079576 add , value = 84 */	 
 GSM_PWROFF_TO_RESETMODE ,	 
 /*********** start add*/	 
 RAT_CAUSE_RESERVED_2 ,	 
 RAT_CAUSE_RESERVED_1 ,	 
 RAT_CAUSE_RESERVED_0 ,	 
 // #if defined ( UPGRADE_NR ) // not add macro to adapt to all products.	 
 POWER_ON_NR = 90 , // 4 90	 
 // NR->LTE	 
 IRAT_RESEL_NR_TO_LTE , // common for resel / redirect	 
 IRAT_RESEL_NR_TO_LTE_FAIL , // common for resel / redirect	 
 IRAT_HO_NR_TO_LTE ,	 
 IRAT_HO_NR_TO_LTE_FAIL ,	 
 // LTE->NR fail	 
 IRAT_RESEL_LTE_TO_NR , // common for resel / redirect	 
 IRAT_RESEL_LTE_TO_NR_FAIL , // common for resel / redirect	 
 IRAT_HO_LTE_TO_NR ,	 
 IRAT_HO_LTE_TO_NR_FAIL ,	 
 // #endif	 
 /*********** end add*/	 
	 
 // 3 NOTE: make sure enum value < 127 , because SetModeReq ( 0x8e ) ->modeSetCause only take 7 bits. ( 111 1111 = 127 )	 
	 
 RAT_CAUSE_MAX_NUM = 127 // RAT_CAUSE_NUM	 
 } RatSetCause;

typedef void ( *amrConnectionEstablish_t ) ( void ) ;
typedef void ( *amrConnectionClose_t ) ( void ) ;
typedef void ( *ctmNegoReport_t ) ( CtmNegoReport ) ;
typedef void ( *amrTxFrame_t ) ( amrFrameInfo_ts* frame ) ;
typedef void ( *IMSTxFrame_t ) ( IMSFrameInfo_ts* frame ) ;
typedef void ( *BitstreamAmrTxFrame_t ) ( BitstreamAmrFrameInfo_ts* frame ) ;
typedef void ( *PcmRxTransfer_t ) ( INT16* pData ) ;
typedef void ( *amrRxRequest_t ) ( amrFrameInfo_ts* frame ) ;
typedef void ( *ims_netEQ_callback ) ( void* data ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 IDLE_VOLTE ,	 
 PKT_VOLTE ,	 
 PCM_VOLTE	 
 } Audio_VOLTE_Mode;

typedef UINT8 ApplicationID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_RC_OK = 0 ,	 
 PRM_RC_FAIL , // MB _ Added General Fail	 
 PRM_RC_RESET_NOT_SUPPORTED ,	 
 PRM_RC_ERR_CLOCK = -100 ,	 
 PRM_RC_ERR_FREQ ,	 
 PRM_RC_ERR_NULL_POINTER ,	 
 PRM_RC_WAKEUP_NOT_SUPPORTED ,	 
 PRM_RC_SERVICE_NOT_SUPPORTED ,	 
 PRM_RC_ERR_CPMU // MB - Arbel Specific on reset on CPMU	 
 } PRM_ReturnCodeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_SRVC_DMA ,	 
 PRM_SRVC_DVFM ,	 
 PRM_SRVC_DSSP0_GB ,	 
 PRM_SRVC_DSSP1_GB ,	 
 PRM_SRVC_DSSP2_GB ,	 
 PRM_SRVC_I2C ,	 
 PRM_SRVC_MSL ,	 
 PRM_SRVC_RTC ,	 
 PRM_SRVC_SSP1 ,	 
 PRM_SRVC_SSP2 ,	 
 PRM_SRVC_SSP3 ,	 
 PRM_SRVC_TIMER0_13M ,	 
 PRM_SRVC_TIMER1_13M ,	 
 PRM_SRVC_TIMER2_13M_GB ,	 
 PRM_SRVC_TIMER3_13M_GB ,	 
 PRM_SRVC_VCTCXO ,	 
 PRM_SRVC_UART1 ,	 
 PRM_SRVC_USIM ,	 
 PRM_SRVC_WB_CIPHER_GB , // DTC	 
 PRM_SRVC_USIM2 ,	 
 /*should be deleted for wujing */	 
 PRM_SRVC_CPA_DDR_HPerf , // Seagull - DDR Request from Harbell ( calls PRM_SRVC_MC_DDR_HPerf if needed )	 
 PRM_SRVC_AIRQ , // Seagull	 
 PRM_SRVC_COMM_IPC , // Seagull	 
 PRM_SRVC_RESOURCE_IPC , // Seagull	 
 PRM_SRVC_AXI_CFG , // Seagull	 
 PRM_SRVC_ETB , // Seagull	 
 PRM_SRVC_DTC , // Seagull	 
 PRM_SRVC_TCU_CTRL , // Seagull	 
 PRM_SRVC_ABP_BUS , // Seagull	 
 PRM_SRVC_AXI_BUS , // Seagull	 
 PRM_LAST_SERVICE=PRM_SRVC_AXI_BUS , // Always update this field.	 
 PRM_NUM_OF_SRVCS ,	 
 PRM_SRVC_NOT_AVAILABLE ,	 
 PRM_SRVC_MC_DDR_HPerf = PRM_SRVC_NOT_AVAILABLE	 
	 
 } PRM_ServiceE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_WU_SRVC_TIMER , // Harbell , BRN ( relevant for RTOS )	 
 PRM_WU_SRVC_SSP , // Harbell	 
 PRM_WU_SRVC_SCK , // Harbell	 
 PRM_WU_SRVC_WB_SLEEP_MODULE , // Harbell	 
 PRM_WU_SRVC_TD_SLEEP_MODULE = PRM_WU_SRVC_WB_SLEEP_MODULE ,	 
 PRM_WU_SRVC_LTE_SLEEP_MODULE , // Harbell	 
 PRM_WU_SRVC_TD_LTE_SLEEP_MODULE ,	 
 PRM_WU_SRVC_TCU , // Harbell	 
 PRM_WU_SRVC_UART , // Harbell , ( BRN via GPIO ( relevant for RTOS ) )	 
 PRM_WU_SRVC_AC_IPC , // Harbell , BRN ( relevant for RTOS )	 
 PRM_WU_SRVC_RTC , // BRN	 
 PRM_WU_SRVC_ROTARY , // BRN	 
 PRM_WU_SRVC_USB20_CLIENT , // BRN - Do we need to USB events or not?	 
 PRM_WU_SRVC_USB_OTGP2 , // BRN - Tx , P2 , P3 ( 3 diferent wakeups )	 
 PRM_WU_SRVC_USB_OTGP3 , // BRN - Tx , P2 , P3 ( 3 diferent wakeups )	 
 PRM_WU_SRVC_KEYPAD , // BRN	 
 PRM_WU_SRVC_USIM , // BRN	 
 PRM_WU_SRVC_USB_OTGTX , // BRN - Tx , P2 , P3 ( 3 diferent wakeups )	 
 PRM_WU_SRVC_GPIO , // BRN ( relevant for RTOS )	 
 PRM_WU_SRVC_COMM_WDT , // BRN	 
 PRM_WU_SRVC_AC97 , // BRN ored with BSSP wakeup	 
 PRM_WU_SRVC_CI2C , // BRN	 
 PRM_WU_SRVC_MMC1 , // BRN	 
 PRM_WU_SRVC_SDIO1 , // BRN	 
 PRM_WU_SRVC_MMC2 , // BRN	 
 PRM_WU_SRVC_SDIO2 , // BRN	 
 PRM_WU_SRVC_NAND , // BRN	 
 PRM_WU_SRVC_PMIC , // BRN ( relevant for RTOS )	 
 PRM_WU_BTUART , // BRN	 
 PRM_WU_STUART , // BRN	 
 PRM_WU_SRVC_ICP , // BRN - In A0 is ored with UARTs wakeup	 
 PRM_WU_SRVC_KEYPAD_ROTARY , // BRN	 
 PRM_WU_SRVC_KEYPAD_DIRECT_KEYS , // BRN	 
 PRM_WU_SRVC_EXTERNAL_EVENT0 , // BRN - Special case - Driver not defined	 
 PRM_WU_SRVC_EXTERNAL_EVENT1 , // BRN - Special case - Driver not defined	 
 PRM_WU_SRVC_BSSP1 , // BRN	 
 PRM_WU_SRVC_BSSP2 , // BRN	 
 PRM_WU_SRVC_BSSP3 , // BRN	 
 PRM_WU_SRVC_BSSP4 , // BRN	 
	 
 PRM_NUM_OF_WU_SRVCS ,	 
 PRM_ORED_INT_MSL0 , // For BRM B0	 
 PRM_WU_INVALID_RSRC	 
 } PRM_WU_ServiceE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_NONRETAINED_SRVC_INTC = 0 , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_TIMER , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_SSP , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_DMA , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_I2C , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_WDT , // Harbell , BRN ( ? )	 
 PRM_NONRETAINED_SRVC_IPC , // Harbell	 
 PRM_NONRETAINED_SRVC_USIM , // Harbell	 
 PRM_NONRETAINED_SRVC_PMIC , // Harbell	 
 PRM_NONRETAINED_SRVC_MSL , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_SCK , // Harbell	 
 PRM_NONRETAINED_SRVC_WB_SLEEP_MODULE , // Harbell	 
 PRM_NONRETAINED_SRVC_LTE_SLEEP_MODULE , // Harbell	 
 PRM_NONRETAINED_SRVC_TD_LTE_SLEEP_MODULE , // Harbell	 
 PRM_NONRETAINED_SRVC_TCU , // Harbell	 
 PRM_NONRETAINED_SRVC_UART , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_HSI ,	 
 PRM_NONRETAINED_SRVC_GPIO , // BRN	 
 PRM_NONRETAINED_SRVC_USB20 , // BRN	 
 PRM_NONRETAINED_SRVC_UDC , // BRN	 
 PRM_NONRETAINED_SRVC_LCD , // BRN	 
 PRM_NONRETAINED_SRVC_DTC , // Seagull	 
 PRM_NONRETAINED_SRVC_PMNC , // Seagull	 
	 
 PRM_NUM_OF_NONRETAINED_SRVCS ,	 
 PRM_INVALID_NONRETAINED	 
	 
	 
 } PRM_NRS_ServiceE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_FREQ_13MHZ = 0 ,	 
 PRM_FREQ_26MHZ ,	 
 PRM_FREQ_52MHZ ,	 
 PRM_FREQ_78MHZ ,	 
 PRM_FREQ_89_1MHZ ,	 
 PRM_FREQ_104MHZ ,	 
 PRM_FREQ_124_8MHZ ,	 
 PRM_FREQ_156MHZ ,	 
 PRM_FREQ_208MHZ ,	 
 PRM_FREQ_260MHZ ,	 
 PRM_FREQ_312MHZ ,	 
 PRM_NUM_OF_FREQS ,	 
 PRM_INVALID_FREQ	 
 } PRM_ServiceFreqE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_RSRC_FREE=0 ,	 
 PRM_RSRC_ALLOC	 
	 
 } PRM_AllocFreeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_RSRC_SC_FREE=1 , // resource is free , single client handling	 
 PRM_RSRC_SC_BUSY , // resource is busy , single client handling	 
 PRM_RSRC_MC_FREE , // resource is free , multi client handling	 
 PRM_RSRC_MC_BUSY , // resource is busy , multi client handling	 
 PRM_RSRC_NOT_DEFINED // resource is not defined	 
 // in this plat / sub-system	 
 } PRM_resourceStatusE;

typedef void ( *PRM_CallbackFuncWakeupT ) ( PM_PowerStatesE sleepstate , PM_PowerStatesE WUState , BOOL b_DDR_ready , BOOL b_RegsRetainedState ) ;
typedef void ( *PRM_CallbackFuncPrepareT ) ( PM_PowerStatesE statetoprepare ) ;
typedef void ( *PRM_CallbackFuncRecoverT ) ( PM_PowerStatesE stateexited , BOOL b_DDR_ready , BOOL b_RegsRetainedState ) ;
typedef void ( *PRM_CallbackFuncBeforeIntT ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 BOOL auto_baud ; /* enable auto_baud , auto-baud-rate detection within the UART ( default - FALSE ) */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR , /* -85 - Illegal IrDA configuration */	 
 UART_RC_TX_DMA_ERR /* -84 - DMA TX Error */	 
 } UART_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_DUMP_ENABLE = 0x1 ,	 
 RTI_TASK_ENABLE = 0x2 ,	 
 RTI_MIPS_ENABLE = 0x3	 
 } RTI_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 } Log_ConfigS;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *UsbLogPrint_t ) ( const char * , ... ) ;
typedef void ( *IPCCommNotifyMessageReceived ) ( UINT16 , UINT16 , UINT8 * ) ;
typedef void ( *IPCCommNotifyDataReceived ) ( IPC_DataStructReceived * , IPC_CmdMsgParams * ) ;
typedef void ( *IPCCommNotifyDataBufferFree ) ( UINT32 * , IPC_DataChannelNumber ) ;
typedef void ( *IPCCommNotifyDataChannelFree ) ( IPC_DataChannelNumber ) ;
typedef IPC_ReturnCode ( *IPCCommGetDataPointer ) ( UINT32 ** , UINT16 , IPC_DataChannelNumber , IPC_CmdMsgParams* ) ;
typedef void ( *IPCCommNotifySelfEventReceived ) ( UINT32 MessageParam ) ;
typedef void ( *IPCCommSpyCommandNotification ) ( UINT16 , UINT16 , UINT8* , SpyCmdData* ) ;
typedef void ( *IPCErrorIndicationCallBack ) ( IPC_ERROR_INDICATION * ) ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
DIAG_FILTER ( AUDIO , RAT , audioBindGetRAT , IAG_INFORMATION)  
 diagPrintf ( " audioBindGetRAT:0x%lx " , audioGetRAT );

DIAG_FILTER ( AUDIO , ACM , poc_resample_init , DIAG_INFORMATION)  
 diagPrintf ( " pocRes:0x%lx , sr:0x%d , dir:0x%lx , ifDspRate:0x%d " , 
 pocRes , sr , dir , ifDspRate );

DIAG_FILTER ( AUDIO , ACM , poc_resample_init_params_error , DIAG_INFORMATION)  
 diagPrintf ( " pocRes:0x%lx " , pocRes );

DIAG_FILTER ( AUDIO , ACM , poc_resample_init_inuse , DIAG_INFORMATION)  
 diagPrintf ( " pocRes->res_on:0x%lx , pocRes->sampleRate:0x%lx , pocRes->len:0x%lx , pocRes->buf:0x%lx " , 
 pocRes->res_on , pocRes->sampleRate , pocRes->len , pocRes->buf );

DIAG_FILTER ( AUDIO , ACM , poc_resample_init_malloc_failed , DIAG_INFORMATION)  
 diagPrintf ( " poc_resample_init_malloc_failed " );

DIAG_FILTER ( AUDIO , ACM , poc_resample_destory , DIAG_INFORMATION)  
 diagPrintf ( " pocRes:0x%lx , pocRes->res_on:0x%lx , pocRes->buf:0x%lx " , 
 pocRes , pocRes->res_on , pocRes->buf );

DIAG_FILTER ( AUDIO , ACM , setPOCBindType , DIAG_INFORMATION)  
 diagPrintf ( " setPOCBindType to:%x " , AudioPOCBindType );

DIAG_FILTER ( AUDIO , ACM , IsPocTxExist , DIAG_INFORMATION)  
 diagPrintf ( " POCVOICE_GET_TX_PCM to:0x%lx , POCVOICE_GET_TX_AMR to:0x%lx " , 
 audio_bind [ POCVOICE_GET_TX_PCM ] , audio_bind [ POCVOICE_GET_TX_AMR ] );

DIAG_FILTER ( AUDIO , ACM , IsPocRxExist , DIAG_INFORMATION)  
 diagPrintf ( " POCVOICE_GET_RX_PCM to:0x%lx , POCVOICE_GET_RX_AMR to:0x%lx , AudioPOC_DTMF to:0x%lx " , 
 audio_bind [ POCVOICE_GET_RX_PCM ] , audio_bind [ POCVOICE_GET_RX_AMR ] , AudioPOC_DTMF );

DIAG_FILTER ( AUDIO , ACM , AudioControl_TimeStamp , DIAG_INFORMATION)  
 diagPrintf ( " tickes:%lx , lastTickes:%lx , duration:%lx " , tickes , lastTickes , duration );

DIAG_FILTER ( AUDIO , ACM , AudioControl_TimeStamp_sleep , DIAG_INFORMATION)  
 diagPrintf ( " audio stop-->start too fast , need 10 ticks ( 50 ms ) for PMU and DSP tasks " );

DIAG_FILTER ( AUDIO , ACM , poc_bind_audio_path_check_OK , DIAG_ERROR)  
 diagPrintf ( " poc_bind_audio_path_check_OK " );

DIAG_FILTER ( AUDIO , HAL , POCVoice_DTMF_Button , DIAG_INFORMATION)  
 diagPrintf ( " ascii_pushbutton:0x%lx , start=%d , AudioPOC_DTMF=%d " , ascii_pushbutton , start , AudioPOC_DTMF );

DIAG_FILTER ( AUDIO , HAL , POCVoice_DTMF_Freq , DIAG_INFORMATION)  
 diagPrintf ( " f1:0x%lx , f2:0x%lx , start=%d , AudioPOC_DTMF=%d " , f1 , f2 , start , AudioPOC_DTMF );

DIAG_FILTER ( AUDIO , Voice , updatePOCStreamSetting , DIAG_ERROR)  
 diagPrintf ( " POCVoice_Stream_Setting = 0x%lx , POCVoice_rx_tx_cust_config = 0x%lx " , 
 POCVoice_Stream_Setting , POCVoice_rx_tx_cust_config );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMHandler , DIAG_INFORMATION)  
 diagPrintf ( " tx_cb:0x%lx , rx_cb:0x%lx " , tx_cb , rx_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindTxPCMHandler_inuse , DIAG_ERROR)  
 diagPrintf ( " POC already in used AudioPOCBindType = 0x%lx " , AudioPOCBindType );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCM_check_fail , DIAG_ERROR)  
 diagPrintf ( " POCVoice_BindPCM_check_fail " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindTxPCMHandler_Failed_tx_cb , DIAG_ERROR)  
 diagPrintf ( " tx_cb = 0x%lx , already bound cb = 0x%lx " , tx_cb , audio_bind [ POCVOICE_GET_TX_PCM ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindTxPCMHandler_Failed_rx_cb , DIAG_ERROR)  
 diagPrintf ( " rx_cb = 0x%lx , already bound cb = 0x%lx " , rx_cb , audio_bind [ POCVOICE_GET_RX_PCM ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindTxPCMHandler_OK , DIAG_INFORMATION)  
 diagPrintf ( " POCVOICE_GET_TX_PCM:0x%lx , POCVOICE_GET_RX_PCM:0x%lx " , 
 audio_bind [ POCVOICE_GET_TX_PCM ] , audio_bind [ POCVOICE_GET_RX_PCM ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMHandler , DIAG_INFORMATION)  
 diagPrintf ( " tx_cb:0x%lx , rx_cb:0x%lx " , tx_cb , rx_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindTxPCMHandler_Failed_tx_cb , DIAG_ERROR)  
 diagPrintf ( " tx_cb = 0x%lx , mismatch bound cb = 0x%lx " , tx_cb , audio_bind [ POCVOICE_GET_TX_PCM ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindTxPCMHandler_Failed_rx_cb , DIAG_ERROR)  
 diagPrintf ( " rx_cb = 0x%lx , mismatch bound cb = 0x%lx " , rx_cb , audio_bind [ POCVOICE_GET_RX_PCM ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindTxPCMHandler_OK , DIAG_INFORMATION)  
 diagPrintf ( " POCVOICE_GET_TX_PCM:0x%lx , POCVOICE_GET_RX_PCM:0x%lx " , 
 audio_bind [ POCVOICE_GET_TX_PCM ] , audio_bind [ POCVOICE_GET_RX_PCM ] );

DIAG_FILTER ( AUDIO , Voice , POCVoice_handleTxPCM_data , DIAG_INFORMATION)  
 diagStructPrintf ( " " , ( void* ) g_PCMTxBuff , len );

DIAG_FILTER ( AUDIO , ACM , POCVoice_handleTxPCM_need_res , DIAG_INFORMATION)  
 diagPrintf ( " needRate:%d , realDspRate:%d , poc_tx_res.dspRate:%d " , 
 needRate , realDspRate , poc_tx_res.dspRate );

DIAG_FILTER ( AUDIO , ACM , POCVoice_handleTxPCM_init_res_fail , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_record_res_init_failed " );

DIAG_FILTER ( AUDIO , Voice , POCVoice_handleTxPCM_res_data , DIAG_INFORMATION)  
 diagStructPrintf ( " " , ( void* ) poc_tx_res.buf , poc_tx_res.len );

DIAG_FILTER ( AUDIO , Voice , POCVoice_handleTxPCM_end , DIAG_INFORMATION)  
 diagPrintf ( " audio_bind [ POCVOICE_GET_TX_PCM ] : 0x%lx " , audio_bind [ POCVOICE_GET_TX_PCM ] );

DIAG_FILTER ( AUDIO , POC , poc_play_handler , DIAG_INFORMATION)  
 diagPrintf ( " g_pocPlay [ %d ] .cb = 0x%lx , g_pocPlay [ %d ] .resPlay.buf = 0x%lx " , 
 i , g_pocPlay [ i ] .cb , i , g_pocPlay [ i ] .resPlay.buf );

DIAG_FILTER ( AUDIO , POC , poc_record_handler , DIAG_INFORMATION)  
 diagPrintf ( " poc_record.buf = 0x%lx , poc_record.len = %d " , poc_record.buf , poc_record.len );

DIAG_FILTER ( AUDIO , POC , record_handler_reinit_res , DIAG_INFORMATION)  
 diagPrintf ( " realDspRate = 0x%d , poc_record.dspRate = %d " , 
 realDspRate , poc_record.dspRate );

DIAG_FILTER ( AUDIO , ACM , poc_record_handler_reinit_res_fail , DIAG_INFORMATION)  
 diagPrintf ( " poc_record_handler_reinit_resample_fail " );

DIAG_FILTER ( AUDIO , Voice , poc_record_handler_data , DIAG_INFORMATION)  
 diagStructPrintf ( " " , ( void* ) poc_record.buf , poc_record.len );

DIAG_FILTER ( AUDIO , ACM , updateFarEnd , DIAG_INFORMATION)  
 diagPrintf ( " g_pocPlay [ %d ] .farEnd:%d " , slot , g_pocPlay [ slot ] .farEnd );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMPlay , DIAG_INFORMATION)  
 diagPrintf ( " sampleRate:0x%lx , pcm2FarEnd:%d , play_cb:0x%lx " , 
 config->sampleRate , config->pcm2FarEnd , play_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMPlay_cb_inuse , DIAG_ERROR)  
 diagPrintf ( " slot:%d " , slot );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMPlay_inuse , DIAG_ERROR)  
 diagPrintf ( " POCVoice_BindPCMPlay_all_slots_inuse " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_play_res_init_failed , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_play_res_init_failed " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMPlay_failed , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_BindPCMPlay_failed " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMPlay_OK , DIAG_INFORMATION)  
 diagPrintf ( " play_cb:0x%lx , g_pocPlay [ %d ] .cb:0x%lx , pocPlay_actived_cnt:0x%d " , 
 play_cb , slot , g_pocPlay [ slot ] .cb , pocPlay_actived_cnt );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMPlay , DIAG_INFORMATION)  
 diagPrintf ( " play_cb:0x%lx " , play_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMPlay_failed , DIAG_ERROR)  
 diagPrintf ( " play_cb:0x%lx , there no one in g_pocPlay [ slot ] .cb " , play_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMPlay_OK , DIAG_INFORMATION)  
 diagPrintf ( " play_cb:0x%lx , g_pocPlay [ %d ] .cb:0x%lx " , play_cb , slot , g_pocPlay [ slot ] .cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMPlay_failed_2 , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_UnBindPCMPlay_failed_2 " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMRecord , DIAG_INFORMATION)  
 diagPrintf ( " sampleRate:0x%lx , record_cb:0x%lx , g_pocRecord_cb:0x%lx " , 
 config->sampleRate , record_cb , g_pocRecord_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMRecord_inuse , DIAG_ERROR)  
 diagPrintf ( " POCVoice_BindPCMRecord_inuse " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_record_res_init_failed , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_record_res_init_failed " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMRecord_failed , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_BindPCMRecord_failed " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindPCMRecord_OK , DIAG_INFORMATION)  
 diagPrintf ( " record_cb:0x%lx , g_pocRecord_cb:0x%lx " , record_cb , g_pocRecord_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMRecord , DIAG_INFORMATION)  
 diagPrintf ( " record_cb:0x%lx , g_pocRecord_cb:0x%lx " , record_cb , g_pocRecord_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMRecord_failed , DIAG_ERROR)  
 diagPrintf ( " record_cb:0x%lx mismatch g_pocRecord_cb:0x%lx " , record_cb , g_pocRecord_cb );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMRecord_OK , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_UnBindPCMRecord_OK " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindPCMRecord_failed , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_UnBindPCMRecord_failed " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_ConfigPlay2FarEnd , DIAG_INFORMATION)  
 diagPrintf ( " nearCodec:0x%lx , substitute:0x%lx , POCVoice_rx_tx_cust_config:0x%lx " , 
 nearCodec , substitute , POCVoice_rx_tx_cust_config );

DIAG_FILTER ( AUDIO , ACM , POCVoice_ConfigRecord , DIAG_INFORMATION)  
 diagPrintf ( " nearCodec:0x%lx , POCVoice_record_nearCodec:0x%lx " , 
 nearCodec , POCVoice_record_nearCodec );

DIAG_FILTER ( AUDIO , ACM , POCVoice_ConfigPlay2NearEnd , DIAG_INFORMATION)  
 diagPrintf ( " nearCodec:0x%lx , substitute:0x%lx , POCVoice_rx_tx_cust_config:0x%lx " , 
 nearCodec , substitute , POCVoice_rx_tx_cust_config );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindAMRHandlers , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_BindAMRHandlers " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindAMRHandlers_inuse , DIAG_ERROR)  
 diagPrintf ( " POC already in used AudioPOCBindType = 0x%lx " , AudioPOCBindType );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindAMR_check_fail , DIAG_ERROR)  
 diagPrintf ( " POCVoice_BindAMR_check_fail " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindAMRHandlers_OK , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_BindAMRHandlers OK " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_BindAMRHandlers_Failed , DIAG_ERROR)  
 diagPrintf ( " POCVoice_BindAMRHandlers failed , cb_tx = 0x%lx , cb_rx = 0x%lx , bound cb_tx = 0x%lx , cb_rx = 0x%lx " , 
 cb_tx , cb_rx , audio_bind [ POCVOICE_GET_TX_AMR ] , audio_bind [ POCVOICE_GET_RX_AMR ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindAMRHandlers , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_UnBindAMRHandlers " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_POCVoice_UnBindAMRHandlers_OK , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_UnBindAMRHandlers OK " );

DIAG_FILTER ( AUDIO , ACM , POCVoice_POCVoice_UnBindAMRHandlers_Failed , DIAG_ERROR)  
 diagPrintf ( " mismatch cb_tx = 0x%lx , cb_rx = 0x%lx , already bound cb_tx = 0x%lx , cb_rx = 0x%lx " , 
 cb_tx , cb_rx , audio_bind [ POCVOICE_GET_TX_AMR ] , audio_bind [ POCVOICE_GET_RX_AMR ] );

DIAG_FILTER ( AUDIO , ACM , POCVoice_ConfigureAMR , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_ConfigureAMR , Band:%d , Rate:%d , Format:%d " , Band , Rate , Format );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_SWITCH_NB 
 void POCVoice_SWITCH_NB ( ) {	 
 band ^= 1 ;	 
 POCVoice_ConfigureAMR ( band , rate , format ) ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_SWITCH_RATE 
 void POCVoice_SWITCH_RATE ( ) {	 
 if ( rate == 0 )	 
 rate = 7 ;	 
 else	 
 rate = 0 ;	 
 POCVoice_ConfigureAMR ( band , rate , format ) ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_SWITCH_FORMAT 
 void POCVoice_SWITCH_FORMAT ( ) {	 
 format ^= 1 ;	 
 POCVoice_ConfigureAMR ( band , rate , format ) ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_UnBindAny 
 void POCVoice_UnBindAny ( void ) 
 {	 
 void* cb_pcm_tx = 0 ;	 
 void* cb_pcm_rx = 0 ;	 
 void* cb_amr_tx = 0 ;	 
 void* cb_amr_rx = 0 ;	 
 int i = 0 ;	 
	 
DIAG_FILTER ( AUDIO , ACM , POCVoice_UnBindAny , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_UnBindAny " );

	 
 for ( i =0 ; i < 2 ; i++ ) {		 
 if ( g_pocPlay [ i ] .cb ) {			 
 POCVoice_UnBindPCMPlay ( g_pocPlay [ i ] .cb ) ;			 
 }		 
 }	 
 if ( g_pocRecord_cb ) {		 
 POCVoice_UnBindPCMRecord ( g_pocRecord_cb ) ;		 
 }	 
	 
 cb_pcm_tx = audio_bind [ POCVOICE_GET_TX_PCM ] ;	 
 cb_pcm_rx = audio_bind [ POCVOICE_GET_RX_PCM ] ;	 
 cb_amr_tx = audio_bind [ POCVOICE_GET_TX_AMR ] ;	 
 cb_amr_rx = audio_bind [ POCVOICE_GET_RX_AMR ] ;	 
 if ( cb_pcm_tx || cb_pcm_rx ) {		 
 POCVoice_UnBindPCMHandler ( ( POCTxPCMHandler_t ) cb_pcm_tx , ( POCRxPCMHandler_t ) cb_pcm_rx ) ;		 
 } else if ( cb_amr_tx && cb_amr_rx ) {		 
 POCVoice_UnBindAMRHandlers ( ( POCTxAMRHandler_t ) cb_amr_tx , ( POCRxAMRHandler_t ) cb_amr_rx ) ;		 
 }	 
	 
	 
 if ( 1 == AudioPOC_DTMF ) {		 
 POCVoice_DTMF_Button ( 0 , 0 ) ;		 
		 
 }	 
	 
 if ( 2 == AudioPOC_DTMF ) {		 
 POCVoice_DTMF_Freq ( 0 , 0 , 0 ) ;		 
 }	 
 return ;	 
 }

DIAG_FILTER ( AUDIO , ACM , AcmSetPCMRate_fail , DIAG_INFORMATION)  
 diagPrintf ( " wb:%d , cannot change PCM Rate during POC audio runing " , wb );

DIAG_FILTER ( AUDIO , ACM , AcmSetPCMRate , DIAG_INFORMATION)  
 diagPrintf ( " wb:%d , Audio_WB_AMR_Enable:%d " , wb , Audio_WB_AMR_Enable );

DIAG_FILTER ( AUDIO , ACM , OEMAudio_singleToneDetectConfigure_faile , DIAG_ERROR)  
 diagPrintf ( " OEMAudio_singleToneDetectConfigure_faile " );

DIAG_FILTER ( AUDIO , ACM , OEMAudio_singleToneDetectConfigure , DIAG_INFORMATION)  
 diagPrintf ( " Enable:%d , DecFreqNum:%d , N:%d , FreqCoff [ 0 ] :%d , FreqCoff [ 1 ] :%d , FreqCoff [ 2 ] :%d , FreqCoff [ 3 ] :%d , OEMAudio_singleToneStreamType:%d " , 
 OEMAudio_singleToneDetect.Enable , 
 OEMAudio_singleToneDetect.DecFreqNum , 
 OEMAudio_singleToneDetect.N , 
 OEMAudio_singleToneDetect.FreqCoff [ 0 ] , 
 OEMAudio_singleToneDetect.FreqCoff [ 1 ] , 
 OEMAudio_singleToneDetect.FreqCoff [ 2 ] , 
 OEMAudio_singleToneDetect.FreqCoff [ 3 ] , 
 OEMAudio_singleToneStreamType );

DIAG_FILTER ( AUDIO , ACM , UnBindGetDTMFHandler_Failed_1 , DIAG_ERROR)  
 diagPrintf ( " OEMAudio_UnBindDTMFDetectionHandler Failed , Please check parameter! " );

DIAG_FILTER ( AUDIO , ACM , UnBindGetDTMFHandler_OK , DIAG_INFORMATION)  
 diagPrintf ( " OEMAudio_UnBindDTMFDetectionHandler OK " );

DIAG_FILTER ( AUDIO , ACM , UnBindGetDTMFHandler_Failed_2 , DIAG_ERROR)  
 diagPrintf ( " OEMAudio_UnBindDTMFDetectionHandler Failed , mismatch cb = 0x%lx , bound cb = 0x%lx " , cb , audio_bind [ AUDIO_GET_DTMF_CODE ] );

DIAG_FILTER ( AUDIO , ACM , BindGetDTMFHandler_Failed_1 , DIAG_ERROR)  
 diagPrintf ( " OEMAudio_BindDTMFDetectionHandler Failed , Please check parameter! " );

DIAG_FILTER ( AUDIO , ACM , BindGetDTMFHandler_OK , DIAG_INFORMATION)  
 diagPrintf ( " OEMAudio_BindDTMFDetectionHandler OK " );

DIAG_FILTER ( AUDIO , ACM , BindGetDTMFHandler_Failed_2 , DIAG_ERROR)  
 diagPrintf ( " OEMAudio_BindDTMFDetectionHandler Failed , cb = 0x%lx , already bound cb = 0x%lx " , cb , audio_bind [ AUDIO_GET_DTMF_CODE ] );

DIAG_FILTER ( AUDIO , ACM , voiceData_reample_process , DIAG_INFORMATION)  
 diagPrintf ( " voiceData_wb:0x%lx , voiceData_type:0x%lx , outReslen:%d , len:%d " , 
 voiceData_wb , voiceData_type , outReslen , len );

DIAG_FILTER ( AUDIO , ACM , voiceData_reample_init , DIAG_INFORMATION)  
 diagPrintf ( " voiceData_wb:0x%lx , voiceData_type:0x%lx " , voiceData_wb , voiceData_type );

DIAG_FILTER ( AUDIO , ACM , voiceDataRx_resmple_init_failed , DIAG_INFORMATION)  
 diagPrintf ( " voiceDataRx_resmple_init_failed " );

DIAG_FILTER ( AUDIO , ACM , voiceDataTx_resmple_init_failed , DIAG_INFORMATION)  
 diagPrintf ( " voiceDataTx_resmple_init_failed " );

DIAG_FILTER ( AUDIO , ACM , voiceDataRx_resmple_init_failed , DIAG_INFORMATION)  
 diagPrintf ( " voiceDataRx_resmple_init_failed " );

DIAG_FILTER ( AUDIO , ACM , voiceData_reample_init_OK , DIAG_INFORMATION)  
 diagPrintf ( " outSr:0x%lx , dspSr:0x%lx " , outSr , dspSr );

DIAG_FILTER ( AUDIO , ACM , voiceData_reample_destroy , DIAG_INFORMATION)  
 diagPrintf ( " voiceData_wb:0x%lx , voiceData_type:0x%lx " , voiceData_wb , voiceData_type );

DIAG_FILTER ( AUDIO , ACM , BindGetVoiceDataCallback_Failed_1 , DIAG_ERROR)  
 diagPrintf ( " %s failed , callback = 0x%lx " , __FUNCTION__ , cb );

DIAG_FILTER ( AUDIO , ACM , BindGetVoiceDataCallback_OK , DIAG_INFORMATION)  
 diagPrintf ( " %s OK , bind callback = 0x%lx " , __FUNCTION__ , cb );

DIAG_FILTER ( AUDIO , ACM , BindGetVoiceDataCallback_Failed_2 , DIAG_ERROR)  
 diagPrintf ( " %s failed , cb = 0x%lx , already bound cb = 0x%lx " , __FUNCTION__ , cb , audio_bind [ AUDIO_GET_VOICE_DATA ] );

DIAG_FILTER ( AUDIO , ACM , UnBindGetVoiceDataCallback_Failed_1 , DIAG_ERROR)  
 diagPrintf ( " %s failed , callback = 0x%lx " , __FUNCTION__ , cb );

DIAG_FILTER ( AUDIO , ACM , UnBindGetVoiceDataCallback_OK , DIAG_INFORMATION)  
 diagPrintf ( " %s OK , unbind callback = 0x%lx " , __FUNCTION__ , cb );

DIAG_FILTER ( AUDIO , ACM , UnBindGetVoiceDataCallback_Failed_2 , DIAG_ERROR)  
 diagPrintf ( " %s failed , mismatch cb = 0x%lx , bound cb = 0x%lx " , __FUNCTION__ , cb , audio_bind [ AUDIO_GET_VOICE_DATA ] );

DIAG_FILTER ( AUDIO , ACM , OEMAudio_SetVoiceData_fail , DIAG_INFORMATION)  
 diagPrintf ( " audio_bind [ AUDIO_GET_VOICE_DATA ] = 0x%lx " , audio_bind [ AUDIO_GET_VOICE_DATA ] );

DIAG_FILTER ( AUDIO , ACM , OEMAudio_SetVoiceData , DIAG_INFORMATION)  
 diagPrintf ( " wb = 0x%lx , VoiceType = 0x%lx , audio_bind [ AUDIO_GET_VOICE_DATA ] = 0x%lx " , 
 wb , VoiceType , audio_bind [ AUDIO_GET_VOICE_DATA ] );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , OEMTest_SetVoiceData 
 void OEMTest_SetVoiceData ( void *data ) 
 {	 
 unsigned int * pData = ( unsigned int * ) data ;	 
 unsigned int wb = 0 ;	 
 unsigned int VoiceType = 0 ;	 
 wb = *pData ;	 
 pData++ ;	 
 VoiceType = *pData ;	 
	 
DIAG_FILTER ( AUDIO , ACM , OEMTest_TestSetVoiceData , DIAG_INFORMATION)  
 diagPrintf ( " wb = 0x%lx , VoiceType = 0x%lx " , 
 wb , VoiceType );

	 
	 
 OEMAudio_SetVoiceData ( wb , VoiceType ) ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , audioStubReverseWBAMR 
 int audioStubReverseWBAMR ( ) 
 {	 
 if ( Audio_WB_AMR_Enable )	 
 AcmSetPCMRate ( 0 ) ;	 
 else	 
 AcmSetPCMRate ( 1 ) ;	 
	 
DIAG_FILTER ( AUDIO , StubServer , audioStubReverseWBAMR , DIAG_INFORMATION)  
 diagPrintf ( " Audio_WB_AMR_Enable:%d " , Audio_WB_AMR_Enable );

	 
 return Audio_WB_AMR_Enable ;	 
 }

DIAG_FILTER ( AUDIO , OEMTest , OEMTest_buff_init , DIAG_INFORMATION)  
 diagPrintf ( " OEMTest_PCMBuffer = 0x%lxd " , OEMTest_PCMBuffer );

DIAG_FILTER ( AUDIO , OEMTest , Loopback , DIAG_INFORMATION)  
 diagPrintf ( " pData = 0x%lx , Length = %d " , pData , Length );

DIAG_FILTER ( AUDIO , OEMTest , LoopbackDump , DIAG_INFORMATION)  
 diagStructPrintf ( " Tx PCM: " , ( void* ) pData , Length );

DIAG_FILTER ( AUDIO , OEMTest , OEMTest_LoopbackRxHandler , DIAG_INFORMATION)  
 diagPrintf ( " pData = 0x%lx , Length = %d " , pData , Length );

DIAG_FILTER ( AUDIO , OEMTest , OEMTest_LoopbackRxHandler_2 , DIAG_INFORMATION)  
 diagPrintf ( " pData = 0x%lx , Length = %d " , pData , Length );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , OEMTest_Loopback 
 void OEMTest_Loopback ( void *data ) 
 {	 
 char OnOff = * ( char * ) data ;	 
 if ( 0 != OEMTest_buff_init ( ) ) { return ; }	 
	 
 if ( OnOff == 0 )	 
 { // Remove the bound callback and MSA voice path will be stopped		 
 POCVoice_UnBindPCMHandler ( OEMTest_LoopbackTxHandler , OEMTest_LoopbackRxHandler ) ;		 
 }	 
 else	 
 { // Bind the callback and MSA voice path will be started		 
 POCVoice_BindPCMHandler ( OEMTest_LoopbackTxHandler , OEMTest_LoopbackRxHandler ) ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , OEMTest_Loopback_EX 
 void OEMTest_Loopback_EX ( void *data ) 
 {	 
 static char OnOff = 0 ;	 
 if ( 0 != OEMTest_buff_init ( ) ) { return ; }	 
	 
 OnOff = ( OnOff + 1 ) % 2 ;	 
 if ( OnOff == 0 )	 
 { // Remove the bound callback and MSA voice path will be stopped		 
 POCVoice_UnBindPCMHandler ( OEMTest_LoopbackTxHandler , OEMTest_LoopbackRxHandler ) ;		 
 }	 
 else	 
 { // Bind the callback and MSA voice path will be started		 
 POCVoice_BindPCMHandler ( OEMTest_LoopbackTxHandler , OEMTest_LoopbackRxHandler ) ;		 
 }	 
	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , OEMTest_Loopback_EX2 
 void OEMTest_Loopback_EX2 ( void *data ) 
 {	 
 static char OnOff = 0 ;	 
 PocPcmConfig config = { 0 } ;	 
 config.sampleRate = 8000 ;	 
 // config.sampleRate = 44100 ;	 
 // config.sampleRate = 16000 ;	 
	 
 if ( 0 != OEMTest_buff_init ( ) ) { return ; }	 
	 
 OnOff = ( OnOff + 1 ) % 4 ;	 
 if ( 1 == OnOff ) {		 
 POCVoice_BindPCMRecord ( &config , OEMTest_LoopbackTxHandler ) ;		 
 } else if ( 2 == OnOff ) {		 
 POCVoice_BindPCMPlay ( &config , OEMTest_LoopbackRxHandler ) ;		 
 // POCVoice_BindPCMPlay ( &config , OEMTest_LoopbackRxHandler_2 ) ;		 
 } else if ( 3 == OnOff ) {		 
 // POCVoice_UnBindPCMPlay ( OEMTest_LoopbackRxHandler_2 ) ;		 
 POCVoice_UnBindPCMPlay ( OEMTest_LoopbackRxHandler ) ;		 
 } else if ( 0 == OnOff ) {		 
 POCVoice_UnBindPCMRecord ( OEMTest_LoopbackTxHandler ) ;		 
 }	 
	 
 }

DIAG_FILTER ( AUDIO , OEMTest , OEMTest_AMRTxHandler , DIAG_INFORMATION)  
 diagPrintf ( " frame = 0x%lx , len = %d " , frame , len );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , OEMTest_AMR_nodata 
 void OEMTest_AMR_nodata ( void *data ) 
 {	 
 test_amr_nodata = ( test_amr_nodata + 1 ) % 2 ;	 
 return ;	 
 }

DIAG_FILTER ( AUDIO , OEMTest , OEMTest_AMRRxHandler , DIAG_INFORMATION)  
 diagPrintf ( " frame = 0x%lx , len = %d " , frame , len );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , OEMTest_AMR_Loopback 
 void OEMTest_AMR_Loopback ( void *data ) 
 {	 
 static char OnOff = 0 ;	 
 if ( 0 != OEMTest_buff_init ( ) ) { return ; }	 
	 
 OnOff = ( OnOff + 1 ) % 2 ;	 
 if ( OnOff == 0 )	 
 { // Remove the bound callback and MSA voice path will be stopped		 
 POCVoice_UnBindAMRHandlers ( OEMTest_AMRTxHandler , OEMTest_AMRRxHandler ) ;		 
 }	 
 else	 
 { // Bind the callback and MSA voice path will be started		 
 POCVoice_BindAMRHandlers ( OEMTest_AMRTxHandler , OEMTest_AMRRxHandler ) ;		 
 }	 
 }

DIAG_FILTER ( AUDIO , OEMTest , OEMTest_DTMFDetectionHandler , DIAG_INFORMATION)  
 diagPrintf ( " %s:DTMF code is %c " , __FUNCTION__ , DTMFCode );

DIAG_FILTER ( AUDIO , OEMTest , Audio_production_mode_CBTX , DIAG_INFORMATION)  
 diagPrintf ( " pData = 0x%lx , Length = %d " , pData , Length );

DIAG_FILTER ( AUDIO , OEMTest , Audio_production_mode_CBRX_NULL , DIAG_INFORMATION)  
 diagPrintf ( " pData = 0x%lx , Length = %d " , pData , Length );

DIAG_FILTER ( AUDIO , OEMTest , Audio_production_mode_DetectionHandler , DIAG_INFORMATION)  
 diagPrintf ( " DTMF code is 0x%x , send:%d , detect:%d " , 
 DTMFCode , 
 audioProductionSendCnt , 
 audioProductionDetectCnt );

DIAG_FILTER ( AUDIO , OEMTest , Audio_production_result_get , DIAG_INFORMATION)  
 diagPrintf ( " audioProductionDetectCnt=%d " , audioProductionDetectCnt );

DIAG_FILTER ( AUDIO , OEMTest , Audio_production_mode_ctl , DIAG_INFORMATION)  
 diagPrintf ( " OnOff=%d " , OnOff );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , Audio_production_mode_test 
 void Audio_production_mode_test ( void ) 
 {	 
 static int OnOff = 0 ;	 
 OnOff++ ;	 
 OnOff %= 2 ;	 
	 
DIAG_FILTER ( AUDIO , OEMTest , Audio_production_mode_test , DIAG_INFORMATION)  
 diagPrintf ( " OnOff=%d " , OnOff );

	 
 Audio_production_mode_ctl ( OnOff ) ;	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , Audio_production_result_get_test 
 void Audio_production_result_get_test ( void ) 
 {	 
 unsigned int ret = 0 ;	 
 ret = Audio_production_result_get ( 0 ) ;	 
	 
DIAG_FILTER ( AUDIO , OEMTest , Audio_production_result_get_test , DIAG_INFORMATION)  
 diagPrintf ( " ret=%d " , ret );

	 
	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , DTMFDetectionDemo 
 void OEMTest_DTMFDetectionDemo ( void *data ) 
 {	 
 char OnOff = * ( char * ) data ;	 
 DTMFDetectionControl_t DTMFDetectionControl ;	 
	 
 ToneDecStateForCustomerStruct dtmf_cp ;	 
 dtmf_cp.Enable = 1 ;	 
 dtmf_cp.DecFreqNum = 2 ;	 
 dtmf_cp.FreqCoff [ 0 ] = -7557 ; // 2300 Hz	 
 dtmf_cp.FreqCoff [ 1 ] = 14706 ; // 1400 Hz	 
 dtmf_cp.N = 108 ;	 
	 
 memset ( &DTMFDetectionControl , 0x00 , sizeof ( DTMFDetectionControl ) ) ;	 
	 
 if ( OnOff == 0 )	 
 { // Remove the bound callback and close the function of DTMF detection		 
 DTMFDetectionControl.dtmfType = 1 ;		 
 DTMFDetectionControl.onoff = 0 ; // Must be set to AUDIO_DTMF_OFF		 
		 
 OEMAudio_UnBindDTMFDetectionHandler ( OEMTest_DTMFDetectionHandler , &DTMFDetectionControl ) ;		 
		 
 dtmf_cp.Enable = 0 ;		 
 OEMAudio_singleToneDetectConfigure ( &dtmf_cp , 1 ) ;		 
 }	 
 else	 
 { // Bind the callback and open the function of DTMF detection		 
 DTMFDetectionControl.dtmfType = 1 ;		 
 DTMFDetectionControl.onoff = 1 ; // Must be set to AUDIO_DTMF_ON		 
 DTMFDetectionControl.dialToneToOthersTones = 0x19 ;		 
 DTMFDetectionControl.dialTonesToOthersDialTones = 4 ;		 
 DTMFDetectionControl.dialVadDuration = 3 ;		 
		 
 OEMAudio_BindDTMFDetectionHandler ( OEMTest_DTMFDetectionHandler , &DTMFDetectionControl ) ;		 
		 
 OEMAudio_singleToneDetectConfigure ( &dtmf_cp , 1 ) ;		 
		 
 }	 
	 
 return ;	 
 }

DIAG_FILTER ( AUDIO , ACM , GetVoiceDataCallback , DIAG_INFORMATION)  
 diagPrintf ( " PCM count = %ld , length = %d ( short ) , " , count , length );

DIAG_FILTER ( AUDIO , ACM , SendPCMToAP , DIAG_INFORMATION)  
 diagStructPrintf ( " " , mixed , length * sizeof ( short ) );

DIAG_FILTER ( AUDIO , ACM , getVoiceRx , DIAG_INFORMATION)  
 diagStructPrintf ( " " , rx , length * sizeof ( short ) );

DIAG_FILTER ( AUDIO , ACM , getVoiceTx , DIAG_INFORMATION)  
 diagStructPrintf ( " " , tx , length * sizeof ( short ) );

DIAG_FILTER ( AUDIO , ACM , getVoiceMix , DIAG_INFORMATION)  
 diagStructPrintf ( " " , mixed , length * sizeof ( short ) );

//ICAT EXPORTED FUNCTION - Audio , OEMTest , GetVoiceDataDemo 
 void OEMTest_GetVoiceDataDemo ( void *data ) 
 {	 
 char OnOff = * ( char * ) data ;	 
	 
 if ( OnOff == 1 )	 
 OEMAudio_BindGetVoiceDataCallback ( Test_GetVoiceDataCallback ) ;	 
 else	 
 OEMAudio_UnBindGetVoiceDataCallback ( Test_GetVoiceDataCallback ) ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_tone_test 
 void POCVoice_tone_test ( void* data ) 
 {	 
 static int start = 0 ;	 
	 
 start++ ;	 
 start %= 2 ;	 
DIAG_FILTER ( AUDIO , HAL , POCVoice_tone_test , DIAG_INFORMATION)  
 diagPrintf ( " POCVoice_tone_test " );

	 
 POCVoice_DTMF_Button ( start , 48 ) ;	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_DTMF_test 
 void POCVoice_DTMF_test ( void ) 
 {	 
 static BOOL start = 1 ;	 
 int f1 = 200 ;	 
 int f2 = 2000 ;	 
	 
DIAG_FILTER ( AUDIO , HAL , POCVoice_DTMF_test , DIAG_INFORMATION)  
 diagPrintf ( " start=%u , f1=%u , f2=%u " , start , f1 , f2 );

	 
 POCVoice_DTMF_Freq ( start , f1 , f2 ) ;	 
 start=start+1 ;	 
 start=start%2 ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_DTMF_test2 
 void POCVoice_DTMF_test2 ( void* data ) 
 {	 
 static BOOL start = 1 ;	 
 int *pdata = data ;	 
 int f1 = *pdata ;	 
 int f2 = * ( pdata + 1 ) ;	 
	 
DIAG_FILTER ( AUDIO , HAL , POCVoice_DTMF_test2 , DIAG_INFORMATION)  
 diagPrintf ( " start=%u , f1=%u , f2=%u " , start , f1 , f2 );

	 
 POCVoice_DTMF_Freq ( start , f1 , f2 ) ;	 
 start=start+1 ;	 
 start=start%2 ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_PCMPlayConfig 
 void POCVoice_PCMPlayConfig ( void* data ) 
 {	 
 unsigned int *pdata = data ;	 
 unsigned int farEnd = *pdata ;	 
 unsigned int nearCodec = * ( pdata + 1 ) ;	 
 unsigned int substitute = * ( pdata + 2 ) ;	 
	 
DIAG_FILTER ( AUDIO , HAL , POCVoice_PCMPlayConfig , DIAG_INFORMATION)  
 diagPrintf ( " farEnd=%u , nearCodec=%u , substitute=%u " , farEnd , nearCodec , substitute );

	 
 if ( farEnd ) { POCVoice_ConfigPlay2FarEnd ( nearCodec , substitute ) ; }	 
 else { POCVoice_ConfigPlay2NearEnd ( nearCodec , substitute ) ; }	 
 }

//ICAT EXPORTED FUNCTION - Audio , OEMTest , POCVoice_ConfigRecod_switch 
 void POCVoice_ConfigRecod_switch ( void ) 
 {	 
 static int nearCodec = 0 ;	 
 nearCodec++ ;	 
 nearCodec %= 2 ;	 
	 
DIAG_FILTER ( AUDIO , HAL , POCVoice_ConfigRecod_switch , DIAG_INFORMATION)  
 diagPrintf ( " nearCodec=0x%lx " , nearCodec );

	 
 POCVoice_ConfigRecord ( nearCodec ) ;	 
 }

