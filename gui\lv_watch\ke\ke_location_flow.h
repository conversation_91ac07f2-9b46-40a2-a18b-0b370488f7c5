#ifndef __KE_LOCATION_FLOW_H
#define __KE_LOCATION_FLOW_H

#include "lv_conf.h"
#include "lv_watch_conf.h"

#include "../modem/mmi_modem_adaptor_main.h"
#include "../ws/mmi_modem_adaptor_ws.h"
#include "../ws/mmi_modem_adaptor_ws_com.h"

#define WIFI_UP_MIN_CNT 		3 //WIFI上报的最小数
#define NO_GPS_WIFI_STEP_CNT 	400 //没有gps，wifi < 3 步数范围内上报上一次定
#define WIFI_OVER_TIME 			25

typedef enum {
    STRAT_WIFI_TPYE_GPS_STARTED    =0x01,  //先开启gps，定位不成功，在开启WiFi
    STRAT_WIFI_TPYE_GPS_NO_STARTED=0x02, //没有开启过gps，在开启WiFi
  	 STRAT_WIFI_TPYE_EXPRA_WIFI_STARTED=0x03, //单独搜索WIFI
}START_WIFI_TPYE;
	
//上报定位类型
typedef enum
{
	LOCATION_UP_TYPE_LBS = 0,
	LOCATION_UP_TYPE_GPS,
	LOCATION_UP_TYPE_WIFI,
	LOCATION_UP_TYPE_LAST,
	LOCATION_UP_TYPE_BLE = 4,
}LOCATION_UP_TYPE;

//定位类型
typedef enum
{
	LOCATION_TYPE_PERIOD,    
	LOCATION_TYPE_IMMEDIATE	,
	LOCATION_TYPE_SOS = 3,	//sos定位	
	LOCATION_TYPE_FENCE = 4,	// 围栏定位
	#if USE_LV_WATCH_SPAIN != 0
	LOCATION_TYPE_FALL,	// 跌倒定位
	LOCATION_TYPE_CHARGER_CONNECTED, // 充电器连接
	LOCATION_TYPE_CHARGER_DISCONNECTED, // 充电器断开
	LOCATION_TYPE_BATTERY_LOW,  //低电报警
	LOCATION_TYPE_CHARGER_COMPLETE, // 充电完成
	#endif
}LOCATION_TYPE;
	
#if USE_LV_WATCH_EXTRA_WIFI != 0

/*全通平台，除正常周期位置上报，2分钟额外采集一次WiFi数据，在周期上报时，一起上报*/
typedef struct _ExtraLocationUp
{
	ws_wifi_info mWifi;
	
	uint8_t  mCellType;  //基站类型 lte gsm
	/*基站数据*/
	union{
	ws_location_lte  lte;  
	ws_location_gsm  gsm;
	ws_location_cdma cdma;
	}data;
	
	hal_rtc_t mTime;     //取位置的时间
	bool    mValid;	    //位置信息是否有效
}ExtraLocationUp;

extern char Flw_GetExpraWifi(ExtraLocationUp * extra_loc_data);

#endif

void Flw_JudgeLocationType(void);
void Flw_JudgeWifiLocationType(void);
void Flw_JudgeFirstWifiLocationType(void);

extern void Flw_JudgeLocationType(void);

#endif

