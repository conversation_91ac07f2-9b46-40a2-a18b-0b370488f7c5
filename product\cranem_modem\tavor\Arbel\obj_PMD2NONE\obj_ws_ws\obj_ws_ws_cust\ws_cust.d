\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \ws\ws_cust\code\ws_cust.c
\ws\ws_cust\code\ws_cust.c:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\teldev.h
\hop\telephony\atcmdsrv\inc\teldev.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_api_types.h
\pcac\ci\inc\ci_api_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_cfg.h
\pcac\ci\inc\ci_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_err.h
\pcac\ci\inc\ci_err.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\ci_api_client.h
\tavor\Arbel\obj_PMD2NONE\inc\ci_api_client.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\teldef.h
\hop\telephony\atcmdsrv\inc\teldef.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\at_gbl_types.h
\hop\telephony\atcmdsrv\inc\at_gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlAtParser.h
\hop\telephony\atparser\inc\utlAtParser.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlTypes.h
\hop\telephony\atparser\inc\utlTypes.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlLinkedList.h
\hop\telephony\atparser\inc\utlLinkedList.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlStateMachine.h
\hop\telephony\atparser\inc\utlStateMachine.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlTime.h
\hop\telephony\atparser\inc\utlTime.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\time.h
\tavor\Arbel\obj_PMD2NONE\inc\time.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\signal.h
\tavor\Arbel\obj_PMD2NONE\inc\signal.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlTimer.h
\hop\telephony\atparser\inc\utlTimer.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlVString.h
\hop\telephony\atparser\inc\utlVString.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlSemaphore.h
\hop\telephony\atparser\inc\utlSemaphore.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlMutex.h
\hop\telephony\atparser\inc\utlMutex.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_thread.h
\os\threadx\inc\tx_thread.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_port.h
\os\threadx\inc\tx_port.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa_old_api.h
\os\osa\inc\osa_old_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_hisr.h
\os\threadx\inc\tx_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa_tx.h
\os\osa\inc\osa_tx.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_thread.h
\os\threadx\inc\tx_thread.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_port.h
\os\threadx\inc\tx_port.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_hisr.h
\os\threadx\inc\tx_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_timer.h
\os\threadx\inc\tx_timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_initialize.h
\os\threadx\inc\tx_initialize.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\BSP\inc\bsp_hisr.h
\csw\BSP\inc\bsp_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa_utils.h
\os\osa\inc\osa_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\nu_xscale\inc\nucleus.h
\os\nu_xscale\inc\nucleus.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa_internals.h
\os\osa\inc\osa_internals.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_hisr.h
\os\threadx\inc\tx_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_api.h
\os\threadx\inc\tx_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\threadx\inc\tx_initialize.h
\os\threadx\inc\tx_initialize.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa_um_extr.h
\os\osa\inc\osa_um_extr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \os\osa\inc\osa_um_defs.h
\os\osa\inc\osa_um_defs.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\mat.h
\tavor\Arbel\obj_PMD2NONE\inc\mat.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\utlTrace.h
\hop\telephony\atparser\inc\utlTrace.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\inc\diag.h
\diag\diag_logic\inc\diag.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\inc\diag_API.h
\diag\diag_logic\inc\diag_API.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\inc\diag_types.h
\diag\diag_logic\inc\diag_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\inc\diag_config.h
\diag\diag_logic\inc\diag_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\inc\diag_osif.h
\diag\diag_logic\inc\diag_osif.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\BSP\inc\asserts.h
\csw\BSP\inc\asserts.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\inc\diag_pdu.h
\diag\diag_logic\inc\diag_pdu.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\platform\inc\ICAT_config.h
\csw\platform\inc\ICAT_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\src\diag_tx.h
\diag\diag_logic\src\diag_tx.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \diag\diag_logic\src\diag_API_var.h
\diag\diag_logic\src\diag_API_var.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hal\UART\inc\UART.h
\hal\UART\inc\UART.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\pmu\inc\pmu.h
\hop\pmu\inc\pmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\inc\platform.h
\tavor\Arbel\inc\platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\tftdef.h
\tavor\Arbel\obj_PMD2NONE\inc\tftdef.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/mbedTLS/include/../../../pcac/duster/inc/pdpdef.h
/pcac/mbedTLS/include/../../../pcac/duster/inc/pdpdef.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\telconfig.h
\hop\telephony\atcmdsrv\inc\telconfig.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\telatparamdef.h
\hop\telephony\atcmdsrv\inc\telatparamdef.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_mm.h
\pcac\ci\inc\ci_mm.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_ps.h
\pcac\ci\inc\ci_ps.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\telcontroller.h
\hop\telephony\atcmdsrv\inc\telcontroller.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\telatci.h
\hop\telephony\atcmdsrv\inc\telatci.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_cc.h
\pcac\ci\inc\ci_cc.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_dat.h
\pcac\ci\inc\ci_dat.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atcmdsrv\inc\stub.h
\hop\telephony\atcmdsrv\inc\stub.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_dev.h
\pcac\ci\inc\ci_dev.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \pcac\ci\inc\ci_dev_engm.h
\pcac\ci\inc\ci_dev_engm.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \cust\bsp\inc\uos.h
\cust\bsp\inc\uos.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \hop\telephony\atparser\inc\stdbool.h
\hop\telephony\atparser\inc\stdbool.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/sockets.h
/pcac/lwipv4v6/src/include/lwip/sockets.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/opt.h
/pcac/lwipv4v6/src/include/lwip/opt.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/arch/lwipopts.h
/pcac/lwipv4v6/src/include/arch/lwipopts.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/arch/cc.h
/pcac/lwipv4v6/src/include/arch/cc.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/arch/lwipopts_platform.h
/pcac/lwipv4v6/src/include/arch/lwipopts_platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/arch/lwipopts_crane.h
/pcac/lwipv4v6/src/include/arch/lwipopts_crane.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/debug.h
/pcac/lwipv4v6/src/include/lwip/debug.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/arch.h
/pcac/lwipv4v6/src/include/lwip/arch.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/opt.h
/pcac/lwipv4v6/src/include/lwip/opt.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/ip_addr.h
/pcac/lwipv4v6/src/include/lwip/ip_addr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/def.h
/pcac/lwipv4v6/src/include/lwip/def.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\ip4_addr.h
\tavor\Arbel\obj_PMD2NONE\inc\ip4_addr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\ip6_addr.h
\tavor\Arbel\obj_PMD2NONE\inc\ip6_addr.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\inet.h
\tavor\Arbel\obj_PMD2NONE\inc\inet.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\inet6.h
\tavor\Arbel\obj_PMD2NONE\inc\inet6.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/arch/sys_arch.h
/pcac/lwipv4v6/src/include/arch/sys_arch.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/err.h
/pcac/lwipv4v6/src/include/lwip/err.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/mem.h
/pcac/lwipv4v6/src/include/lwip/mem.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/netdb.h
/pcac/lwipv4v6/src/include/lwip/netdb.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/api_msg.h
/pcac/lwipv4v6/src/include/lwip/api_msg.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/sys.h
/pcac/lwipv4v6/src/include/lwip/sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\igmp.h
\tavor\Arbel\obj_PMD2NONE\inc\igmp.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/netif.h
/pcac/lwipv4v6/src/include/lwip/netif.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/pbuf.h
/pcac/lwipv4v6/src/include/lwip/pbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\dhcp6.h
\tavor\Arbel\obj_PMD2NONE\inc\dhcp6.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/api.h
/pcac/lwipv4v6/src/include/lwip/api.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/netbuf.h
/pcac/lwipv4v6/src/include/lwip/netbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\digest.h
\tavor\Arbel\obj_PMD2NONE\inc\digest.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/tcp.h
/pcac/lwipv4v6/src/include/lwip/tcp.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/ip.h
/pcac/lwipv4v6/src/include/lwip/ip.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\ip4.h
\tavor\Arbel\obj_PMD2NONE\inc\ip4.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\ip6.h
\tavor\Arbel\obj_PMD2NONE\inc\ip6.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : /pcac/lwipv4v6/src/include/lwip/ip.h
/pcac/lwipv4v6/src/include/lwip/ip.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\icmp.h
\tavor\Arbel\obj_PMD2NONE\inc\icmp.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\icmp6.h
\tavor\Arbel\obj_PMD2NONE\inc\icmp6.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\libhttpclient.h
\tavor\Arbel\obj_PMD2NONE\inc\libhttpclient.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\httpclient_sys.h
\tavor\Arbel\obj_PMD2NONE\inc\httpclient_sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_ws_ws\obj_ws_ws_cust/ws_cust.o : \tavor\Arbel\obj_PMD2NONE\inc\ws_protocol.h
\tavor\Arbel\obj_PMD2NONE\inc\ws_protocol.h:
