# 1 "\\pcac\\lzma\\src\\LzmaDec.c"
/* LzmaDec.c -- LZMA Decoder
2008-11-06 : <PERSON> : Public domain */

# 1 "\\pcac\\lzma\\inc\\LzmaDec.h"
/* LzmaDec.h -- LZMA Decoder
2008-10-04 : <PERSON> : Public domain */




# 1 "\\pcac\\lzma\\inc\\types.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 4 "\\pcac\\lzma\\inc\\types.h"
# 1 "\\csw\\platform\\inc\\gbl_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_types.h
Description : Global types file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




/* Use the Xscale environment types */
# 1 "\\env\\win32\\inc\\xscale_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : xscale_types.h
Description : Global types file for the Xscale environment.

Notes       : This file is designed for use in the arm environment
              and is referenced from the gbl_types.h file. Use of
			  this file requires ENV_XSCALE to be defined in xscale_env.mak.
              
Copyright 2001, Intel Corporation, All rights reserved.
=========================================================================== */




typedef unsigned char	BOOL;
typedef unsigned char   UINT8;
typedef unsigned short  UINT16;
typedef unsigned long   UINT32;

typedef char            CHAR;
typedef signed char     INT8;
typedef signed short    INT16;
typedef signed long     INT32;













/*                         end of xscale_types.h
--------------------------------------------------------------------------- */



# 23 "\\csw\\platform\\inc\\gbl_types.h"


/* Use the NordHeim environment types */




/* Use the Arm environment types */




/* Use the Gnu environment types */




/* Use the Microsoft Visual C environment types */




  /* Standard typedefs */
  typedef unsigned char   Bool;         /* Boolean                        */

  /* Standard typedefs - to retain compatibility with TDMA */
  typedef UINT8           		 BYTE;         			/* Unsigned 8-bit quantity        */
  typedef UINT8            		 UBYTE;        			/* Unsigned 8-bit quantity        */
  typedef UINT16          		 UWORD;        			/* Unsigned 16-bit quantity       */
  typedef UINT16          		 WORD;         			/* Unsigned 16-bit quantity       */
  typedef INT16           		 SWORD;        			/* Signed 16-bit quantity         */
  typedef UINT32                 DWORD;        			/* Unsigned 32-bit quantity       */
  // hero start //FEATURE_HERO_ENGINE_APP

  typedef unsigned long long     UINT64;                /* Unsigned 64-bit quantity       */

  typedef void* 		         VOID_PTR;























  /* A NULL value is required such that it is not mistaken for a valid */
  /* value which includes values in the range of modulo 64. */


  /* Definition of NULL is required */















/*                      end of gbl_types.h
--------------------------------------------------------------------------- */



# 5 "\\pcac\\lzma\\inc\\types.h"
typedef unsigned char uint8_t ;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;
typedef unsigned long long uint64_t;
//typedef unsigned long uintptr_t;




	



typedef int SRes;
typedef int ptrdiff_t;
//typedef int Bool;








typedef struct
{
  void *(*Alloc)(void *p, size_t size);
  void (*Free)(void *p, void *address); /* address can be 0 */
} ISzAlloc;






# 52 "\\pcac\\lzma\\inc\\types.h"












typedef int Int32;
typedef unsigned int UInt32;



typedef UInt32 SizeT;


# 81 "\\pcac\\lzma\\inc\\types.h"





typedef long long int Int64;
typedef unsigned long long int UInt64;





typedef struct
{
  size_t (*Write)(void *p, const void *buf, size_t size);
    /* Returns: result - the number of actually written bytes.
       (result < size) means error */
} ISeqOutStream;


typedef struct
{
  SRes (*Read)(void *p, void *buf, size_t *size);
    /* if (input(*size) != 0 && output(*size) == 0) means end_of_stream.
       (output(*size) < input(*size)) is allowed */
} ISeqInStream;

typedef struct
{
  SRes (*Progress)(void *p, UInt64 inSize, UInt64 outSize);
    /* Returns: result. (result != SZ_OK) means break.
       Value (UInt64)(Int64)-1 for size means unknown value. */
} ICompressProgress;



# 8 "\\pcac\\lzma\\inc\\LzmaDec.h"
# 9 "\\pcac\\lzma\\inc\\LzmaDec.h"


/* _LZMA_PROB32 can increase the speed on some CPUs,
   but memory usage for CLzmaDec::probs will be doubled in that case */









/* ---------- LZMA Properties ---------- */



typedef struct _CLzmaProps
{
  unsigned lc, lp, pb;
  UInt32 dicSize;
} CLzmaProps;

/* LzmaProps_Decode - decodes properties
Returns:
  SZ_OK
  SZ_ERROR_UNSUPPORTED - Unsupported properties
*/

SRes LzmaProps_Decode(CLzmaProps *p, const unsigned char *data, unsigned size);


/* ---------- LZMA Decoder state ---------- */

/* LZMA_REQUIRED_INPUT_MAX = number of required input bytes for worst case.
   Num bits = log2((2^11 / 31) ^ 22) + 26 < 134 + 26 = 160; */



typedef struct
{
  CLzmaProps prop;
  UInt32 *probs;
  unsigned char *dic;
  const unsigned char *buf;
  UInt32 range, code;
  SizeT dicPos;
  SizeT dicBufSize;
  UInt32 processedPos;
  UInt32 checkDicSize;
  unsigned state;
  UInt32 reps[4];
  unsigned remainLen;
  int needFlush;
  int needInitState;
  UInt32 numProbs;
  unsigned tempBufSize;
  unsigned char tempBuf[20];
} CLzmaDec;



void LzmaDec_Init(CLzmaDec *p);

/* There are two types of LZMA streams:
     0) Stream with end mark. That end mark adds about 6 bytes to compressed size.
     1) Stream without end mark. You must know exact uncompressed size to decompress such stream. */

typedef enum
{
  LZMA_FINISH_ANY,   /* finish at any point */
  LZMA_FINISH_END    /* block must be finished at the end */
} ELzmaFinishMode;

/* ELzmaFinishMode has meaning only if the decoding reaches output limit !!!

   You must use LZMA_FINISH_END, when you know that current output buffer
   covers last bytes of block. In other cases you must use LZMA_FINISH_ANY.

   If LZMA decoder sees end marker before reaching output limit, it returns SZ_OK,
   and output value of destLen will be less than output buffer size limit.
   You can check status result also.

   You can use multiple checks to test data integrity after full decompression:
     1) Check Result and "status" variable.
     2) Check that output(destLen) = uncompressedSize, if you know real uncompressedSize.
     3) Check that output(srcLen) = compressedSize, if you know real compressedSize.
        You must use correct finish mode in that case. */

typedef enum
{
  LZMA_STATUS_NOT_SPECIFIED,               /* use main error code instead */
  LZMA_STATUS_FINISHED_WITH_MARK,          /* stream was finished with end mark. */
  LZMA_STATUS_NOT_FINISHED,                /* stream was not finished */
  LZMA_STATUS_NEEDS_MORE_INPUT,            /* you must provide more input bytes */
  LZMA_STATUS_MAYBE_FINISHED_WITHOUT_MARK  /* there is probability that stream was finished without end mark */
} ELzmaStatus;

/* ELzmaStatus is used only as output value for function call */


/* ---------- Interfaces ---------- */

/* There are 3 levels of interfaces:
     1) Dictionary Interface
     2) Buffer Interface
     3) One Call Interface
   You can select any of these interfaces, but don't mix functions from different
   groups for same object. */


/* There are two variants to allocate state for Dictionary Interface:
     1) LzmaDec_Allocate / LzmaDec_Free
     2) LzmaDec_AllocateProbs / LzmaDec_FreeProbs
   You can use variant 2, if you set dictionary buffer manually.
   For Buffer Interface you must always use variant 1.

LzmaDec_Allocate* can return:
  SZ_OK
  SZ_ERROR_MEM         - Memory allocation error
  SZ_ERROR_UNSUPPORTED - Unsupported properties
*/
   
SRes LzmaDec_AllocateProbs(CLzmaDec *p, const unsigned char *props, unsigned propsSize, ISzAlloc *alloc);
void LzmaDec_FreeProbs(CLzmaDec *p, ISzAlloc *alloc);

SRes LzmaDec_Allocate(CLzmaDec *state, const unsigned char *prop, unsigned propsSize, ISzAlloc *alloc);
void LzmaDec_Free(CLzmaDec *state, ISzAlloc *alloc);

/* ---------- Dictionary Interface ---------- */

/* You can use it, if you want to eliminate the overhead for data copying from
   dictionary to some other external buffer.
   You must work with CLzmaDec variables directly in this interface.

   STEPS:
     LzmaDec_Constr()
     LzmaDec_Allocate()
     for (each new stream)
     {
       LzmaDec_Init()
       while (it needs more decompression)
       {
         LzmaDec_DecodeToDic()
         use data from CLzmaDec::dic and update CLzmaDec::dicPos
       }
     }
     LzmaDec_Free()
*/

/* LzmaDec_DecodeToDic
   
   The decoding to internal dictionary buffer (CLzmaDec::dic).
   You must manually update CLzmaDec::dicPos, if it reaches CLzmaDec::dicBufSize !!!

finishMode:
  It has meaning only if the decoding reaches output limit (dicLimit).
  LZMA_FINISH_ANY - Decode just dicLimit bytes.
  LZMA_FINISH_END - Stream must be finished after dicLimit.

Returns:
  SZ_OK
    status:
      LZMA_STATUS_FINISHED_WITH_MARK
      LZMA_STATUS_NOT_FINISHED
      LZMA_STATUS_NEEDS_MORE_INPUT
      LZMA_STATUS_MAYBE_FINISHED_WITHOUT_MARK
  SZ_ERROR_DATA - Data error
*/

SRes LzmaDec_DecodeToDic(CLzmaDec *p, SizeT dicLimit,
    const unsigned char *src, SizeT *srcLen, ELzmaFinishMode finishMode, ELzmaStatus *status);


/* ---------- Buffer Interface ---------- */

/* It's zlib-like interface.
   See LzmaDec_DecodeToDic description for information about STEPS and return results,
   but you must use LzmaDec_DecodeToBuf instead of LzmaDec_DecodeToDic and you don't need
   to work with CLzmaDec variables manually.

finishMode:
  It has meaning only if the decoding reaches output limit (*destLen).
  LZMA_FINISH_ANY - Decode just destLen bytes.
  LZMA_FINISH_END - Stream must be finished after (*destLen).
*/

SRes LzmaDec_DecodeToBuf(CLzmaDec *p, unsigned char *dest, SizeT *destLen,
    const unsigned char *src, SizeT *srcLen, ELzmaFinishMode finishMode, ELzmaStatus *status);


/* ---------- One Call Interface ---------- */

/* LzmaDecode

finishMode:
  It has meaning only if the decoding reaches output limit (*destLen).
  LZMA_FINISH_ANY - Decode just destLen bytes.
  LZMA_FINISH_END - Stream must be finished after (*destLen).

Returns:
  SZ_OK
    status:
      LZMA_STATUS_FINISHED_WITH_MARK
      LZMA_STATUS_NOT_FINISHED
      LZMA_STATUS_MAYBE_FINISHED_WITHOUT_MARK
  SZ_ERROR_DATA - Data error
  SZ_ERROR_MEM  - Memory allocation error
  SZ_ERROR_UNSUPPORTED - Unsupported properties
  SZ_ERROR_INPUT_EOF - It needs more bytes in input buffer (src).
*/

SRes LzmaDecode(unsigned char *dest, SizeT *destLen, const unsigned char *src, SizeT *srcLen,
    const unsigned char *propData, unsigned propSize, ELzmaFinishMode finishMode,
    ELzmaStatus *status, ISzAlloc *alloc);

# 5 "\\pcac\\lzma\\src\\LzmaDec.c"
//#include "tinyalloc.h"

//#include <string.h>












# 27 "\\pcac\\lzma\\src\\LzmaDec.c"





/* #define _LZMA_SIZE_OPT */

# 47 "\\pcac\\lzma\\src\\LzmaDec.c"



# 59 "\\pcac\\lzma\\src\\LzmaDec.c"





# 70 "\\pcac\\lzma\\src\\LzmaDec.c"

# 77 "\\pcac\\lzma\\src\\LzmaDec.c"


















# 107 "\\pcac\\lzma\\src\\LzmaDec.c"










static const unsigned char kLiteralNextStates[12 * 2] =
{
  0, 0, 0, 0, 1, 2, 3,  4,  5,  6,  4,  5,
  7, 7, 7, 7, 7, 7, 7, 10, 10, 10, 10, 10
};



/* First LZMA-symbol is always decoded.
And it decodes new LZMA-symbols while (buf < bufLimit), but "buf" is without last normalization
Out:
  Result:
    SZ_OK - OK
    SZ_ERROR_DATA - Error
  p->remainLen:
    < kMatchSpecLenStart : normal remain
    = kMatchSpecLenStart : finished
    = kMatchSpecLenStart + 1 : Flush marker
    = kMatchSpecLenStart + 2 : State Init Marker
*/

static int  LzmaDec_DecodeReal(CLzmaDec *p, SizeT limit, const unsigned char *bufLimit)
{
  UInt32 *probs = p->probs;

  unsigned state = p->state;
  UInt32 rep0 = p->reps[0], rep1 = p->reps[1], rep2 = p->reps[2], rep3 = p->reps[3];
  unsigned pbMask = ((unsigned)1 << (p->prop.pb)) - 1;
  unsigned lpMask = ((unsigned)1 << (p->prop.lp)) - 1;
  unsigned lc = p->prop.lc;

  unsigned char *dic = p->dic;
  SizeT dicBufSize = p->dicBufSize;
  SizeT dicPos = p->dicPos;
  
  UInt32 processedPos = p->processedPos;
  UInt32 checkDicSize = p->checkDicSize;
  unsigned len = 0;

  const unsigned char *buf = p->buf;
  UInt32 range = p->range;
  UInt32 code = p->code;

  do
  {
    UInt32 *prob;
    UInt32 bound;
    unsigned ttt;
    unsigned posState = processedPos & pbMask;

    prob = probs + 0 + (state << 4) + posState;
    ttt = *(prob); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
    {
      unsigned symbol;
      range = bound; *(prob) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
      prob = probs + (((((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4)) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8))) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8)));
      if (checkDicSize != 0 || processedPos != 0)
        prob += (768 * (((processedPos & lpMask) << lc) +
        (dic[(dicPos == 0 ? dicBufSize : dicPos) - 1] >> (8 - lc))));

      if (state < 7)
      {
        symbol = 1;
        do { ttt = *(prob + symbol); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(prob + symbol) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; symbol = (symbol + symbol); ;; } else { range -= bound; code -= bound; *(prob + symbol) = (UInt32)(ttt - (ttt >> 5));; symbol = (symbol + symbol) + 1; ;; } } while (symbol < 0x100);
      }
      else
      {
        unsigned matchByte = p->dic[(dicPos - rep0) + ((dicPos < rep0) ? dicBufSize : 0)];
        unsigned offs = 0x100;
        symbol = 1;
        do
        {
          unsigned bit;
          UInt32 *probLit;
          matchByte <<= 1;
          bit = (matchByte & offs);
          probLit = prob + offs + bit + symbol;
          ttt = *(probLit); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(probLit) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; symbol = (symbol + symbol); offs &= ~bit; } else { range -= bound; code -= bound; *(probLit) = (UInt32)(ttt - (ttt >> 5));; symbol = (symbol + symbol) + 1; offs &= bit; }
        }
        while (symbol < 0x100);
      }
      dic[dicPos++] = (unsigned char)symbol;
      processedPos++;

      state = kLiteralNextStates[state];
      /* if (state < 4) state = 0; else if (state < 10) state -= 3; else state -= 6; */
      continue;
    }
    else
    {
      range -= bound; code -= bound; *(prob) = (UInt32)(ttt - (ttt >> 5));;
      prob = probs + (0 + (12 << 4)) + state;
      ttt = *(prob); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
      {
        range = bound; *(prob) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
        state += 12;
        prob = probs + (((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4));
      }
      else
      {
        range -= bound; code -= bound; *(prob) = (UInt32)(ttt - (ttt >> 5));;
        if (checkDicSize == 0 && processedPos == 0)
          return 1;
        prob = probs + ((0 + (12 << 4)) + 12) + state;
        ttt = *(prob); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
        {
          range = bound; *(prob) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
          prob = probs + (((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (state << 4) + posState;
          ttt = *(prob); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
          {
            range = bound; *(prob) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
            dic[dicPos] = dic[(dicPos - rep0) + ((dicPos < rep0) ? dicBufSize : 0)];
            dicPos++;
            processedPos++;
            state = state < 7 ? 9 : 11;
            continue;
          }
          range -= bound; code -= bound; *(prob) = (UInt32)(ttt - (ttt >> 5));;
        }
        else
        {
          UInt32 distance;
          range -= bound; code -= bound; *(prob) = (UInt32)(ttt - (ttt >> 5));;
          prob = probs + (((0 + (12 << 4)) + 12) + 12) + state;
          ttt = *(prob); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
          {
            range = bound; *(prob) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
            distance = rep1;
          }
          else
          {
            range -= bound; code -= bound; *(prob) = (UInt32)(ttt - (ttt >> 5));;
            prob = probs + ((((0 + (12 << 4)) + 12) + 12) + 12) + state;
            ttt = *(prob); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
            {
              range = bound; *(prob) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
              distance = rep2;
            }
            else
            {
              range -= bound; code -= bound; *(prob) = (UInt32)(ttt - (ttt >> 5));;
              distance = rep3;
              rep3 = rep2;
            }
            rep2 = rep1;
          }
          rep1 = rep0;
          rep0 = distance;
        }
        state = state < 7 ? 8 : 11;
        prob = probs + ((((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4)) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8)));
      }
      {
        unsigned limit, offset;
        UInt32 *probLen = prob + 0;
        ttt = *(probLen); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
        {
          range = bound; *(probLen) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
          probLen = prob + ((0 + 1) + 1) + (posState << 3);
          offset = 0;
          limit = (1 << 3);
        }
        else
        {
          range -= bound; code -= bound; *(probLen) = (UInt32)(ttt - (ttt >> 5));;
          probLen = prob + (0 + 1);
          ttt = *(probLen); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
          {
            range = bound; *(probLen) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));;
            probLen = prob + (((0 + 1) + 1) + ((1 << 4) << 3)) + (posState << 3);
            offset = (1 << 3);
            limit = (1 << 3);
          }
          else
          {
            range -= bound; code -= bound; *(probLen) = (UInt32)(ttt - (ttt >> 5));;
            probLen = prob + ((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3));
            offset = (1 << 3) + (1 << 3);
            limit = (1 << 8);
          }
        }
        { len = 1; do { { ttt = *((probLen + len)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((probLen + len)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; len = (len + len); ;; } else { range -= bound; code -= bound; *((probLen + len)) = (UInt32)(ttt - (ttt >> 5));; len = (len + len) + 1; ;; }; }; } while (len < limit); len -= limit; };
        len += offset;
      }

      if (state >= 12)
      {
        UInt32 distance;
        prob = probs + ((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) +
            ((len < 4 ? len : 4 - 1) << 6);
        { distance = 1; { ttt = *((prob + distance)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((prob + distance)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; distance = (distance + distance); ;; } else { range -= bound; code -= bound; *((prob + distance)) = (UInt32)(ttt - (ttt >> 5));; distance = (distance + distance) + 1; ;; }; }; { ttt = *((prob + distance)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((prob + distance)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; distance = (distance + distance); ;; } else { range -= bound; code -= bound; *((prob + distance)) = (UInt32)(ttt - (ttt >> 5));; distance = (distance + distance) + 1; ;; }; }; { ttt = *((prob + distance)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((prob + distance)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; distance = (distance + distance); ;; } else { range -= bound; code -= bound; *((prob + distance)) = (UInt32)(ttt - (ttt >> 5));; distance = (distance + distance) + 1; ;; }; }; { ttt = *((prob + distance)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((prob + distance)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; distance = (distance + distance); ;; } else { range -= bound; code -= bound; *((prob + distance)) = (UInt32)(ttt - (ttt >> 5));; distance = (distance + distance) + 1; ;; }; }; { ttt = *((prob + distance)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((prob + distance)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; distance = (distance + distance); ;; } else { range -= bound; code -= bound; *((prob + distance)) = (UInt32)(ttt - (ttt >> 5));; distance = (distance + distance) + 1; ;; }; }; { ttt = *((prob + distance)); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *((prob + distance)) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; distance = (distance + distance); ;; } else { range -= bound; code -= bound; *((prob + distance)) = (UInt32)(ttt - (ttt >> 5));; distance = (distance + distance) + 1; ;; }; }; distance -= 0x40; };
        if (distance >= 4)
        {
          unsigned posSlot = (unsigned)distance;
          int numDirectBits = (int)(((distance >> 1) - 1));
          distance = (2 | (distance & 1));
          if (posSlot < 14)
          {
            distance <<= numDirectBits;
            prob = probs + (((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + distance - posSlot - 1;
            {
              UInt32 mask = 1;
              unsigned i = 1;
              do
              {
                ttt = *(prob + i); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(prob + i) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; i = (i + i); ;; } else { range -= bound; code -= bound; *(prob + i) = (UInt32)(ttt - (ttt >> 5));; i = (i + i) + 1; distance |= mask; };
                mask <<= 1;
              }
              while (--numDirectBits != 0);
            }
          }
          else
          {
            numDirectBits -= 4;
            do
            {
              if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }
              range >>= 1;
              
              {
                UInt32 t;
                code -= range;
                t = (0 - ((UInt32)code >> 31)); /* (UInt32)((Int32)code >> 31) */
                distance = (distance << 1) + (t + 1);
                code += range & t;
              }
              /*
              distance <<= 1;
              if (code >= range)
              {
                code -= range;
                distance |= 1;
              }
              */
            }
            while (--numDirectBits != 0);
            prob = probs + ((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14);
            distance <<= 4;
            {
              unsigned i = 1;
              ttt = *(prob + i); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(prob + i) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; i = (i + i); ;; } else { range -= bound; code -= bound; *(prob + i) = (UInt32)(ttt - (ttt >> 5));; i = (i + i) + 1; distance |= 1; };
              ttt = *(prob + i); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(prob + i) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; i = (i + i); ;; } else { range -= bound; code -= bound; *(prob + i) = (UInt32)(ttt - (ttt >> 5));; i = (i + i) + 1; distance |= 2; };
              ttt = *(prob + i); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(prob + i) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; i = (i + i); ;; } else { range -= bound; code -= bound; *(prob + i) = (UInt32)(ttt - (ttt >> 5));; i = (i + i) + 1; distance |= 4; };
              ttt = *(prob + i); if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound; *(prob + i) = (UInt32)(ttt + (((1 << 11) - ttt) >> 5));; i = (i + i); ;; } else { range -= bound; code -= bound; *(prob + i) = (UInt32)(ttt - (ttt >> 5));; i = (i + i) + 1; distance |= 8; };
            }
            if (distance == (UInt32)0xFFFFFFFF)
            {
              len += (2 + (1 << 3) + (1 << 3) + (1 << 8));
              state -= 12;
              break;
            }
          }
        }
        rep3 = rep2;
        rep2 = rep1;
        rep1 = rep0;
        rep0 = distance + 1;
        if (checkDicSize == 0)
        {
          if (distance >= processedPos)
            return 1;
        }
        else if (distance >= checkDicSize)
          return 1;
        state = (state < 12 + 7) ? 7 : 7 + 3;
        /* state = kLiteralNextStates[state]; */
      }

      len += 2;

      if (limit == dicPos)
        return 1;
      {
        SizeT rem = limit - dicPos;
        unsigned curLen = ((rem < len) ? (unsigned)rem : len);
        SizeT pos = (dicPos - rep0) + ((dicPos < rep0) ? dicBufSize : 0);

        processedPos += curLen;

        len -= curLen;
        if (pos + curLen <= dicBufSize)
        {
          unsigned char *dest = dic + dicPos;
          ptrdiff_t src = (ptrdiff_t)pos - (ptrdiff_t)dicPos;
          const unsigned char *lim = dest + curLen;
          dicPos += curLen;
          do
            *(dest) = (unsigned char)*(dest + src);
          while (++dest != lim);
        }
        else
        {
          do
          {
            dic[dicPos++] = dic[pos];
            if (++pos == dicBufSize)
              pos = 0;
          }
          while (--curLen != 0);
        }
      }
    }
  }
  while (dicPos < limit && buf < bufLimit);
  if (range < ((UInt32)1 << 24)) { range <<= 8; code = (code << 8) | (*buf++); };
  p->buf = buf;
  p->range = range;
  p->code = code;
  p->remainLen = len;
  p->dicPos = dicPos;
  p->processedPos = processedPos;
  p->reps[0] = rep0;
  p->reps[1] = rep1;
  p->reps[2] = rep2;
  p->reps[3] = rep3;
  p->state = state;

  return 0;
}

static void  LzmaDec_WriteRem(CLzmaDec *p, SizeT limit)
{
  if (p->remainLen != 0 && p->remainLen < (2 + (1 << 3) + (1 << 3) + (1 << 8)))
  {
    unsigned char *dic = p->dic;
    SizeT dicPos = p->dicPos;
    SizeT dicBufSize = p->dicBufSize;
    unsigned len = p->remainLen;
    UInt32 rep0 = p->reps[0];
    if (limit - dicPos < len)
      len = (unsigned)(limit - dicPos);

    if (p->checkDicSize == 0 && p->prop.dicSize - p->processedPos <= len)
      p->checkDicSize = p->prop.dicSize;

    p->processedPos += len;
    p->remainLen -= len;
    while (len-- != 0)
    {
      dic[dicPos] = dic[(dicPos - rep0) + ((dicPos < rep0) ? dicBufSize : 0)];
      dicPos++;
    }
    p->dicPos = dicPos;
  }
}

static int  LzmaDec_DecodeReal2(CLzmaDec *p, SizeT limit, const unsigned char *bufLimit)
{
  do
  {
    SizeT limit2 = limit;
    if (p->checkDicSize == 0)
    {
      UInt32 rem = p->prop.dicSize - p->processedPos;
      if (limit - p->dicPos > rem)
        limit2 = p->dicPos + rem;
    }
    { int __result__ = (LzmaDec_DecodeReal(p, limit2, bufLimit)); if (__result__ != 0) return __result__; };
    if (p->processedPos >= p->prop.dicSize)
      p->checkDicSize = p->prop.dicSize;
    LzmaDec_WriteRem(p, limit);
  }
  while (p->dicPos < limit && p->buf < bufLimit && p->remainLen < (2 + (1 << 3) + (1 << 3) + (1 << 8)));

  if (p->remainLen > (2 + (1 << 3) + (1 << 3) + (1 << 8)))
  {
    p->remainLen = (2 + (1 << 3) + (1 << 3) + (1 << 8));
  }
  return 0;
}

typedef enum
{
  DUMMY_ERROR, /* unexpected end of input stream */
  DUMMY_LIT,
  DUMMY_MATCH,
  DUMMY_REP
} ELzmaDummy;

static ELzmaDummy LzmaDec_TryDummy(const CLzmaDec *p, const unsigned char *buf, SizeT inSize)
{
  UInt32 range = p->range;
  UInt32 code = p->code;
  const unsigned char *bufLimit = buf + inSize;
  UInt32 *probs = p->probs;
  unsigned state = p->state;
  ELzmaDummy res;

  {
    UInt32 *prob;
    UInt32 bound;
    unsigned ttt;
    unsigned posState = (p->processedPos) & ((1 << p->prop.pb) - 1);

    prob = probs + 0 + (state << 4) + posState;
    ttt = *(prob); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
    {
      range = bound;

      /* if (bufLimit - buf >= 7) return DUMMY_LIT; */

      prob = probs + (((((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4)) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8))) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8)));
      if (p->checkDicSize != 0 || p->processedPos != 0)
        prob += (768 *
          ((((p->processedPos) & ((1 << (p->prop.lp)) - 1)) << p->prop.lc) +
          (p->dic[(p->dicPos == 0 ? p->dicBufSize : p->dicPos) - 1] >> (8 - p->prop.lc))));

      if (state < 7)
      {
        unsigned symbol = 1;
        do { ttt = *(prob + symbol); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound;; symbol = (symbol + symbol); ;; } else { range -= bound; code -= bound;; symbol = (symbol + symbol) + 1; ;; } } while (symbol < 0x100);
      }
      else
      {
        unsigned matchByte = p->dic[p->dicPos - p->reps[0] +
            ((p->dicPos < p->reps[0]) ? p->dicBufSize : 0)];
        unsigned offs = 0x100;
        unsigned symbol = 1;
        do
        {
          unsigned bit;
          UInt32 *probLit;
          matchByte <<= 1;
          bit = (matchByte & offs);
          probLit = prob + offs + bit + symbol;
          ttt = *(probLit); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound;; symbol = (symbol + symbol); offs &= ~bit; } else { range -= bound; code -= bound;; symbol = (symbol + symbol) + 1; offs &= bit; }
        }
        while (symbol < 0x100);
      }
      res = DUMMY_LIT;
    }
    else
    {
      unsigned len;
      range -= bound; code -= bound;;

      prob = probs + (0 + (12 << 4)) + state;
      ttt = *(prob); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
      {
        range = bound;;
        state = 0;
        prob = probs + (((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4));
        res = DUMMY_MATCH;
      }
      else
      {
        range -= bound; code -= bound;;
        res = DUMMY_REP;
        prob = probs + ((0 + (12 << 4)) + 12) + state;
        ttt = *(prob); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
        {
          range = bound;;
          prob = probs + (((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (state << 4) + posState;
          ttt = *(prob); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
          {
            range = bound;;
            if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); };
            return DUMMY_REP;
          }
          else
          {
            range -= bound; code -= bound;;
          }
        }
        else
        {
          range -= bound; code -= bound;;
          prob = probs + (((0 + (12 << 4)) + 12) + 12) + state;
          ttt = *(prob); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
          {
            range = bound;;
          }
          else
          {
            range -= bound; code -= bound;;
            prob = probs + ((((0 + (12 << 4)) + 12) + 12) + 12) + state;
            ttt = *(prob); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
            {
              range = bound;;
            }
            else
            {
              range -= bound; code -= bound;;
            }
          }
        }
        state = 12;
        prob = probs + ((((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4)) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8)));
      }
      {
        unsigned limit, offset;
        UInt32 *probLen = prob + 0;
        ttt = *(probLen); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
        {
          range = bound;;
          probLen = prob + ((0 + 1) + 1) + (posState << 3);
          offset = 0;
          limit = 1 << 3;
        }
        else
        {
          range -= bound; code -= bound;;
          probLen = prob + (0 + 1);
          ttt = *(probLen); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound)
          {
            range = bound;;
            probLen = prob + (((0 + 1) + 1) + ((1 << 4) << 3)) + (posState << 3);
            offset = (1 << 3);
            limit = 1 << 3;
          }
          else
          {
            range -= bound; code -= bound;;
            probLen = prob + ((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3));
            offset = (1 << 3) + (1 << 3);
            limit = 1 << 8;
          }
        }
        { len = 1; do { ttt = *(probLen + len); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound;; len = (len + len); ;; } else { range -= bound; code -= bound;; len = (len + len) + 1; ;; } } while (len < limit); len -= limit; };
        len += offset;
      }

      if (state < 4)
      {
        unsigned posSlot;
        prob = probs + ((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) +
            ((len < 4 ? len : 4 - 1) <<
            6);
        { posSlot = 1; do { ttt = *(prob + posSlot); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound;; posSlot = (posSlot + posSlot); ;; } else { range -= bound; code -= bound;; posSlot = (posSlot + posSlot) + 1; ;; } } while (posSlot < 1 << 6); posSlot -= 1 << 6; };
        if (posSlot >= 4)
        {
          int numDirectBits = ((posSlot >> 1) - 1);

          /* if (bufLimit - buf >= 8) return DUMMY_MATCH; */

          if (posSlot < 14)
          {
            prob = probs + (((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + ((2 | (posSlot & 1)) << numDirectBits) - posSlot - 1;
          }
          else
          {
            numDirectBits -= 4;
            do
            {
              if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }
              range >>= 1;
              code -= range & (((code - range) >> 31) - 1);
              /* if (code >= range) code -= range; */
            }
            while (--numDirectBits != 0);
            prob = probs + ((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14);
            numDirectBits = 4;
          }
          {
            unsigned i = 1;
            do
            {
              ttt = *(prob + i); if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); }; bound = (range >> 11) * ttt; if (code < bound) { range = bound;; i = (i + i); ;; } else { range -= bound; code -= bound;; i = (i + i) + 1; ;; };
            }
            while (--numDirectBits != 0);
          }
        }
      }
    }
  }
  if (range < ((UInt32)1 << 24)) { if (buf >= bufLimit) return DUMMY_ERROR; range <<= 8; code = (code << 8) | (*buf++); };
  return res;
}


static void LzmaDec_InitRc(CLzmaDec *p, const unsigned char *data)
{
  p->code = ((UInt32)data[1] << 24) | ((UInt32)data[2] << 16) | ((UInt32)data[3] << 8) | ((UInt32)data[4]);
  p->range = 0xFFFFFFFF;
  p->needFlush = 0;
}

void LzmaDec_InitDicAndState(CLzmaDec *p, Bool initDic, Bool initState)
{
  p->needFlush = 1;
  p->remainLen = 0;
  p->tempBufSize = 0;

  if (initDic)
  {
    p->processedPos = 0;
    p->checkDicSize = 0;
    p->needInitState = 1;
  }
  if (initState)
    p->needInitState = 1;
}

void LzmaDec_Init(CLzmaDec *p)
{
  p->dicPos = 0;
  LzmaDec_InitDicAndState(p, 1, 1);
}

static void LzmaDec_InitStateReal(CLzmaDec *p)
{
  UInt32 numProbs = (((((((((((0 + (12 << 4)) + 12) + 12) + 12) + 12) + (12 << 4)) + (4 << 6)) + (1 << (14 >> 1)) - 14) + (1 << 4)) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8))) + (((((0 + 1) + 1) + ((1 << 4) << 3)) + ((1 << 4) << 3)) + (1 << 8))) + ((UInt32)768 << (p->prop.lc + p->prop.lp));
  UInt32 i;
  UInt32 *probs = p->probs;
  for (i = 0; i < numProbs; i++)
    probs[i] = (1 << 11) >> 1;
  p->reps[0] = p->reps[1] = p->reps[2] = p->reps[3] = 1;
  p->state = 0;
  p->needInitState = 0;
}

SRes LzmaDec_DecodeToDic(CLzmaDec *p, SizeT dicLimit, const unsigned char *src, SizeT *srcLen,
    ELzmaFinishMode finishMode, ELzmaStatus *status)
{
  SizeT inSize = *srcLen;
  (*srcLen) = 0;
  LzmaDec_WriteRem(p, dicLimit);
  
  *status = LZMA_STATUS_NOT_SPECIFIED;

  while (p->remainLen != (2 + (1 << 3) + (1 << 3) + (1 << 8)))
  {
      int checkEndMarkNow;

      if (p->needFlush != 0)
      {
        for (; inSize > 0 && p->tempBufSize < 5; (*srcLen)++, inSize--)
          p->tempBuf[p->tempBufSize++] = *src++;
        if (p->tempBufSize < 5)
        {
          *status = LZMA_STATUS_NEEDS_MORE_INPUT;
          return 0;
        }
        if (p->tempBuf[0] != 0)
          return 1;

        LzmaDec_InitRc(p, p->tempBuf);
        p->tempBufSize = 0;
      }

      checkEndMarkNow = 0;
      if (p->dicPos >= dicLimit)
      {
        if (p->remainLen == 0 && p->code == 0)
        {
          *status = LZMA_STATUS_MAYBE_FINISHED_WITHOUT_MARK;
          return 0;
        }
        if (finishMode == LZMA_FINISH_ANY)
        {
          *status = LZMA_STATUS_NOT_FINISHED;
          return 0;
        }
        if (p->remainLen != 0)
        {
          *status = LZMA_STATUS_NOT_FINISHED;
          return 1;
        }
        checkEndMarkNow = 1;
      }

      if (p->needInitState)
        LzmaDec_InitStateReal(p);
  
      if (p->tempBufSize == 0)
      {
        SizeT processed;
        const unsigned char *bufLimit;
        if (inSize < 20 || checkEndMarkNow)
        {
          int dummyRes = LzmaDec_TryDummy(p, src, inSize);
          if (dummyRes == DUMMY_ERROR)
          {
            memcpy(p->tempBuf, src, inSize);
            p->tempBufSize = (unsigned)inSize;
            (*srcLen) += inSize;
            *status = LZMA_STATUS_NEEDS_MORE_INPUT;
            return 0;
          }
          if (checkEndMarkNow && dummyRes != DUMMY_MATCH)
          {
            *status = LZMA_STATUS_NOT_FINISHED;
            return 1;
          }
          bufLimit = src;
        }
        else
          bufLimit = src + inSize - 20;
        p->buf = src;
        if (LzmaDec_DecodeReal2(p, dicLimit, bufLimit) != 0)
          return 1;
        processed = (SizeT)(p->buf - src);
        (*srcLen) += processed;
        src += processed;
        inSize -= processed;
      }
      else
      {
        unsigned rem = p->tempBufSize, lookAhead = 0;
        while (rem < 20 && lookAhead < inSize)
          p->tempBuf[rem++] = src[lookAhead++];
        p->tempBufSize = rem;
        if (rem < 20 || checkEndMarkNow)
        {
          int dummyRes = LzmaDec_TryDummy(p, p->tempBuf, rem);
          if (dummyRes == DUMMY_ERROR)
          {
            (*srcLen) += lookAhead;
            *status = LZMA_STATUS_NEEDS_MORE_INPUT;
            return 0;
          }
          if (checkEndMarkNow && dummyRes != DUMMY_MATCH)
          {
            *status = LZMA_STATUS_NOT_FINISHED;
            return 1;
          }
        }
        p->buf = p->tempBuf;
        if (LzmaDec_DecodeReal2(p, dicLimit, p->buf) != 0)
          return 1;
        lookAhead -= (rem - (unsigned)(p->buf - p->tempBuf));
        (*srcLen) += lookAhead;
        src += lookAhead;
        inSize -= lookAhead;
        p->tempBufSize = 0;
      }
  }
  if (p->code == 0)
    *status = LZMA_STATUS_FINISHED_WITH_MARK;
  return (p->code == 0) ? 0 : 1;
}

SRes LzmaDec_DecodeToBuf(CLzmaDec *p, unsigned char *dest, SizeT *destLen, const unsigned char *src, SizeT *srcLen, ELzmaFinishMode finishMode, ELzmaStatus *status)
{
  SizeT outSize = *destLen;
  SizeT inSize = *srcLen;
  *srcLen = *destLen = 0;
  for (;;)
  {
    SizeT inSizeCur = inSize, outSizeCur, dicPos;
    ELzmaFinishMode curFinishMode;
    SRes res;
    if (p->dicPos == p->dicBufSize)
      p->dicPos = 0;
    dicPos = p->dicPos;
    if (outSize > p->dicBufSize - dicPos)
    {
      outSizeCur = p->dicBufSize;
      curFinishMode = LZMA_FINISH_ANY;
    }
    else
    {
      outSizeCur = dicPos + outSize;
      curFinishMode = finishMode;
    }

    res = LzmaDec_DecodeToDic(p, outSizeCur, src, &inSizeCur, curFinishMode, status);
    src += inSizeCur;
    inSize -= inSizeCur;
    *srcLen += inSizeCur;
    outSizeCur = p->dicPos - dicPos;
    memcpy(dest, p->dic + dicPos, outSizeCur);
    dest += outSizeCur;
    outSize -= outSizeCur;
    *destLen += outSizeCur;
    if (res != 0)
      return res;
    if (outSizeCur == 0 || outSize == 0)
      return 0;
  }
}

void LzmaDec_FreeProbs(CLzmaDec *p, ISzAlloc *alloc)
{
  alloc->Free(alloc, p->probs);
  p->probs = 0;
}

static void LzmaDec_FreeDict(CLzmaDec *p, ISzAlloc *alloc)
{
  alloc->Free(alloc, p->dic);
  p->dic = 0;
}

void LzmaDec_Free(CLzmaDec *p, ISzAlloc *alloc)
{
  LzmaDec_FreeProbs(p, alloc);
  LzmaDec_FreeDict(p, alloc);
}

SRes LzmaProps_Decode(CLzmaProps *p, const unsigned char *data, unsigned size)
{
  UInt32 dicSize;
  unsigned char d;
  
  if (size < 5)
    return 4;
  else
    dicSize = data[1] | ((UInt32)data[2] << 8) | ((UInt32)data[3] << 16) | ((UInt32)data[4] << 24);
 
  if (dicSize < (1 << 12))
    dicSize = (1 << 12);
  p->dicSize = dicSize;

  d = data[0];
  if (d >= (9 * 5 * 5))
    return 4;

  p->lc = d % 9;
  d /= 9;
  p->pb = d / 5;
  p->lp = d % 5;

  return 0;
}

static SRes LzmaDec_AllocateProbs2(CLzmaDec *p, const CLzmaProps *propNew, ISzAlloc *alloc)
{
  UInt32 numProbs = ((UInt32)1846 + (768 << ((propNew)->lc + (propNew)->lp)));
  if (p->probs == 0 || numProbs != p->numProbs)
  {
    LzmaDec_FreeProbs(p, alloc);
    p->probs = (UInt32 *)alloc->Alloc(alloc, numProbs * sizeof(UInt32));
    p->numProbs = numProbs;
    if (p->probs == 0)
      return 2;
  }
  return 0;
}

SRes LzmaDec_AllocateProbs(CLzmaDec *p, const unsigned char *props, unsigned propsSize, ISzAlloc *alloc)
{
  CLzmaProps propNew;
  { int __result__ = (LzmaProps_Decode(&propNew, props, propsSize)); if (__result__ != 0) return __result__; };
  { int __result__ = (LzmaDec_AllocateProbs2(p, &propNew, alloc)); if (__result__ != 0) return __result__; };
  p->prop = propNew;
  return 0;
}

SRes LzmaDec_Allocate(CLzmaDec *p, const unsigned char *props, unsigned propsSize, ISzAlloc *alloc)
{
  CLzmaProps propNew;
  SizeT dicBufSize;
  { int __result__ = (LzmaProps_Decode(&propNew, props, propsSize)); if (__result__ != 0) return __result__; };
  { int __result__ = (LzmaDec_AllocateProbs2(p, &propNew, alloc)); if (__result__ != 0) return __result__; };
  dicBufSize = propNew.dicSize;
  if (p->dic == 0 || dicBufSize != p->dicBufSize)
  {
    LzmaDec_FreeDict(p, alloc);
    p->dic = (unsigned char *)alloc->Alloc(alloc, dicBufSize);
    if (p->dic == 0)
    {
      LzmaDec_FreeProbs(p, alloc);
      return 2;
    }
  }
  p->dicBufSize = dicBufSize;
  p->prop = propNew;
  return 0;
}

SRes LzmaDecode(unsigned char *dest, SizeT *destLen, const unsigned char *src, SizeT *srcLen,
    const unsigned char *propData, unsigned propSize, ELzmaFinishMode finishMode,
    ELzmaStatus *status, ISzAlloc *alloc)
{
  CLzmaDec p;
  SRes res;
  SizeT inSize = *srcLen;
  SizeT outSize = *destLen;
  *srcLen = *destLen = 0;
  if (inSize < 5)
    return 6;

  { (&p)->dic = 0; (&p)->probs = 0; };
  res = LzmaDec_AllocateProbs(&p, propData, propSize, alloc);
  if (res != 0)
    return res;
  p.dic = dest;
  p.dicBufSize = outSize;

  LzmaDec_Init(&p);
  
  *srcLen = inSize;
  res = LzmaDec_DecodeToDic(&p, outSize, src, srcLen, finishMode, status);

  if (res == 0 && *status == LZMA_STATUS_NEEDS_MORE_INPUT)
    res = 6;

  (*destLen) = p.dicPos;
  LzmaDec_FreeProbs(&p, alloc);
  return res;
}

