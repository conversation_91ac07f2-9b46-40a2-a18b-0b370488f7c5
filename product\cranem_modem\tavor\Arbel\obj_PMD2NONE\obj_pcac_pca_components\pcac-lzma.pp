//PPC Version : V2.1.9.30
//PPL Source File Name : \tavor\Arbel\obj_PMD2NONE\prepass_results\LzFind.ppp
//PPL Source File Name : \\pcac\\lzma\\src\\LzFind.c
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef unsigned char uint8_t ;
typedef unsigned short uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned long long uint64_t ;
typedef int SRes ;
typedef int ptrdiff_t ;
typedef int Int32 ;
typedef unsigned int UInt32 ;
typedef UInt32 SizeT ;
typedef long long int Int64 ;
typedef unsigned long long int UInt64 ;
typedef UInt32 CLzRef ;
typedef void ( *Mf_Init_Func ) ( void *object ) ;
typedef unsigned char ( *Mf_GetIndexByte_Func ) ( void *object , Int32 index ) ;
typedef UInt32 ( *Mf_GetNumAvailableBytes_Func ) ( void *object ) ;
typedef const unsigned char * ( *Mf_GetPointerToCurrentPos_Func ) ( void *object ) ;
typedef UInt32 ( *Mf_GetMatches_Func ) ( void *object , UInt32 *distances ) ;
typedef void ( *Mf_Skip_Func ) ( void *object , UInt32 ) ;
//PPC Version : V2.1.9.30
//PPL Source File Name : \tavor\Arbel\obj_PMD2NONE\prepass_results\LzmaDec.ppp
//PPL Source File Name : \\pcac\\lzma\\src\\LzmaDec.c
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef unsigned char uint8_t ;
typedef unsigned short uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned long long uint64_t ;
typedef int SRes ;
typedef int ptrdiff_t ;
typedef int Int32 ;
typedef unsigned int UInt32 ;
typedef UInt32 SizeT ;
typedef long long int Int64 ;
typedef unsigned long long int UInt64 ;
//PPC Version : V2.1.9.30
//PPL Source File Name : \tavor\Arbel\obj_PMD2NONE\prepass_results\LzmaLib.ppp
//PPL Source File Name : \\pcac\\lzma\\src\\LzmaLib.c
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef unsigned char uint8_t ;
typedef unsigned short uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned long long uint64_t ;
typedef int SRes ;
typedef int ptrdiff_t ;
typedef int Int32 ;
typedef unsigned int UInt32 ;
typedef UInt32 SizeT ;
typedef long long int Int64 ;
typedef unsigned long long int UInt64 ;
typedef void * CLzmaEncHandle ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
