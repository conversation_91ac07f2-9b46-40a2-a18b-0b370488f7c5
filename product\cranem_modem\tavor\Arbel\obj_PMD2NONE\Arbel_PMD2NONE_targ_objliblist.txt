..\obj_PMD2NONE\ps_init.o 
..\obj_PMD2NONE\ps_nvm.o 
..\obj_PMD2NONE\GKITick.o 
..\obj_PMD2NONE\tavor_packages.o 
..\obj_PMD2NONE\dsp_filters.o 
..\obj_PMD2NONE\usbmgrttpal.o 
..\obj_PMD2NONE\root.o 
..\obj_PMD2NONE\diagDB.o 
..\obj_PMD2NONE\os-osa.lib 
..\obj_PMD2NONE\hal-HAL.lib 
..\obj_PMD2NONE\CrossPlatformSW-CrossPlatformSW.lib 
..\obj_PMD2NONE\csw-PM.lib 
..\obj_PMD2NONE\softutil-softutil.lib 
..\obj_PMD2NONE\cust-cust.lib 
..\obj_PMD2NONE\nota-nota.lib 
..\obj_PMD2NONE\framework-framework.lib 
..\obj_PMD2NONE\pcac-pca_components.lib 
..\obj_PMD2NONE\aud_sw-Audio.lib 
..\obj_PMD2NONE\ws-ws.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\3g_ps-dps.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\genlib-fsm.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\ltel1a-LTEL1A.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\genlib-qmgr.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\genlib-min_max.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\CRD-CRD.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\drat-DRAT.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-rm.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-aam.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-dma.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-intc.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-timer.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-RTC.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-pmu.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-commpm.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-pm.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-aci.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hop-mmi_mat.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\hal-BT_hoststack.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\nota-sulog.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\l1wlan-l1wlan.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\softutil-csw_memory.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\softutil-lzop.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\softutil-TickManager.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\pcac-dial.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\pcac-ci_stub.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\volte-volte_components.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\ims-ims_singlesim_lteonly.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\agpstp-agpstp_components.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\diag-diag.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\pcaconfigs-lwipv4v6_C2M.lib 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\os-threadx.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\commond.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\dpd.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\aslte.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\nsab_d.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\utd.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\usbd.lib 
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\sac.lib 
\hal\gps\aboot\lib\libaboot_tiny.a 
\xf_device\libs\xf_KW19_device.lib 
\aud_sw\lib\libcpaudio.a 
\mini_fota\libs\mini_fota.lib 
\framework\build\crane_evb\libhal.a 
\tavor\Arbel\CRANE_SDK_LIB\DM_THIN_SINGLE_SIM_LTEONLY\armps.lib 
