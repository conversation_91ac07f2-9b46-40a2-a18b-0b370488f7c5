/******************************************************************************
 * * mmi_modem_adaptor_nw.h - data structure for mmi-modem adpator, network sub-module
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#ifndef MMI_MODEM_ADAPTOR_NW_H
#define MMI_MODEM_ADAPTOR_NW_H

#ifdef __cplusplus
extern "C" {
#endif

#include <string.h>
#include "mmi_modem_adaptor_main.h"
/******************************************************************************
 *
 *         Message structure of MMI -> Modem Adaptor
 *
 *******************************************************************************/

// msg stru: MMI_MODEMADP_POWER_UP_REQ
typedef struct {
    MMI_MODEM_SIM_ID SimId;
} MMI_Modemadp_Power_Up_Req_t;

// msg stru: MMI_MODEMADP_POWER_OFF_REQ
typedef struct {
    MMI_MODEM_SIM_ID         SimId;
    MMI_MODEM_POWER_OFF_TYPE PowerOffType;
} MMI_Modemadp_Power_Off_Req_t;

// msg stru: MMI_MODEMADP_PLMN_SEARCH_REQ
// typedef struct {
// MMI_MODEM_SIM_ID           simId;
// MMI_MODEM_PLMN_SEARCH_TYPE SearchType;
// MMI_Modem_PLMN_Info        SpecPlmn;
// } MMI_Modem_Plmn_Search_Req_t;

// msg stru: MMI_MODEMADP_PLMN_SEARCH_STOP_REQ
typedef struct {
    MMI_MODEM_SIM_ID SimId;
} MMI_Modemadp_Plmn_Search_Stop_Req_t;

// msg stru: MMI_MODEMADP_GET_CALI_INFO_REQ
typedef struct {
    MMI_MODEM_SIM_ID SimId;
} MMI_Modemadp_Get_Cali_Info_Req_t;

// msg stru: MMI_MODEMADP_GET_SN_INFO_REQ
typedef struct {
    MMI_MODEM_SIM_ID SimId;
} MMI_Modemadp_Get_Sn_Info_Req_t;

/******************************************************************************
 *
 *         Message structure of RIL -> Modem Adaptor
 *
 *******************************************************************************/
// msg stru: RIL_MODEMADP_RIL_READY_IND
typedef struct {
    UINT8 Spare;
} RIL_Modemadp_Ril_Ready_Ind_t;

// msg stru: RIL_MODEMADP_MM_RSP_NW_STATE_CHANGED
typedef struct {
    RIL_SIM_ID SimId;
} RIL_Modemadp_Nw_State_Changed_t;

// msg stru: RIL_MODEMADP_MM_RSP_SIG_STRENGTH
typedef struct {
    RIL_SIM_ID SimId;
    UINT8 SignalStrength;
    RIL_GW_SignalStrength     GW_SignalStrength;
    RIL_LTE_SignalStrength_v8 LTE_SignalStrength;
} RIL_Modemadp_Sig_Strength_t;

// msg stru: RIL_MODEMADP_MM_RSP_NITZ_TIME_RECEIVED
typedef struct {
    RIL_SIM_ID SimId;
    INT8       NitzTimeData[RIL_MM_MAX_NITZ_TIME_LENGTH];
} RIL_Modemadp_Nitz_Time_t;

// msg stru: RIL_MODEMADP_MM_RSP_QUERY_AVAILABLE_NETWORKS
typedef struct {
    RIL_SIM_ID   SimId;
    RIL_OperInfo OperInfo;
    RIL_Errno    result;
} RIL_Modemadp_Query_Available_Nw_Cnf_t;

// msg stru: RIL_MODEMADP_MM_RSP_SET_NW_SEL_MANUAL
typedef struct {
    RIL_SIM_ID SimId;
    RIL_Errno  Result;
} RIL_Modemadp_Set_Nw_Sel_Manual_Cnf_t;

// msg stru: RIL_MODEMADP_MM_RSP_RAT_CHANGED
typedef struct {
    RIL_SIM_ID   SimId;
    RIL_MM_Reg_info RegInfo;
} RIL_Modemadp_Rat_Changed_t;

// msg stru: RIL_MODEMADP_MM_RSP_OPERATOR
typedef struct {
    RIL_SIM_ID   SimId;
    RIL_Errno result;
    RIL_OperInfo *pOperInfo;
} RIL_Modemadp_Rsp_Operator_t;

// msg stru: RIL_MODEMADP_MM_RSP_IMS_REG_STATE
typedef struct {
    RIL_SIM_ID   SimId;
    UINT8 ImsRegState;
} RIL_Modemadp_Ims_Reg_State_t;

// msg stru: RIL_MODEMADP_MM_RSP_RADIO_POWER_STATUS
typedef struct {
    RIL_SIM_ID SimId;
    UINT8      Status;
} RIL_Modemadp_Radio_Power_Status_t;

// msg stru: RIL_MODEMADP_MM_RSP_GET_BAND
typedef struct {
    RIL_SIM_ID   SimId;
    unsigned char ucMode;
    unsigned char ucGsmBand;
    unsigned short usLteBandH;
    unsigned long ulLteBandL;
} RIL_Modemadp_Rsp_Get_Band_t;

// msg stru: RIL_MODEMADP_MM_IMS_VOPS
typedef struct {
    RIL_SIM_ID   SimId;
    int volte;
} RIL_Modemadp_Ims_Vops_Ind_t;

// msg stru: RIL_MODEMADP_MM_RSP_CALI_INFO
typedef struct {
    RIL_SIM_ID   SimId;
    RIL_Errno    result;
} RIL_Modemadp_Rsp_Cali_Info_t;

// msg stru: RIL_MODEMADP_MM_RSP_GET_SN
typedef struct {
    RIL_SIM_ID   SimId;
    RIL_Errno    result;
    INT8         *pSn;
} RIL_Modemadp_Rsp_Get_Sn_t;

// msg stru: RIL_MODEMADP_MM_RSP_BANDIND
typedef struct {
    RIL_SIM_ID SimId;
    char       *BandindString;
} RIL_Modemadp_Rsp_Bandind_t;

#if ECARD_SAVE_TO_FACTORY != 0
typedef struct {
    MMI_MODEM_SIM_ID            SimId;
    INT8                        *Number;
} MMI_Modemadp_Set_Ecard_Info_Req_t;

typedef struct {
    RIL_SIM_ID   SimId;
    RIL_Errno    result;
} RIL_Modemadp_Rsp_Set_Ecard_t;

typedef struct {
    RIL_SIM_ID   SimId;
    RIL_Errno    result;
	INT8         *pCid;
} RIL_Modemadp_Rsp_Get_Ecard_t;

typedef struct {
    MMI_MODEM_SIM_ID            SimId;
} MMI_Modemadp_Get_Ecard_Info_Req_t;

typedef struct {
    RIL_SIM_ID   SimId;
    RIL_Errno    result;
} RIL_Modemadp_Rsp_Del_Ecard_t;

typedef struct {
    MMI_MODEM_SIM_ID            SimId;
} MMI_Modemadp_Del_Ecard_Info_Req_t;

#endif 

/******************************************************************************
 *
 *         network global management structure
 *
 *******************************************************************************/
#define MODEM_NW_SIGNAL_LEVEL_0     5
#define MODEM_NW_SIGNAL_LEVEL_1     9
#define MODEM_NW_SIGNAL_LEVEL_2     13
#define MODEM_NW_SIGNAL_LEVEL_3     17
#define MODEM_NW_SIGNAL_LEVEL_4     21
#define MODEM_NW_SIGNAL_LEVEL_FULL  25

typedef enum {
    MODEM_NW_STATE_NULL = 0,            // state after power on
    MODEM_NW_STATE_IDLE,                // after ril ready
    MODEM_NW_STATE_PLMN_SEARHING        // PLMN searching
} MODEM_NW_STATE;

typedef struct {
    MMI_MODEM_PLMN_STAT PlmnState;      // state of plmn
    INT8                *PlmnName;      // "UNKNOW" for plmn name not present.
    MMI_MODEM_PLMN_RAT  PlmnRat;        // Rat of current plmn
} Modem_NW_Plmn_Info_t;

typedef enum {
    MODEM_NW_CS_NOT_REG = 0,            // CS not registered
    MODEM_NW_CS_REG_HOME,               // Registered on home plmn
    MODEM_NW_CS_REG_HOME_SMS_ONLY,      // Registered on home plmn, SMS only
    MODEM_NW_CS_REG_ROAMING,            // Roaming
    MODEM_NW_CS_REG_ROAMING_SMS_ONLY,   // Roaming, SMS only
    MODEM_NW_CS_LIMITED                 // emergency call only
} MODEM_NW_CS_REG_STATE;

typedef enum {
    MODEM_NW_MSG_NULL = 0,
    MODEM_NW_POWER_UP_REQ = 0x01,
} MODEM_NW_PENDING_MSG;

typedef enum
{
    MODEM_NW_APP_USE = 0,
    MODEM_NW_ENGINEER_MODE_USE
} MODEM_NW_CELL_INFO_SRC_TYPE;

typedef enum
{
    MODEM_NW_LTE_MODE_UNKOWN = 0,
    MODEM_NW_LTE_ONLY,
    MODEM_NW_NOT_LTE_ONLY
} MODEM_NW_LTE_SUPPORT_TYPE;

typedef enum
{
    MODEM_NW_IMS_VOPS_NULL = 0,
    MODEM_NW_IMS_VOPS_OFF,
    MODEM_NW_IMS_VOPS_ON
} MODEM_NW_IMS_VOPS_STATE_TYPE;

typedef enum
{
    MODEM_NW_IMS_VOPS_NOT_REPORTED = 0,
    MODEM_NW_IMS_VOPS_HAS_BEEN_REPORTED
} MODEM_NW_IMS_VOPS_REP_TYPE;

typedef struct
{
    app_adaptor_lte_cell_info_ind lte_cellinfoind;
    app_adaptor_gsm_cell_info_ind gsm_cellinfoind;
    app_adp_lte_cells_t *ltecell_list;
    app_adp_gsm_cells_t *gsmcell_list;
    MMI_Modem_Lte_Cells_t  *ltecells;
    MMI_Modem_Gsm_Cells_t  *gsmcells;
    MODEM_NW_CELL_INFO_SRC_TYPE srctype;
} MODEM_NW_GET_CELL_INFO;

typedef struct {
    unsigned char ucMode;  ///see also: RIL_PreferredNetworkType
    unsigned char ucGsmBand;
    unsigned short usLteBandH;
    unsigned long ulLteBandL;
} MODEM_NW_GET_BAND_INFO;

typedef struct {
    MODEM_NW_STATE        ModemState;       // state of modem network sub-module
    MMI_MODEM_SIGNAL_BAR  SignalBar;
    Modem_NW_Plmn_Info_t  PlmnInfo;         // current plmn information
    MODEM_NW_CS_REG_STATE CsRegState;       // CS register status
    MODEM_NW_PENDING_MSG  PendingMsg;       // Pending messages
    MMI_MODEM_SIM_ID      PlmnSearchingSim; // SimId which is in plmn searching state
    UI_Inter_Msg_t        *SuspendPowerOff;  // power off command before ril ready
    MODEM_NW_GET_CELL_INFO  GetCellInfo;
    UINT8                 Ccfc_Ccwa_ReadFlg;// 0: unreaded 1: readed
    UINT8                 CcwaOnOffFlg;     // 0: off 1: on
    UINT8                 ImsRegState;      // 0: not registered 1:registered
    UINT8                 GsmRegState;      // 0: GPRS not registered 1:GPRS registered
    UINT8                 UmtsRegState;     // 0: WCDMA registered 1:TD-SCDMA registered 2:HSPA registered
    UINT8                 VolteState;       // 0: off 1:on
    UINT8                 IMSVopsState;     // 0: null 1:off 2:on
    UINT8                 LteOnlyFlg;       // 0: lte unkown 1:lte only 2:not lte only
    UINT8                 ImsVopsRepFlg;    // 0: not report 1:has been reported
    app_adp_radio_power_cb RadioPowerCb;
    UINT8                 RoamingState;     // 0: not roaming, 1: roaming
    UINT8                 CaliRslt;         // calibration result
    UINT8                 CaliRspCnt;       // response's count of calibration request
    UINT8                 CaliRptFlg;       // report calibration result
    UINT8                 ConfApnOnPowerup; // config APN on powerup
    MMI_Modem_Apn_Info_t  ApnInfo;          // APN info
    MMI_Modem_Call_Forward_Info_t NoConditionDivert;
    MMI_Modem_Call_Forward_Info_t BusyDivert;
    MMI_Modem_Call_Forward_Info_t NotReachDivert;
    MMI_Modem_Call_Forward_Info_t NoAnswerDivert;
	MODEM_NW_GET_BAND_INFO GetBandInfo;
    char                   *devSN;
} Modem_NW_Mng_t;

typedef struct {
    BOOL WifiStart;
    app_adp_wifi_scan_cb WifiResultInd;
    app_adp_wifi_ap_list WifiApList;
    UINT32 TimeoutSeconds;
    void* TimeoutId;
} Modem_NW_Wifi_t;

extern Modem_NW_Mng_t g_ModemNwMng[2];

/******************************************************************************
 *
 *         extern functions
 *
 *******************************************************************************/
extern VOID MMI_ModemAdap_Nw_Init_Req(void);
extern VOID MMI_ModemAdap_Call_Init_Req(void);
extern VOID MMI_ModemAdap_Sms_Init_Req(void);
extern VOID MMI_ModemAdap_Ss_Init_Req(void);
extern VOID MMI_ModemAdap_Sim_Init_Req(void);

extern MMI_MODEM_SIM_ID  MMI_ModemAdp_Get_Sim_in_Plmn_Searching(VOID);
extern RIL_SIM_ID  MMI_ModemAdp_Get_Ril_SimId(MMI_MODEM_SIM_ID UiSimId);
extern MMI_MODEM_SIM_ID  MMI_ModemAdp_Get_UI_SimId(RIL_SIM_ID RilSimId);

// extern VOID MMI_ModemAdap_Ue_Init_Req();

extern INT32 CM_SetConnectionSwitch(BOOL onOff);
extern BOOL CM_GetConnectionSwitch(void);
extern void CM_SetDataOnRoamingPersist(int dataOn);
extern int CM_GetDataOnRoaming(void);
extern int CM_GetDefaultApnName(char *name, int name_len);
extern int CM_SetupDefaultConnection(void);
extern INT32 CM_ConfigApnInfo(ApnInfo_s *info);
extern INT32 CM_SetupDataCall(UINT8 cid);
extern INT32 CM_DeactiveDataCallAll(void);
extern int get_auto_apn_iptype(int simID);
extern void setAutoApn(UINT8 mode, BOOL persist);

#define MMI_ModemAdp_printf    printf

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*SETTING_CALL_H*/
